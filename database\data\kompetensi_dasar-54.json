[{"kompetensi_dasar_id": "507f408e-03a2-4475-aeff-105c195fe4b4", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkanstrategi pemasaran tanaman pangan dan palawija", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:04", "updated_at": "2019-06-15 15:07:04", "deleted_at": null, "last_sync": "2019-06-15 15:07:04"}, {"kompetensi_dasar_id": "5082288f-f936-457d-aae4-419b29db2330", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 843100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mementaskan  teknik  gerak tari putri berdasarkan pola irama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:29", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "5082fbdd-a720-4c0d-8526-d1503e03761e", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> <PERSON><PERSON> kehid<PERSON>an sehari-hari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "50832e8d-fb9e-4dc8-80fb-5f57927224c3", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan rias dan busana tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "50840a66-af0d-4e49-9780-260c767ca343", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:02"}, {"kompetensi_dasar_id": "5085cac1-7cf0-4651-8fea-020781186bfb", "id_kompetensi": "Proses bisnis menyeluruh bidang teknika kapal penang<PERSON>p ikan ", "kompetensi_id": 3, "mata_pelajaran_id": 800000133, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik dapat memahami proses bisnis teknika kapal penangkap ikan sebagai bagian integral dari bidang pelayaran perikanan, antara lain tentang penerapan prosedur darurat dan K3LH, persy<PERSON><PERSON> kerja di kapal, k<PERSON><PERSON> kerja, buku pelaut, se<PERSON><PERSON><PERSON><PERSON>, hukum maritim dan hukum perikanan, penang<PERSON>pan dan penanganan pasca penangkapan ikan. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:22", "updated_at": "2022-11-10 19:56:58", "deleted_at": null, "last_sync": "2022-11-10 19:56:58"}, {"kompetensi_dasar_id": "50863985-dae4-4453-a6c8-8ccff7b5653d", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 820140200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem penerangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "50868e4c-869e-42e1-9712-8a52adae7a72", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik tenun kepar dengan ATBM", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "50878552-28d1-4a5d-b64c-fbc655b1ab13", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:03", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:00:03"}, {"kompetensi_dasar_id": "50882f0d-5a0a-4b31-9dad-cc6328ab680b", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 843062400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikan repertoar kategori lan<PERSON>t g<PERSON>/lagu pada sajian karawitan iringan berbagai irama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "508a3e30-5d5c-4485-a4b2-f82cdd49ada2", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804101400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perawatan peralatan proteksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:45", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "508af797-532e-424c-8392-afd9f9cc74a3", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 830050040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan tangan dan rias kuku secara manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "508af958-9b63-4816-b92d-4b33a0681ed4", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi ajaran Nabi Kongzi tentang Jalan Suci Bumi (Di Dao)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:49", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:06:05"}, {"kompetensi_dasar_id": "508b108d-944c-4ad0-b7db-d8541603832d", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 817130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses blending linier dan tidak linier", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "508b2bd3-a02f-43a3-a41e-46e12d570b50", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 825030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON> jenis dan karakteristik serta aplikasi pupuk dasar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "508bc7bc-1532-4dc5-a7df-37848cae997f", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 802010500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan aplikasi interaktif pada web server", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "508c2a30-2778-47db-815b-b397c884682f", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825250300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola paludarium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "508c44da-4619-467c-beed-b02f84080d76", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan puja terkait dengan budaya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "508f3c0f-6c15-4422-ac8e-ce2012309549", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gambar konstruksi bangunan air dan bangunan pertanian dan komponen alat mesin pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:46", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:02:46"}, {"kompetensi_dasar_id": "508f4fa4-9a99-4d52-8614-eb7651026c6a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 801030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan adiksi korban NAPZA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "5092f87d-8900-4dbd-a5b7-ba76f134a8f4", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan berbagai peraturan perundangan serta konsekuensi hukum bagi para pengguna dan pengedar NARKOBA dan psikotropika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:15"}, {"kompetensi_dasar_id": "50937d4d-3e95-4793-925f-ca2711472a55", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip pengukuran dengan sekstan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "509439bd-bc84-4a93-9f7c-5f694daeb902", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami cara menyebutkan arah, letak suatu tempat dan benda (위치와 방향, 장소,) sesuai dengan konteks penggunaannya, dengan memperhatikan fungsi social, struktur teks, dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:31"}, {"kompetensi_dasar_id": "50956948-369d-4594-bcda-bcc5c22e6436", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:12", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:12"}, {"kompetensi_dasar_id": "50964ab5-2f3c-4daf-a827-52bf9e165984", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:29"}, {"kompetensi_dasar_id": "50978e23-9ff0-45bb-bce6-0b531b264095", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan titrasi pen<PERSON> (argentometri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:44"}, {"kompetensi_dasar_id": "5097e92d-9460-4cb4-8b0b-b2ebc6b5f773", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "5098bfb0-b102-4f75-9a03-b90a6e0e3a1e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsi<PERSON>, kese<PERSON><PERSON> kerja dan lingkungan hidup pada tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "50993851-9213-4ed2-b710-2f32dfec19b1", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 800050430, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan prosedur memandikan klien", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "5099b603-350b-4fd9-84bb-4ee795e441cf", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 830130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan wajah dehidrasi dengan teknologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "509ac81c-1202-427c-a777-15b1f4019fc0", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:56"}, {"kompetensi_dasar_id": "509b270e-15fc-4603-9426-356048450734", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik inkubasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "509c3bf0-6613-4bda-b203-59e7385cd4a2", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 821120300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis berdasarkan pengamatan terkait prinsip kerja heat exchanger", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "509cfce9-d1a3-4109-8147-6d5d3d570d55", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Memahami room section", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "509d0105-e2c0-4cd2-be97-c033225773a6", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 820140200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan  sistem starter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "509dc37c-2534-453f-a4e0-061adb675028", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "509eebe3-eaa0-48f5-b6b4-64bce3a4c233", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 825020300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengan<PERSON><PERSON> pemeli<PERSON>an kesuburan tanah tanaman buah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "509f48a5-6e03-4acf-b152-90937916c807", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 840040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan masalah pengglasiran benda keramik  dan cara mengatasinya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "509fd01a-2658-4da9-a22e-aa663dc5dd05", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800061000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis anatomi fisiologi umum dan gigi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "50a0bad7-dc9d-45e9-9bc9-e18de36cae6e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "50a11d3e-614e-4549-9d94-82133f96ead5", "id_kompetensi": "3.45", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip penggunaan batu gerinda untuk penggerindaan silinder dalam dan selinder luar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "50a13aa0-4ac7-469c-85a9-910337711ef9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:59"}, {"kompetensi_dasar_id": "50a1b056-ca30-4f61-8a44-66988eb223e8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 802032300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik editing gambar (foto)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "50a1ee9b-c051-4d8e-b7e6-3a0265cb5a4d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persiapan pengoperasian turbin gas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "50a4cfff-3fb2-4844-9a0b-51054cbb3f89", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:14", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:07:14"}, {"kompetensi_dasar_id": "50a5bde1-1cfe-45a2-bc74-889f5b2ca3b5", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803071500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan jenis-jenis konektor saluran transmisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "50a88f9e-9cdd-4ce5-9c33-e44bddefff70", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 807021910, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Meminimalis terjadinya kes<PERSON>han dalam proses pembentukan sebuah komponen pesawat udara dengan cara mekanik/mesin pembentuk nibler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "50a916aa-1b50-4042-8fb0-2c0cac36e5f7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi listrik dengan k<PERSON>, cable ladder dan cable tray/trunking.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "50a91911-6958-4244-bfb6-ce9fe071f123", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 828110100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan komunikasi bisnis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "50a93c46-e48f-465a-b113-5c5236d321d3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON>ami  alat bantu untuk \r\nmenghasilkan komponen  sesuai\r\ngambar kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "50a9fac2-bf9d-4f41-a0dd-dbf88b6560c5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "50aa5cc1-565d-4ced-a6da-8f2a8068c3c9", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukanperhitunganberbagai proses berda<PERSON><PERSON><PERSON><PERSON>ter<PERSON>dinami<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:08"}, {"kompetensi_dasar_id": "50ab6eed-c6a8-4f5e-9471-97aea5bdba96", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843062400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ragam motif tabuhan dasar instrumen karawitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "50abab61-481e-414f-98ad-8b90b534bba0", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 814032000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melaksanakan pengawasan uji petik secara langsung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "50acd936-ac3b-4035-b1ae-b92bac45c2ea", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 804101200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemeliharaan system pendingin pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "50ad11e2-c43e-4e61-95df-b7db569c9fc7", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami makna keterlibatan aktif  umat Katolik dalam membangun bangsa dan negara Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "50ae7b86-87e8-40a1-a069-6d21516d4fc0", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "50b0dae5-7f98-4ac5-b380-3c57e5a141fd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sterilisasi dan uji sterilitas.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "50b1422f-c0c8-4935-9c46-bbbfbf6ae2e4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> dasar-dasar manajemen bidang konstruksi pada tugas membuat gambar kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:17", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:07:17"}, {"kompetensi_dasar_id": "50b197c2-b30c-4182-9fb0-4e78b4222a98", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802031900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat key-pose dan inbetweend dengan lebih dari dua karakter, be<PERSON><PERSON>  dengan properti", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "50b23857-f661-46bd-9934-71413ee52709", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "50b2614a-9611-4609-924a-d7a8e08046ec", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:05:13"}, {"kompetensi_dasar_id": "50b34cf0-3367-457e-bdf0-c33dc0c8a3f1", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 809020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses cetak warna proses pada pekerjaan multiwarna sesuai pesanan cetak dengan teknik cetak rotogravure/ intaglio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "50b42fd8-1078-40d9-9ba5-73a010e8f527", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> p<PERSON> pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:17"}, {"kompetensi_dasar_id": "50b4750a-2c98-4ae4-b11f-fcdf1af45dab", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 821060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar dinding partisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "50b7be69-164d-4156-af6e-fe12d9d2685b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827391000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain principles  maintenance", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "50b8ca20-4f7e-461e-a9a7-901b443d9ccb", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan keselamatan \r\nkerja pada pek<PERSON><PERSON><PERSON>, \r\nbaju pelindung dan kaca mata \r\npengaman", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:07", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:07"}, {"kompetensi_dasar_id": "50b966d0-1238-4019-846f-a879a1340be9", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemesinan gerinda silinder", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:43", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:08:43"}, {"kompetensi_dasar_id": "50bcbce1-7dfc-4fcc-872f-ef3ae71c04d3", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825240310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip HACCP produk mollusca, crustacea dan pengalengan standar ekspor dan olahannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "50bd32a3-1b06-439f-8a9c-50bbd070c171", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 801031000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memberikan  kegiatan untuk mengisi waktu luang pada lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "50bd5b7c-323d-46cc-9074-3bf88df619fc", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 827290300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengemasan dan pengiriman bibit rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "50bd67ab-97f0-49e7-a0e7-65708eb9cc19", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 822190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis parameter kinerja PLTH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "50bd8c63-2880-419d-b4d4-88f2bfeb073d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>r nilai-nilai demokrasi pada konteks lokal dan global mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:10"}, {"kompetensi_dasar_id": "50bdc82a-f022-4663-aa21-92e519d29ad8", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:58", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:02:58"}, {"kompetensi_dasar_id": "50bf282a-814d-4cd7-86e2-94c2872c62d2", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 800070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan olah gerak tubuh untuk kebugaran tubuh", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-10-19 23:19:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "50c005e6-1ea3-4926-82a6-28790c718166", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:40"}, {"kompetensi_dasar_id": "50c02b72-8acb-4bbf-b661-e1825d222a33", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:17", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:17"}, {"kompetensi_dasar_id": "50c0e70b-8cdf-492b-bcf5-5d779a163726", "id_kompetensi": "2.3.3.a ", "kompetensi_id": 1, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "memahami rancangan pergelaran musik", "kompetensi_dasar_alias": "", "user_id": "881946e1-fcde-4c87-8e1c-ed5c309853ac", "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:32:02", "updated_at": "2019-06-15 15:32:02", "deleted_at": null, "last_sync": "2019-06-15 15:32:02"}, {"kompetensi_dasar_id": "50c1e4e2-9449-4a84-8c1a-135ba05fa295", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 843120400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji data pola kerja tata cahaya dalam produksi teater", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "50c3263b-e38a-4fbd-a9e9-2b27d684087e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401251620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menganalisis manajemen bisnis ritel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "50c40673-2ce5-40a1-85b3-e604de8a1e9b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 875190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis karakteristik generator AC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:39", "updated_at": "2019-11-27 00:29:39", "deleted_at": null, "last_sync": "2019-11-27 00:29:39"}, {"kompetensi_dasar_id": "50c42561-0cbf-4d59-a217-dba2bb2de2e1", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:30", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:07:30"}, {"kompetensi_dasar_id": "50c426e5-959c-494f-9e76-eb2fcfcc0d6e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengapresiasi periodisasi tari di Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "50c4420c-fc71-499d-a551-771519667629", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 843061100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi repertoar dasar karawitan pada berbagai irama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "50c44409-b495-49d3-9d79-b53bc6c0c33a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:26:53"}, {"kompetensi_dasar_id": "50c69aa5-8ee1-4acb-b6f2-94298402ca1f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802011000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahamidokumen proposal kerja proyek yang telah disetujui oleh pembimbing yang berisi : tuju<PERSON> kerja proyek, tim dalam kerja proyek beserta tugas dan tangg<PERSON>, metode kerja proyek, organisasi kerja proyek, re<PERSON><PERSON> kerja proyek, analisis dan desain, implementasi kerja proyek.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "50c7352c-02ac-4cdc-b7a3-77ca84d6974a", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 814030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil peta kendali baik peta kendali variabel atau atribut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "50c78d8c-86f1-4f40-b534-c5b4cd7b6b11", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 809010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kebutuhan sumber daya manusia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "50c898ea-bf89-464f-af6f-e10a82f24a8f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk mengucapkan dan merespon ungkapan meminta per<PERSON> (extended), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:14"}, {"kompetensi_dasar_id": "50c8ae74-1a36-446f-bdd4-0a6abe163492", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 814080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca data pengambilan barang berdasarkan daftar distribusi harian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "50c8bf01-fdae-4ece-abff-09821be01933", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 817060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> metode produksi migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "50c993d4-256e-46f2-b156-976abf23c9b0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "50c9bbb1-01de-4a1f-bab5-d6df9e47e82a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827351100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Analyze the magnetism of the earth and the ship’s deviation (menganalisis magnet bumi untuk mencari deviasi kapal )", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "50cbdd60-e1dd-455a-8d35-902a3d564e1b", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menenerapkan rangkaian gerbang logika sequensial pada sistem register geser (shift register) digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "50cd7eaa-8e92-4121-bee8-36d00bd4b2e0", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 821140500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menampilkan gambar proyeksi orthogonal kuadran I dan kuadran III (2D)", "kompetensi_dasar_alias": "", "user_id": "ac8d5b4e-9eeb-4f73-967e-7888d5881f2d", "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:06", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:30:06"}, {"kompetensi_dasar_id": "50cdc5c7-1261-47e4-89c7-6bb375d2832d", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 822050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan peralatan kerja dan komponen yang digunakan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "50cdff1d-b47c-4387-ab7a-4e32684ec03b", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 800020410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pemeriksaan penyakit sistem indera berdasarkan manifestasi klinis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "50ce22d6-33b3-48fb-ab46-91a7d497204b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala poros propeler", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2019-11-27 00:27:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "50d10e31-61cd-4321-b5a1-6145c3f542f3", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 831010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> bahan peleng<PERSON>p", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "50d2420c-8f3d-402b-9540-81ad1b8214e2", "id_kompetensi": "3.31", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi desain inverter untuk PLTS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "50d281c9-06f6-4588-a797-d9ff6613ce37", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825230210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerap<PERSON> pen<PERSON> susu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "50d2a874-1351-47bb-8d7a-7f6e1c13c8a0", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> p<PERSON> pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:35"}, {"kompetensi_dasar_id": "50d49386-c5d0-412e-9d5e-5ffd97e1ee98", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamatidrill stem dan handling tools", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "50d56c3b-7ac1-44e7-a01c-9a05af7c6b9c", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 803070800, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses debugging pemrograman mik<PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "50d5d23c-b537-4144-be33-12bbe9b47d13", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804130600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit pelat pola polisterin sesuai POS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "50d5e95f-1bfe-4296-91eb-630bf3c0b08f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401251102, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan kartu aset tetap dan pen<PERSON>ian saldonya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "50d70c7b-39d6-46e5-8ad9-0727697326c6", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 828190110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan kegiatan ekowisata yang berdampak negatif rendah terhadap lingkungan dan sosial budaya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:52", "updated_at": "2019-06-15 14:52:52", "deleted_at": null, "last_sync": "2019-06-15 14:52:52"}, {"kompetensi_dasar_id": "50d82c44-f103-461e-bc8c-5522f2c2b8dc", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan peralatan edting dan perangkat pendukung editing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "50d8b21d-7652-4c7a-820e-1087d83b5e3c", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 828170100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproses dokumen perjalanan udara Internasional (normal dan spesial).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:19", "updated_at": "2019-06-15 15:07:19", "deleted_at": null, "last_sync": "2019-06-15 15:07:19"}, {"kompetensi_dasar_id": "50d91da4-7970-4a9e-80e9-b2051bdd178a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dalam penggunaan peralatan dasar laboratorium    (alat-alat gelas dan non gelas)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:01"}, {"kompetensi_dasar_id": "50d969a0-29f1-43af-afc1-73b3959775c2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari surat lamaran kerja, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:11"}, {"kompetensi_dasar_id": "50d9f6a3-1a61-4813-8393-68f9a6b4f987", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:04"}, {"kompetensi_dasar_id": "50da4e98-4850-47f8-9316-e2c5c5f03358", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> gelombang suara dan sistem akustik ruang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "50da8ff5-069d-45c5-8b12-fb0f777787fe", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan berbagai peraturan perundangan serta konsekuensi hukum bagi para pengguna dan pengedar NARKOBA dan psikotropika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:07", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:07"}, {"kompetensi_dasar_id": "50dac91e-7d14-4cb8-bf38-005615940789", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencatatan transaksi pengeluaran kas untuk pem<PERSON>ian bahan, membayar biaya tenaga kerja langsung, biaya overhead pabrik, biaya administrasi umum dan pema<PERSON>, melu<PERSON><PERSON> utang dagang, dan utang lainnya ke dalam buku jurnal khusus.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "50dae580-832c-4ba8-add6-5807859a7502", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisisis  penanganan telur ikan hias", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "50dc02db-4883-417f-a8a4-2a8c85f25594", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 822060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan penggunaan jacking, blocking & lifting", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "50dc97dc-d5f9-4c62-992c-de2df1caec7f", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:54"}, {"kompetensi_dasar_id": "50dd4360-0be4-4faa-a5d2-b025d0a8c6e7", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:28"}, {"kompetensi_dasar_id": "50df8761-30fb-405a-a1ac-e20b53175976", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:29", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:28:29"}, {"kompetensi_dasar_id": "50dfcb06-7abf-4dc8-be5b-28a21f2e50e4", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 830040100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan rias wajah malam hari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "50e0062a-29e2-4d7a-8aa4-53acfed1782a", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:13", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:13"}, {"kompetensi_dasar_id": "50e13dd3-8ad6-4b82-be94-7aecffe42a01", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 830050400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan Acrylic Nail Extension", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "50e2da55-1b6e-4826-97c0-1acf7a4787fa", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 843090100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan simpingan wayang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "50e2feb5-7203-4529-b91e-93a3bf1d3b8e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825020610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan persiapan lahan produksi tanaman perkebunan semusim pen<PERSON> serat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "50e3050c-18a1-4293-aa63-08face201eb7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan bahan baku dan bahan penunjang dalam suatu industri kimia berdasarkan azaz sto<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:02"}, {"kompetensi_dasar_id": "50e3d913-873c-4f03-beb4-4ba036cdb20f", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 824060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>  renc<PERSON>ui<PERSON> / Game", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2019-06-15 14:58:10", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "50e3f64b-c7ff-4847-87d6-d2cf3dcdc6d7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan klasifikasi generator-generator berdasarkan fungsi dan karakteristik operasionalnya pada  generator-set pusat pembangkitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "50e40648-0660-4434-a9fd-f5984e4ec5b2", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:49", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:49"}, {"kompetensi_dasar_id": "50e500fc-fe99-4e83-82cc-4f46e3bccb33", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 830050200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan praktik pelayanan salon SPA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "50e575d6-c349-4919-88c3-26fe6335c974", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:56"}, {"kompetensi_dasar_id": "50e5a215-f9e8-4d68-94d2-e3fe58ed7452", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:23", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:23"}, {"kompetensi_dasar_id": "50e64bc7-3ce7-40fa-9fa3-e15415d3c3e6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802040800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami bisnis siaran radio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "50e70ce6-adef-4c0a-8553-a726abb4eacf", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:28", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:28"}, {"kompetensi_dasar_id": "50e827cc-52f8-4101-b66e-322aa6b83c53", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:55", "updated_at": "2022-10-19 23:19:18", "deleted_at": null, "last_sync": "2019-06-15 16:03:55"}, {"kompetensi_dasar_id": "50e98813-a159-42a1-91e9-e755bb490401", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mendemonstrasikan pengangkatan benda kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "50eb3171-9321-4350-93f3-9fcec3cfa02b", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 839110200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pembuatan desain produk kulit non alas kaki dan non busana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "50eb3f3f-843c-42da-8fae-fe900c88c4ab", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401131800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prosedur pemba<PERSON>n bulu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "50eb4027-f2cd-4705-9ff8-1068eb5d00c8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan  kondisi operasi saklar manual (SPST, SPDT, DPST, DPDT, TPST, TPDT, DRUM SWITCH)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "50eb4084-4417-4dcd-93f2-df4f4f7b2d86", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804160800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perintah dalam perangkat lunak CAM Lathe 3D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "50ec63e1-ebfe-4ff5-88f5-45d28b39deb7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827050110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply ask for and gives personal  data", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "50ecbc39-c14a-469a-9154-a7e04ceaba96", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 842040300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kayu secara alami", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "50ee6ab9-b622-406d-9727-86007dcb7f00", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 828200110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penataan event", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "50ee9452-9c1d-4c88-983a-df83421a5596", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843110200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data fungsi latihan adegan terkait teknik pemeranan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "50eebba5-ba6d-4a7a-aed4-57944715deee", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 800070200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkaitkan komponen anatomi dan fisiologi dalam sistem limfatik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "50f205d8-84b2-4156-9697-a9be2e8ed1c7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 821060900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan  dan menggambar konstruksi melintang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "50f23ea7-d291-4da5-8eb7-c113450eaca1", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802032700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar obyek 3 dimensi dengan perangkat lunak secara tepat dan efektif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:27", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:30:27"}, {"kompetensi_dasar_id": "50f2751e-bf19-4ebe-bdc8-57a665961569", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:08", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:08"}, {"kompetensi_dasar_id": "50f2ca42-8457-4b7c-b446-da78a4f92344", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan prinsip dasar  penggunaan kontrol kelembapan  dengan sistem mekanik dan elektrik serta hubungannya dengan interface dalam sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "50f39e66-7c83-404f-ae80-7d656051daa8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821060703, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> peralatan utama dan peralatan bantu kerja pipa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "50f40d3d-ee3c-4818-99a1-295f5f2afa44", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821090600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung ukuran utama roda gigi terkait dengan proses pemesinan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "50f97268-562b-4099-86d3-601590f06120", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:57"}, {"kompetensi_dasar_id": "50fa74b1-3b73-44e4-97cf-c786045807e7", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 843011100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan jenis dan karakter musik pada format ansambel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "50fb6ad9-d747-4c83-ab4f-ddd861eee4d9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 814100100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan proses pemintalan kering", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-10-18 06:43:59", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "50fc5dcb-8916-4be0-ad3c-a4e83939d4d3", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:11"}, {"kompetensi_dasar_id": "50fe1c32-455f-48ae-8c5e-23af56081efd", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 843100100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi teknik gerak tari putri berdasarkan pola irama", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "50fe1f2d-ecbf-4bda-8a61-22d460529b89", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemasangan sinkronisasi sistem tenga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "50ff945b-6963-4ce3-aeb6-74ef6a078c84", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825060200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan pakan ternak (ruminansia, unggas, aneka ternak) secara sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "50fff04a-7b2b-41f1-9e68-35028c0b9f26", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 807021920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeset peralatan las oksiasitelin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:47", "updated_at": "2019-11-27 00:29:47", "deleted_at": null, "last_sync": "2019-11-27 00:29:47"}, {"kompetensi_dasar_id": "51014142-c28e-4fa6-8e48-ad2a4345c38c", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 800070200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemeriksaan laboratorium berdasarkan penyakit sistem pernafasan (respiratori)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2019-06-15 14:59:28", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "510169aa-12a7-41d3-bb77-c018e6af5232", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemesinan bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan  teknik pemesinan bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:26:55"}, {"kompetensi_dasar_id": "5101ff44-f3be-4b84-9b72-a123a03672f5", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 827390800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate maintenance and repair boiler", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "5104b427-a87a-4731-8ec2-7e413c26a180", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan mesin gerinda datar/  survace grinding machine untuk berbagai jeni<PERSON> p<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "51053831-a90c-4d68-9ef2-96f62f088579", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi perkembangan kehidupan politik dan ekonomi bangsa Indonesia pada masa Demokrasi Terpimpin.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "5105d349-b547-4f13-b7a8-0569abf6f2ab", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Merawat peralatan jacking, blocking dan liffting sesuai operation manual", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "510622c9-04e4-42c1-b8fd-8711fedde03e", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan overhaul poros propeler", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2019-11-27 00:27:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "5109b83a-2c14-476b-baf1-7ccbd77d9d39", "id_kompetensi": "3.23", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penggunaan alat kromatografi gas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "510a774a-9a11-4aba-ac0c-301bea6fd6bd", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 814080100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penyimpanan barang sesuai  teknik tata letak penyimpanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "510abb25-f589-4331-ae31-b5f51175a095", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:41"}, {"kompetensi_dasar_id": "510bf170-1877-40cd-a530-c97e76c7b3ab", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengidentifika<PERSON> pengel<PERSON> sampel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "510c5cec-bc3f-41fa-9f75-0a77200e9bee", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821060120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa konstruksi senta (Web)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "510d3e8d-33b6-4b80-89c5-0e7101c255e0", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 814070200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses administrasi pengambilan barang berdasarkan daftar distribusi harian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "510e986e-1bf8-440d-bec2-9c74c53df5cb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahan baku dan bahan pembantu mengi<PERSON>ti stok<PERSON>, si<PERSON><PERSON> kimia fisika bahan dan intruksi kerja dari industri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:03", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:02:03"}, {"kompetensi_dasar_id": "510f30d0-6034-4480-a874-1b38c568553e", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 821060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara penggambaran pemasangan marine board kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "510fa089-c16f-435f-b700-357de48ef94e", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> a<PERSON>an <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "510fcdb4-0fa2-496d-a091-9ca817798eee", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:56"}, {"kompetensi_dasar_id": "5110796b-221e-4c40-a1f4-a3f1e019e57e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Q<PERSON><PERSON> (31): 13-14 dan Q.S<PERSON> (2): 83, serta hadits tentang saling menasihati dan berbuat baik (ihsan).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:03", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:03"}, {"kompetensi_dasar_id": "5110e773-ae1b-4ac8-9419-39b8d8b04dba", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827110110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> tipe, k<PERSON><PERSON><PERSON><PERSON>, prinsip kerja dan fungsi cargo winch", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:06", "updated_at": "2019-11-27 00:29:06", "deleted_at": null, "last_sync": "2019-11-27 00:29:06"}, {"kompetensi_dasar_id": "5110ffa1-2ad9-4070-8611-95c9d53c4bb4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 800020500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus dinamika masyarakat pada kelompok sosial, pranata sosial, dan mobilitas sosial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "5111e3f6-9ab0-40c0-9175-c6176f8c7444", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827050110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Understand identifiesandnames ofthe mainpartsofa passenger vessel Inspeechand writing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "51134510-56cf-4c39-8768-401b703e7397", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804100700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang Instalasi Penerangan dan Daya pada Gedung Kontrol Gardu Induk.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "5113b1d5-c486-48fe-9ad9-2597105455ee", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 817020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan pengujian parameter Cadangan migas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2019-11-27 00:27:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "5113de6d-8db2-4c5b-afe5-0503a9a02ca0", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804130500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang  aplikasi mikrokontroler pada sistem kendali", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "5114e727-30c9-4e7e-b06c-a509fe57ab43", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 801031200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is kebutuhan lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "5116ed81-494b-439e-826d-d7605a6e6146", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:05"}, {"kompetensi_dasar_id": "51170b45-8348-4bc3-be4f-6d339592b115", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "511797d7-be41-430c-8aa3-8927c7dd6dc1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 800061100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penyimpanan bahan dan obat ked<PERSON>n gigi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:27"}, {"kompetensi_dasar_id": "5117a7aa-ed68-4f62-89cf-31298fc5699d", "id_kompetensi": "Pembiakan tanaman ", "kompetensi_id": 3, "mata_pelajaran_id": 800000127, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> akhir fase <PERSON>, peserta didik dapat menjelaskan tentang pembiakan tanaman secara generatif dan vegetatif, baik konvensional maupun modern. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:21", "updated_at": "2022-11-10 19:56:58", "deleted_at": null, "last_sync": "2022-11-10 19:56:58"}, {"kompetensi_dasar_id": "5118c2d1-6528-47cc-b39b-19f3e8f4862e", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:42", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:42"}, {"kompetensi_dasar_id": "511900d3-3830-4612-af20-b9df1dfe96a2", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827310100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> aturan hukum kepelautan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "511a41a7-dbfa-493c-a1c6-ec1d4f7c0a7f", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 830010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> kesehatan kerja pada ruang, personal dan pelanggan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "511a9165-d29f-4dcb-bc90-ae7282579f86", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825130200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengal<PERSON>s gambar profil hasil pen<PERSON>n", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "511b0d56-4530-403b-8b9e-a675705de986", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 828150100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan tentang industri perhotelan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "511b3c35-dee6-4007-ae35-d85693174b1d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825210700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasangan sarana instalasi air, listrik dan sarana aerasi pada pendederan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2019-11-27 00:29:23", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "511bd539-9098-4357-80d9-41b4aebbbadd", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan komponen -komponen loop sitem pengendalian pada sistem otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "511c42c4-500a-4ff8-ba8c-7a85c761ba8e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami orientasi 2 buah foto udara agar stereoskopis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "511c4eb3-4ef2-42ec-b4f1-3cc1897c01de", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:06", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:06"}, {"kompetensi_dasar_id": "511d9741-c201-4854-8c94-7c67f18e1f83", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:41", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:41"}, {"kompetensi_dasar_id": "511da8f2-55e0-41f7-80ea-371a2e8c93f5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "511e241b-49f3-495c-866b-4e3bdc4c5094", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang penempatan antena WLL,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "511e461d-f26a-488f-9df4-b2b6e3a16bc3", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 828190120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan perangkat pemanduan perjalanan wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2019-06-15 14:58:27", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "511f37f5-0e07-4b14-b3bf-44d4bf1b8be1", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 821080300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil pengoperasian mesin gergaji jig (Jig Saw)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "511f8dd7-4490-4366-a077-bd9325e5b9cf", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 825050600, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik pendataan selama proses pemuliaan tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "51206ffd-8890-4f73-89a2-5d8f863a0ceb", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pem<PERSON>n kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:42"}, {"kompetensi_dasar_id": "51215424-5953-43de-85a3-8356ff09f414", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 830030100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan pemeriksaan anatomi dan fisiologi tubuh", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "512159e2-705c-4f5b-a93c-8cfbe9beb858", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827390600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain engine types", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "5121a663-5b82-48c1-8d01-51738f900c60", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 820050500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala Differential", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "5121d579-0908-4a83-94b0-2b6e51be2f27", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825021100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis kerusakan tanaman perkebunan akibat serangan hama", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "5124dc77-a3dc-4fad-975a-be404f91e1c0", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 825230100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Merancang kegiatan unit produksi pada produk baru nabati yang bernilai jual dan berdaya saing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "5124eaff-11e5-402b-ac8a-c11cf3769d2f", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 803071400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji parameter penentu Kualitas pesawat penerima", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "5125176b-7d8e-4ec6-8a3c-5ee0e8662b76", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 401141600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sediaan supositoria", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2019-11-27 00:30:23", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "51259848-462b-4d3a-9402-deca02a83c59", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 815010900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki hasil pengujian sliver mesin Roving", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "512698ad-08f3-4dff-97f2-61cbdebf7d65", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi yang berkembang di wilayah setempat dan lainnya sesuai teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:30", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:30"}, {"kompetensi_dasar_id": "51280ba4-ccb3-49f8-bc6a-03d3a9b26bc9", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, menyajikan model mate<PERSON>ika dan menye<PERSON><PERSON><PERSON> masalah keseharian yang berkaitan dengan barisan dan deret aritmetika, geometri dan yang la<PERSON>ya.", "kompetensi_dasar_alias": "<p><PERSON>y<span>elesaikan\r\nmasalah yang&nbsp;</span>berkaitan dengan\r\npenyajian\r\ndata hasil pengukuran\r\ndan\r\npencacahan dalam tabel distribusi frekuensi\r\ndan histogram</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:50", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:50"}, {"kompetensi_dasar_id": "512854bb-faa5-4d01-a64f-3b55d0224259", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 825030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pemupukan dasar taman bangunan gedung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "5128f0e1-67f5-47f9-b01c-1682423c0fa7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis polarimetri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "5129de2c-f68d-45a6-b617-faf1d5b8fb51", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:49", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:08:49"}, {"kompetensi_dasar_id": "512add03-8535-4b2e-92d8-57565c56a7e5", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 807020710, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis komponen hard/ soft ware program CAD", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:38", "updated_at": "2019-11-27 00:29:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:38"}, {"kompetensi_dasar_id": "512d9cae-3404-4e0c-9e08-50bb8816b934", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 809020100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses pengolahan limbah cair dengan teknik destruksi suhu tinggi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2019-11-27 00:27:35", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "512fe10e-7975-4fa9-87d6-a4f2f087535e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:46", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:46"}, {"kompetensi_dasar_id": "5130b575-50f6-4e1d-9c80-032dc2bc39b8", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:51"}, {"kompetensi_dasar_id": "51312d49-e932-4096-996b-ea80d5492dd1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:22", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:22"}, {"kompetensi_dasar_id": "51314a78-5fae-41a6-8ef6-9bb3462e8e69", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan perencanaan ESP", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "5132e243-d700-4b26-9234-ab73fd185e8c", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804050200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengecatan ulang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "51352171-0f84-4955-a4fd-4b7893dd741f", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827130110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan komponen utama mesin <PERSON>in", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:06", "updated_at": "2019-11-27 00:29:06", "deleted_at": null, "last_sync": "2019-11-27 00:29:06"}, {"kompetensi_dasar_id": "51355e5b-ebe1-4e1c-86cf-120d0cb3ab7b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Q<PERSON><PERSON> (31): 13-14 dan Q.S<PERSON> (2): 83, serta hadits tentang saling menasihati dan berbuat baik (ihsan).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:55"}, {"kompetensi_dasar_id": "5136106b-2c03-454e-b822-aa2ad5eacf4b", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:52"}, {"kompetensi_dasar_id": "51364ada-81d3-47d1-a6f6-0242cfd46d14", "id_kompetensi": "4241", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca QS Luqman (31): 13-14 dan QS al<PERSON>Ba<PERSON> (2): 83 sesuai dengan kaidah tajwid dan ma<PERSON><PERSON><PERSON><PERSON> huruf", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:00", "updated_at": "2019-11-27 00:30:00", "deleted_at": null, "last_sync": "2019-11-27 00:30:00"}, {"kompetensi_dasar_id": "513768be-68e6-4085-a1bc-d6111c3e087e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 822100110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kinerja Sistem Kontrol Pen<PERSON>gan Kendaraan (Light- Tronic)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "5137a730-744f-4917-9d0b-c5e5d0b13954", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 801030800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan relasi secara positif terhadap korban NAPZA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "5137b7ca-c2cd-4873-be50-fb486d110405", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 839040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>las<PERSON> jenis warna alam dan sintetis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "51399aba-c6e9-44b1-a9bc-f8b23cf21c97", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan cara olah gerak saat berlabuh jangkar dalam segala kondisi cuaca", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:02", "updated_at": "2019-11-27 00:29:02", "deleted_at": null, "last_sync": "2019-11-27 00:29:02"}, {"kompetensi_dasar_id": "513ae3b8-8a3b-4a7f-9614-68a514b0f861", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 843062200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penggalian ide/gagasan dalam berkarya seni karawitan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "513bbb71-c83a-4fad-b33b-f361865e5247", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825250500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kultur  pakan alami", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "513c22e6-380e-4ff9-bbbf-30ebbd5821e1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 816010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>s p<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "513c871b-e095-4edf-8951-44006142fb1e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:38"}, {"kompetensi_dasar_id": "513ca9a0-2f6b-4a35-8bd8-d5655fdabde7", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401131400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> titrasi penetralan (asam basa)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "513cce1a-7e20-409d-b986-d9391521dfa6", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401110300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> reproduksi dan genetika sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:04", "updated_at": "2019-11-27 00:29:04", "deleted_at": null, "last_sync": "2019-11-27 00:29:04"}, {"kompetensi_dasar_id": "513cf7ff-c534-409a-bf71-3fea6ad83ae9", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis macam-macam budaya politik di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:37", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:37"}, {"kompetensi_dasar_id": "513e43b7-e321-4b30-a219-13f6fd165e54", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827390400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply of electron theory", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "513e81fb-010b-4c7b-81d5-11f899ef277d", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 804111000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeksekusi program di mesin CNC Miling", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "513ec6a9-8a81-49ce-8d11-6c09a3b4acdb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "5140d663-b316-4424-8205-1918c6d5ccbf", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 819030110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemantauan kondisi lingkungan laboratorium sesuai persyaratan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "5140eae8-db58-41aa-92e4-cb66fbecd97c", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827390800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Identifies  fastening", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "51412711-218f-40da-8487-7b5e08502a4c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembentukan teknik pijit (pinch)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "51418280-1ea0-4cbc-bf93-d339ebe96cfb", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>an etude instrumen spesialisasi pada genre musik pop 80’s dan 90’s", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "5142c9c2-83b4-4a72-9280-dec0426c66ea", "id_kompetensi": "3.37", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis desain konstruksi sipil pico hydro.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:15"}, {"kompetensi_dasar_id": "5144c1bc-b2d2-4519-9a7f-0258d9316046", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 801031400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemecahan masalah klien dengan menggunakan potensi dan sistem sumber kesejahteraan sosial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "514694a0-1096-4f0b-a3f3-9b8f0dd8400b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan workshop equipment", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "5147d1a1-c3a8-4b8f-b5c6-8acd9083bbc0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 807020300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  Landing Gear system (ATA 32)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "51484193-405d-4793-a900-3feab07575ae", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802010400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penggunaan pakage dalam aplikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "51488eca-5598-41d8-bf09-cc0d61976b96", "id_kompetensi": "4.42", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar ulang komponen-komponen turbin dan kelengkapan mekanik pico hydro menggunakan perangkat lunak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "5148d7e9-5de8-4723-acfe-55328983f0ef", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 824050900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis aspek motivasi dan informasi pada set<PERSON> shot", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "5148ffb1-e2e7-48ac-8c10-8fd120ed9009", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan program komputer  dalam mengontrol proses  pen<PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:31", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:05:31"}, {"kompetensi_dasar_id": "51491f72-d5e3-4dd9-af58-35fd78063bc0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 817160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peralatan dalam pekerjaan pele<PERSON>an", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "5149805a-f0ad-4a31-921c-84db701dab68", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 842040400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kerusakan yang terjadi pada mesin kayu portabel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "514a4095-a326-4543-b33a-ee3b4f5570d1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:52", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:52"}, {"kompetensi_dasar_id": "514a5a79-70dc-45c7-82fb-6e1c0a83b93d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON>ami  alat bantu untuk \r\nmenghasilkan komponen  sesuai\r\ngambar kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:01:08"}, {"kompetensi_dasar_id": "514a5b3b-2719-43a9-8583-a17572da0eca", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821120310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembongkaran dan pemasangan motor penggerak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "514b035d-11c3-4474-981f-5013aa2885e5", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:14"}, {"kompetensi_dasar_id": "514b43c9-b511-422e-97ec-45b6a97d0520", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 821060120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar Hydrostatik dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "514becac-cfe4-404f-bcde-3ed726125c2c", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:23", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:23"}, {"kompetensi_dasar_id": "514c9113-5f40-4697-a350-438b2f1878af", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843062200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> unsur-unsur berkarya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "514c9631-029e-40e6-833e-7a1bc92fc0c1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:33"}, {"kompetensi_dasar_id": "514d488f-f1b8-4bc3-9f22-7ca8febd6de0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 817090300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  peralatan pembelok", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2019-06-15 15:00:03", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "514d60d1-d059-4d80-8016-2f1a3fa311f7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:17"}, {"kompetensi_dasar_id": "514d6e94-6fcc-437c-a8d3-dd66faa9c781", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 818010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang desain ventilasi tambang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "514f04df-ca45-4521-82ac-3a043e9dd137", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "515137c6-6ae1-40e1-b8cc-39863878503d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804110100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengerjaan logam.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2019-06-15 14:49:59", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "51519e3c-c68e-47b8-bb4d-d6a0f91c2685", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>  putaran mesin   \r\nberdasarkan kecepatan potong \r\nbahan benda kerja sesuai table \r\nyang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:16", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:16"}, {"kompetensi_dasar_id": "5151fa21-c9bf-47de-8218-736ade8e0256", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang mencerminkan kesadaran beriman kepada <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:31", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:31"}, {"kompetensi_dasar_id": "5152c23c-92f3-482b-9106-b8967a620a03", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 823040100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar proses dan hasil rancang bangun PLT Biogas berbasis POME yang dilakukan secara mandiri, efektif dan kreatif sesuai K3 dan di bawah pengawasan langsung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "5152e7bd-abb5-4e45-b11d-46926c47e393", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825240310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengemasan produk mollusca, crustacea dan pengalengan standar ekspor dan olah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "515650ac-2a98-451f-8916-1058bdcdfc82", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterpretasi makna teks cerita sejarah, berita, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:10"}, {"kompetensi_dasar_id": "515b3c05-aae7-41f1-889a-ec690baaa9be", "id_kompetensi": "2.3.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penulisan partitur musik sesuai  makna, simbol, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:47", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:47"}, {"kompetensi_dasar_id": "515b71a3-d10c-4912-8ee9-db290c100b68", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401141200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis simplisia dari minyak mineral", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "515b735c-c81e-4f2f-9e71-4ae0e6707d28", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:55"}, {"kompetensi_dasar_id": "515c15cc-2a7d-4592-9c4e-9c758fab2c40", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804101300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menemutunjukkan perala<PERSON>/mesin konversi energy", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "515d6c71-ab5d-4545-a524-a04d58ecd91b", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:22"}, {"kompetensi_dasar_id": "515d75b6-1f8c-4df4-8591-b396953fd14f", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "515dba91-6171-4554-a286-223907724af4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:33"}, {"kompetensi_dasar_id": "515e149a-5c58-403e-b15b-11a2fbcd0f9f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825140100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem irigasi sprinkler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "515e1898-e1af-4eee-97df-c42e0283b915", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis teknik aklimatisasi plantlet tanaman perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "515e3a0e-ce68-414f-b4d3-83fbdf8251a4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola menyerang dan bertahan) salah satu permainan bola kecil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "515eea9c-c97c-4bd2-8e08-aee6303d4972", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401130000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> struk<PERSON>, si<PERSON>t senyawa hidrokarbon serta dampak pembakaran senyawa hidrokarbon terhadap lingkungan dan kesehatan serta cara mengatasinya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "515f51de-3dea-47c5-9dd9-fe5ce00699a5", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 804011300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar perspektif furnitur dengan teknik komputer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:41", "updated_at": "2019-11-27 00:28:41", "deleted_at": null, "last_sync": "2019-11-27 00:28:41"}, {"kompetensi_dasar_id": "515fc2cc-045b-4f28-8c4d-620e506b367a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 830120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan rias wajah TV dan film", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "5161c914-2966-4b28-ba4d-3920beb96492", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804210200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan produk produk migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:27", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "51627809-5ee3-4d71-9ec3-9418cc11c6fe", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perencanaan laboraorium untuk kegiatan praktek, uji coba dan penelitian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:56"}, {"kompetensi_dasar_id": "5162ba09-4147-40e0-8f0b-a015024e1076", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memresentasikan hak atas kekayaan intelektual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:05:46"}, {"kompetensi_dasar_id": "5164a782-f912-4dac-9915-a3058c060a06", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:10", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:10"}, {"kompetensi_dasar_id": "51659d24-2e47-4504-a92c-f46521739534", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:40", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:40"}, {"kompetensi_dasar_id": "5165dd69-8853-4235-a5cf-2386b0e06842", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800060920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengoperasian alat radiologi serta melakukan proses pencucian film di luar kamar gelap", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "516612b1-db65-4726-9644-342269c30e40", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 818010300, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses genesa tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:31", "updated_at": "2019-11-27 00:27:31", "deleted_at": null, "last_sync": "2019-11-27 00:27:31"}, {"kompetensi_dasar_id": "5168539d-090a-453b-9e36-328330e721c6", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami sistem bahan bakar engine jenis Mechanical Fuel System", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:07:06"}, {"kompetensi_dasar_id": "5168c855-8699-4682-8e7d-2c2cdb20a70a", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401251030, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencatatan persediaan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "516a2823-c791-40b0-bec9-3e7ad84ffed5", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804080700, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil membagi garis untuk gambar plambing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "516b01bc-69f8-4a38-a16f-eaf810b044b2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 817060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "516bd759-d3cd-47cd-aada-4749941a28fb", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 803081400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian kombinasional sistem digital pada instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "516bf199-0aa6-4ae6-b91d-ada6af2051fa", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:33"}, {"kompetensi_dasar_id": "516c3917-40f7-40bf-afe9-a4ae3ad08969", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:37"}, {"kompetensi_dasar_id": "516d863a-3c61-4c57-970f-7c220b76bd0e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 800050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan pelaporan data", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "516dbab5-ae32-4c80-be5a-de182d7e5a04", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827370220, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan Peraturan Pencegahan Tubrukan di Laut (P2TL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "516dfe0d-c85e-4c2d-91ed-b77e2913514a", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804150600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan kerusakan komponen pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "516e2a82-a95f-40e4-9af4-45e17696f6f3", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perubahan demokrasi Indonesia dari tahun 1950 sampai dengan era Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "516e2c8c-d498-48f7-a16b-3834492955ec", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "516f3a1b-d44a-4444-a53b-f73d24d3d725", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 832030200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelenggara kan pameran karya seni lukis realis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "516f521a-457f-4f9f-ac23-7ef212978371", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 818010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pemetaan geologi eksplorasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "5170ddec-b11f-4ee9-b491-35cca336e07c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:23", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:23"}, {"kompetensi_dasar_id": "517370fb-650c-4c0a-9ad1-77bceda207b1", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> konsep kerja tim (team work)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2019-06-15 15:03:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "51744300-2f28-4564-97a4-e038dd3e1f84", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 804040200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemasangan atap beton bertulang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2019-11-27 00:28:15", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "51761a95-9a84-4d82-a751-4cf6f2059e49", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 820130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sales number  pada unit alat berat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "517648f0-55c8-45fb-920e-2dfbfe8a63e3", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk menyatakan dan menanyakan tentang keharusan, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:06"}, {"kompetensi_dasar_id": "5176a816-b538-48d6-8c4e-b4945c5944ce", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengan<PERSON><PERSON>ja \r\npada pek<PERSON> frais, \r\npengg<PERSON><PERSON> baju pelin<PERSON>, \r\nka<PERSON><PERSON> pengaman", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "5177ddea-84b8-4cdb-a3d8-fa043f34679d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 814100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses drawtwisting", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-10-18 06:43:59", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "5178315c-4798-4ae6-a232-2bff05bed96c", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan rambut (styling)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "51789e26-25d1-4e24-b760-a326ea904042", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843080410, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pedalangan dasar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "5179273f-0140-4f2b-9820-82a57317a0fd", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan perannya  sebagai  pembawa damai sejahtera dalam kehidupan sehari-hari se<PERSON>u murid <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:10"}, {"kompetensi_dasar_id": "51798da0-0111-441b-9ceb-70bde7d375c8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:32", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:32"}, {"kompetensi_dasar_id": "517a1bc0-92b9-4037-8818-ae7c03fbc87c", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 805010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan data tabular sumber daya hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "517a861f-b66b-4fd5-b908-45fcfb27a3a7", "id_kompetensi": "3.40", "kompetensi_id": 1, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemeriksaan kebuntingan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "517a988c-66e2-409a-884c-cb99ced87f54", "id_kompetensi": "3.14", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan fasa gerak dan fasa diam untuk analisis kromatografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "517b4265-89de-4aab-b1a5-99f80680a897", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 837030100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan desain ruang makan apartemen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:41", "updated_at": "2019-11-27 00:28:41", "deleted_at": null, "last_sync": "2019-11-27 00:28:41"}, {"kompetensi_dasar_id": "517c7f5f-4751-4990-9817-b8cdf6c7f37c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:18"}, {"kompetensi_dasar_id": "517ec414-5a56-47aa-a381-f1f4140c25fe", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengidentifikasi prinsip kerja mesin konversi, pengeringan, pencampur, pemanas dan <PERSON>in serta perpindahan daya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "517f0daf-69fe-42b6-954e-d94bbfa45266", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:30"}, {"kompetensi_dasar_id": "517fdc93-9bf0-49f8-83a0-a7979b3a211b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825100200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prosedur mengemudikan trakror pertanian roda dua pada jalan menurun dan menanjak disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:46", "updated_at": "2019-11-27 00:28:46", "deleted_at": null, "last_sync": "2019-11-27 00:28:46"}, {"kompetensi_dasar_id": "518210d9-5267-4e63-82b7-7fe588b009c2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:07", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:07"}, {"kompetensi_dasar_id": "518212f7-fd96-4344-8cc2-f99dc8083187", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknik pemesinan  bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan  teknik pemesinan  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:56", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:26:56"}, {"kompetensi_dasar_id": "5182996c-46e6-472d-b95c-e06859230a54", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemasangan dudukan dan modul surya PLTS tipe Penerangan Jalan Umum (PJU)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "51832538-fd98-4b34-9f31-59c9110747e3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:05"}, {"kompetensi_dasar_id": "51832d92-5f24-4b91-898c-a710fcd85258", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825280200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Merinci peralatan laboratorium dasar mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "518384d5-e8ba-42d0-afd4-e36eb7d91c37", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dalam penggunaan peralatan dasar laboratorium    (alat-alat gelas dan non gelas)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "5184c580-3501-49df-a523-c28ffc880262", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem audio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:31", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:31"}, {"kompetensi_dasar_id": "5184d895-5d5d-4939-add8-213933ee965a", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi perhitungan  sucker rod pump", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "51858e56-6fbb-4878-8c4b-c04f87b18728", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401251104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan prinsip dasar distribusi Islam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "5185e6ed-eab6-4057-96d0-2b1d6d4abe63", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 802020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan siklus hidup obyek dalam sistem berorientasi obyek.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "5186b1cf-38a8-4cdc-b2cb-fa0e7e2f0b65", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:30"}, {"kompetensi_dasar_id": "51879b6f-4788-47f7-8607-41c3c60febd8", "id_kompetensi": "3.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan pekerjaan laboratoriuk sesuai SOP K3LH", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:05", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:05"}, {"kompetensi_dasar_id": "5187e53a-1af1-422e-b37b-a2aa0d3154e7", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 821020100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi alat pencegah pencemaran dilaut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "518803bb-72bf-47b2-87b6-24faeb9e3a4c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penyiapan sampel dan standar untuk khromatografi <PERSON> (TLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:34", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 16:00:34"}, {"kompetensi_dasar_id": "51890914-45cb-49ee-94eb-e93aec94aa9e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:17"}, {"kompetensi_dasar_id": "5189bf7a-5e4c-4452-aa0c-dd662162c140", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 827060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembacaan daftar almanak nautika", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "518a1372-6a3e-430a-a0c5-c74694133a90", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji<PERSON> jenis-jenis bahan alat saniter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "518c06c9-6e04-4346-bdff-b851e91aacf9", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 401130620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi data analisis Kadar air", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "518c843d-feda-4abd-b4eb-85cca976bdae", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengapresiasi Seni Pertunjukan Tradisional berdasarkan jenis dan fungsinya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "518caccf-4c9c-4f7f-ad3c-9356dce356a9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cara pengukuran luas lahan pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "518d1999-5489-40d8-acec-45238683ada0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "518d3af7-9401-48b3-9c47-bab5e212079f", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 814031900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan stock opname", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "518e16b1-0205-4ae9-bc78-111c32343339", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan appendages boiler/ketel uap", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "518e9357-5825-45ef-8c61-e6577cd79d8a", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251170, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       <PERSON><PERSON><PERSON><PERSON> persa<PERSON>an dasar akunta<PERSON>i", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "518f02b0-8453-4aa9-869b-b6290d191845", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 300310800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Memproduksi beragam bentuk wacana khusus bidang keahlian profesional dengan memperhatikan fungsi sosial, struktur teks, serta unsur kebahasaan pada teks interpersonal dan teks transaksional tulis dan lisan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "518f0b53-7c36-4bfd-a6ef-e59e8ab2cbb2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:22", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:58:22"}, {"kompetensi_dasar_id": "51904212-fa28-4534-9e71-c77d9b299379", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 839050100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilah motif batik tulis modern", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "51904533-b6fb-45cd-a55c-5c04b05fba41", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:47"}, {"kompetensi_dasar_id": "5190d4c3-34c0-4a3b-b425-b1753c2c9309", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:13"}, {"kompetensi_dasar_id": "519111e4-bc58-4aae-9825-289e80028cbd", "id_kompetensi": "2.4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan  partitur lagu karya sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:45", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:45"}, {"kompetensi_dasar_id": "51915c3d-e901-4a1c-b61d-77b4fac5f916", "id_kompetensi": "2.3.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penulisan partitur musik sesuai  makna, simbol, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:45", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:45"}, {"kompetensi_dasar_id": "5191d67e-42fe-4e72-b26f-2b40a123b772", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis prosedur perawatan dan perbaikan konstruksi bangunan gedung yang tergolong rehabilitasi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:56", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:33:56"}, {"kompetensi_dasar_id": "51920118-b96c-48dd-871d-4f6d5011f8e7", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membuat rangkaian control sederhana", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:26"}, {"kompetensi_dasar_id": "519225c7-8425-4e6d-ba92-da36afc6c004", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "51929639-c554-4242-9fc7-1b95e0ad233f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827390400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain of electron theory", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "51931305-1188-440c-9004-1c0221b76fc6", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemons<PERSON><PERSON><PERSON><PERSON> (3): 190-191, <PERSON>n <PERSON> (3): 159, <PERSON><PERSON>ar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:08:53", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:12:56"}, {"kompetensi_dasar_id": "51934369-6d46-4c0b-a709-1f72de345eec", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> rang<PERSON>an sistem pneumatik satu silinder dengan  menggunakan komponen- komponen pneumatik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "51949b57-b7ef-4b61-a5c3-29facee962fb", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 803090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rancangan rangkaian OPAMP sebagai rangkaian elektronika aritmatik dan kegunaan khusus dengan electronic software", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:12:35"}, {"kompetensi_dasar_id": "5194f464-4413-4eb3-ab9b-c371c79665b8", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan percobaan aplikasi konsep dasar ilmu kimia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:03"}, {"kompetensi_dasar_id": "519658ba-62bd-4571-a02e-0707be7327b3", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 826060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON> dasar komposisi tari berkelompok", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "51967349-c40d-4683-91f0-ecaa655efa44", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843060300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur iringan tari berdasarkan polal agu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:29", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "5196ceaa-6646-47b7-9233-e1a629e0f74b", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja sistem kemudi dan Power Steering", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "5196e2b1-2ac6-48c8-b59c-5227a0ef61c7", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:55", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:02:55"}, {"kompetensi_dasar_id": "51970d75-6bf7-401f-816c-4233e8252d1f", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 826020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> <PERSON> kayu bulat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "51985874-c180-42ed-a3ed-144a2fe03a0c", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kontrol yang terdiri dari PLC, pnuematic dan hydraulic.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "5198f997-e2b4-4c95-af32-5e76b2c2a933", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  hot section inspection", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-10-18 06:43:59", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "519b101d-90be-40c4-a729-b623968e561f", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 825063600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan penanganan ternak unggas petelur afkir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:11", "updated_at": "2019-11-27 00:28:11", "deleted_at": null, "last_sync": "2019-11-27 00:28:11"}, {"kompetensi_dasar_id": "519bc8f0-47b2-45b8-aa90-576dc997b012", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825250600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>n Produksi pada pendederan ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "519e1ee3-5ad0-40a9-a3cb-2a1b98e647aa", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804011400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan program aplikasi computer tiga dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "519e62f7-3499-45bb-8b23-cc853b3e7447", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 803090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang program kalibrasi instrumentasi medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "519f2dbd-a58b-4751-8949-d9e6afb5876a", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 805010800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan spesifikasi teknis basis data kartografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2019-11-27 00:27:49", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "51a1b0d8-3668-4918-9294-e10cdd9d3b22", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 827390600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Describe shafting installation", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "51a313d5-f70c-4bb6-aaf0-1e991c45f595", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan seleksi dan culling pada bibit (parent stock)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "51a32fad-2f59-4a5e-b74d-6a100f9bc4bb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis-jenis bahan yang digunakan untuk konstruksi bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:14", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:14"}, {"kompetensi_dasar_id": "51a3673d-b9f5-4821-9bb8-7e8568fe89e5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "51a3cac8-3873-4a6f-8560-f53b9a1a298b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil  pengukuran dan penandaan pada material kayu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "51a4562d-7488-481e-8c8b-57f24a3f40aa", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803071400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membangun rangkaian Penala pada radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "51a4b22d-3c5e-4956-979f-2d800389c7ab", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 824051500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Unit Shot", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "51a5589e-4407-4e49-9502-a37a861b0646", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:45", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:45"}, {"kompetensi_dasar_id": "51a55db5-0f40-43ca-ae5c-d5ab2ad7bf53", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401132100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis dengan metode  potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "51a59320-d9cf-4d5b-b245-c3ed572329bb", "id_kompetensi": "4.4 Rupa", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membuat tulisan kritik karya seni rupa mengenai jenis, fun<PERSON><PERSON>, simbol dan nilai estetis berda<PERSON><PERSON> hasil pengamatan", "kompetensi_dasar_alias": "", "user_id": "881946e1-fcde-4c87-8e1c-ed5c309853ac", "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:29:44", "updated_at": "2019-06-15 15:29:44", "deleted_at": null, "last_sync": "2019-06-15 15:29:44"}, {"kompetensi_dasar_id": "51a870b0-d004-48db-8e94-4e10041fbbe4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843120300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan peralatan dan perlengkapan tata suara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "51a91d2a-8e42-4c6d-b07d-4fcc6cf7fde5", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan jenis-jenis dan fungsi peralatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "51aa43b7-98e2-46d9-bc03-539851b7677c", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804050450, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan peker<PERSON><PERSON> instalasi listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:15", "updated_at": "2019-11-27 00:30:15", "deleted_at": null, "last_sync": "2019-11-27 00:30:15"}, {"kompetensi_dasar_id": "51ac028a-56bb-440a-8453-b69b37d317f0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:46", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:46"}, {"kompetensi_dasar_id": "51ad0f92-fcd5-428c-ac53-e2d802823a6e", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 827290400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasaran hasil panen dan pasca panen rumput laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "51ad6681-0a8e-488d-bbfc-533c7afe45ca", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 843050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis melodi dari genre musik pada tingkat lanjut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "51af0a60-6b4c-419e-8f8b-1bf2c2fa7629", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang fungsi naik dan fungsi turun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:01"}, {"kompetensi_dasar_id": "51af1ec0-f486-4180-8188-6b6c0d8b8f08", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804210100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan proses pengukuran tekanan, pengukuran permukaan cairan / level measurement dan pengukuran temperatur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "51af26d3-3768-488c-b62c-19701446488d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merangkai sensor sistem analog (non digital) pada aplikasi rangkaian elektronika untuk mendeteksi sensitifitas cahaya, temperature /suhu, berat / tekanan, dan kecepatan putaran “rpm”: rotation per minute)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "51afcf92-f0fe-4037-a978-f663cf4940d8", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 803080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Men-setup Special I/O dan Networking PLC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "51b388c5-25f1-4d50-afbf-cb6a0d134523", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:02"}, {"kompetensi_dasar_id": "51b522e9-3ec2-40f3-80b3-47505b30f7ee", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeliharaan panel kontrol sistem refrigerasi dan tata udara komersial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:20"}, {"kompetensi_dasar_id": "51b528f4-6bae-4ab1-addf-f466450decdc", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 821171000, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan percobaan sistem pendingin refrigerasi dan tata udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "51b592c9-c1b7-40ea-992a-bcc445ac2c51", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 829121200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is  produk \"roolled cake\"", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "51b67a9e-0e3a-40f0-8f2a-2701476a35a7", "id_kompetensi": "Profil entrepreneur, job profile, pel<PERSON> usaha dan pek<PERSON>/profesi pelayanan jasa perhotelan ", "kompetensi_id": 3, "mata_pelajaran_id": 800000141, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu mendeskripsikan profil dan karakteristik seorang hotelier/entrepreneur, personal branding dan HAK<PERSON> (Hak Atas Kekayaan Intelektual) yang mampu membaca peluang pasar dan usaha perhotelan (contoh :  usaha laundry services, home cleaning service, towel art folding sehingga menginspirasi dalam membangun passion dan kebanggaan terhadap pekerjaan di bidangnya. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:23", "updated_at": "2022-11-10 19:56:59", "deleted_at": null, "last_sync": "2022-11-10 19:56:59"}, {"kompetensi_dasar_id": "51b7d005-3f27-45d3-859e-3a3772ac3f58", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 828120300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>ak<PERSON><PERSON> pem<PERSON><PERSON>an standar kinerja pribadi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "51b80dbf-628c-4a69-ad5f-e85d4f55c381", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran Indonesia dalam hubungan Internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:19"}, {"kompetensi_dasar_id": "51b85196-3c3f-4b35-a724-f5cd5dd272e2", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja macam-macam aktuator pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "51b95203-fabb-40f4-ae67-16ceb8d98ace", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 800050430, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerap<PERSON> per<PERSON>ungan nadi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "51b97182-d9e0-4382-b562-67f118ddfb8d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda silinder (cylindrical grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:09", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:08:09"}, {"kompetensi_dasar_id": "51b97724-822a-43fe-9e63-21b91fd390c2", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:39"}, {"kompetensi_dasar_id": "51b9947c-4f18-478d-a4d1-219984222eb6", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat-sifat transformasi geometri (trans<PERSON>i, refleksi garis, dilatasi dan rotasi) dengan pendekatan koordinat dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:52"}, {"kompetensi_dasar_id": "51ba4ad0-c648-46e2-86c4-7fe063815b5b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan kemampuan Sumur Flowing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "51ba77e9-5885-47de-a2ed-074cbc9a79e2", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:11"}, {"kompetensi_dasar_id": "51bb0051-9cfd-4cc7-9ed9-ddb6b26204aa", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 821090330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan pembuatan body kapal <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "51bb1177-b3da-4f51-884b-afab53e0db31", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:44", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:44"}, {"kompetensi_dasar_id": "51bcfc67-57ba-446d-a2ca-698d93697007", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 827350200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain keeping a log (menjelaskan alat kecepatan kapal)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2019-11-27 00:29:11", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "51bd451a-de62-4777-9890-5bd684412f73", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:46"}, {"kompetensi_dasar_id": "51be365c-cdb5-4d52-b16a-73bce1152874", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "51be95c8-3ffa-4a21-b160-53291802abc5", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "51bfc5b7-2ec2-463d-ad69-09ab5ffd4954", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 839070200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat produk sablon dengan desain teknik digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "51c04c4e-4b4e-4283-8f7a-2a11cf50c25b", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 823040100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar proses dan hasil ekonomi dari pemanfaatan PLT Biogas berbasis POME yang dilakukan secara mandiri, efektif dan kreatif sesuai K3 dan di bawah pengawasan langsung.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "51c05618-4dcd-4b05-bdd9-1636642c12c5", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 829121200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  cake  untuk ulang tahun", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "51c0728e-78dd-4770-b34c-1361256fd308", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 821090400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dan menerapkan pelaksanaan persiapan permukaan cat ulang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "51c0bdb7-038b-49b9-aa13-15e4c959bad1", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 801031300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan etika dalam kegiatan rehabilitasi sosial disabilitas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "51c2f0d7-ba28-4f9b-b3b8-39a97c96dd63", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803071500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan jenis-jenis konektor saluran transmisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "51c30291-bbbe-4680-8d95-0bf311b6e732", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "51c3ab7f-048f-4ada-b6f1-2e264d9c1728", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401251105, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menganalisis pencatatan transaksi sumber dana wadiah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "51c3ee1e-b4bd-4e6f-b046-79b5625534d2", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan alat-alat ukur hidrolik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "51c50150-ec64-48f7-b997-ff8bc80c1492", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 800050430, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pertolongan mobilisasi klien", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "51c53715-2e7e-470c-872e-1773e0ec2455", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:21", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:21"}, {"kompetensi_dasar_id": "51c59271-2691-419c-a045-ddc00cc368bd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843062200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan observasi dalam berkarya seni karawitan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "51c82a90-61e4-4cd2-8be0-184d81203bd1", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 829080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengevaluasi hidangan kesempatan khusus untuk acara adat istiadat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "51c9e2ea-d130-4805-bca9-f4c90fdf9445", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:13", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:13"}, {"kompetensi_dasar_id": "51ca2f8f-1d09-4b00-8804-a5b65a26531a", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 827060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> daftar deviasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "51ca8ec6-32af-43a7-92a3-0fde6e3d5e1a", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 815011000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gearing diagram mesin Combing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "51caaa16-fc09-4510-89c0-3dea9076c17d", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang dan mengajukan masalah nyata serta menggunakan konsep dan sifat turunan fungsi terkait dalam titik stasioner (titik maximum,titik minimum dan titik belok)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:01"}, {"kompetensi_dasar_id": "51cadf98-fdda-4d0d-8245-2e3ba2abfa16", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Alat Pemadam Api Ringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:59"}, {"kompetensi_dasar_id": "51cc6a95-1d27-403b-8965-84d1c6eaf706", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat struktur organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:56"}, {"kompetensi_dasar_id": "51cd154f-9467-4fbf-8af8-24e21189a285", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencampuran warna tinta cetak offset gulungan warna khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "51ce5ab8-055c-480f-8cba-294c07e62c4a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820070500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem pengapian konvensional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "51ced66b-5af4-440b-b372-92b0725120e1", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "51cf1b21-4473-46a8-9527-c455d902d65e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843060620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik tata busana ras/suku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "51cfcf34-e808-459d-80a3-628134d764ab", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan bedah <PERSON> (nekropsi) pada mamalia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "51d12ba9-4598-4e28-9bc0-8f83f7238d95", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Q<PERSON><PERSON> (31): 13-14 dan Q.S<PERSON> (2): 83, serta hadits tentang saling menasihati dan berbuat baik (ihsan).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "51d2a67e-5bc1-4cbe-8aa3-db89d12e5630", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 824050600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami peran dan tugas operator kamera", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "51d3c6cb-01fc-480f-bf05-dadcb38aee2b", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 827210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik dan tipe pengolahan komoditas rajungan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:35", "updated_at": "2019-11-27 00:29:35", "deleted_at": null, "last_sync": "2019-11-27 00:29:35"}, {"kompetensi_dasar_id": "51d4035c-3f7b-42af-ae62-640646edb7d5", "id_kompetensi": "4.51", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis sensor untuk sistem aplikasi robot mobile se<PERSON>ai keperluan dan tujuan robot mobile", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "51d46962-e96f-4516-8b9b-61c42881e249", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:31"}, {"kompetensi_dasar_id": "51d51934-8b17-4e54-a4e4-d376cd4620b9", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang integral tak tentu dari fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:37", "updated_at": "2022-10-19 23:19:15", "deleted_at": null, "last_sync": "2019-06-15 16:03:37"}, {"kompetensi_dasar_id": "51d5a48e-313a-4eb1-9f74-9f03255cac7d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja sistem <PERSON>in udara  (air condition system)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "51d63234-1129-43bf-8498-46442be07322", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian pengatur model P D (Proportion Defferential), dengan menggunakan penguat operasional (operational amplifier)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "51d7c8f8-3386-4395-8963-b01eec891689", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 828080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON> dana kas kecil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "51da3759-d4a5-490d-98cd-3666af14eeda", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterprestasikan lembar data (datasheet) dioda penyearah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "51da9e4d-a064-4b14-b78f-8e4a184b4bfb", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:12", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:12"}, {"kompetensi_dasar_id": "51dab7e4-a474-4eb4-9f56-c8dba1c96a49", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi piranti kontrol solid state saklar manual.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "51dbfd11-d0f8-405e-9c92-a79365584b30", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan peniruan karya seni budaya Nusantara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:02:45", "updated_at": "2019-06-15 15:02:45", "deleted_at": null, "last_sync": "2019-06-15 15:02:45"}, {"kompetensi_dasar_id": "51dc0ec4-81c1-42d4-b6e6-97ac8bec94f0", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 809020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan percobaan pengolahan limbah cair.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "51dc2f31-e72c-42bd-85f2-38bb01b14f66", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah editing assembling", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "51dd960c-8779-445f-b0b0-bad7030feb98", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 827320110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Understand Fresh water allowance", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "51de2888-12f7-49ba-b9aa-1ba8bec6d4ad", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dekorasi dan ornamen eksterior dengan mempertimbangkan kesesuaian  komposisi, harmoni, dan estetika dalam desain eksterior", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "51de640f-d652-43c2-a4c1-dbb575c815e7", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 809020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persiapan sesuai pesanan cetak dengan teknik cetak tampon (Pad Printing)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "51de6808-5f2b-4b24-bee0-e943d6bdc708", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 800050430, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pertolongan pengubahan posisi klien", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "51df9648-7109-499b-ba2d-c3faedbc5de3", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:29"}, {"kompetensi_dasar_id": "51dfe49e-85fb-4e90-9c08-676f05f0b427", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 824060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Question Route yang sesuai dengan Topik dan Nara Sumber", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "51e0f333-9ea4-4ebe-a4e8-eb36517076c7", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:13"}, {"kompetensi_dasar_id": "51e0fabb-9035-4815-b9c2-e5db65786aeb", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan latihan pengukuran komponen kebugaran jasmani untuk kesehatan (daya tahan, kekuatan, kompo<PERSON>i tubuh, dan kelenturan) menggunakan instrumen terstandar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:02:48", "updated_at": "2022-10-19 23:19:18", "deleted_at": null, "last_sync": "2019-06-15 15:02:49"}, {"kompetensi_dasar_id": "51e13d86-ddb3-4351-9183-958d4e704ab5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827390700, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Describe hand tools", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "51e14340-0fd6-4dd1-af3c-475b86a7f3ad", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:23"}, {"kompetensi_dasar_id": "51e228ba-8d9e-4d44-8b4f-c87e8e7f27af", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sistem kesetimbangan phase, sistim k<PERSON>, dan si<PERSON>t kimia fisika bahan pada proses industri kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:03"}, {"kompetensi_dasar_id": "51e23888-a7d6-4c09-a1ee-fce25f76fb57", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 827080300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan pencegahan dan penanggulangan pencemaran laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "51e2ae7c-1f49-4f3f-bafa-dc084d96004d", "id_kompetensi": "Akidah ", "kompetensi_id": 3, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Akidah berkaitan dengan prinsip kepercayaan yang akan\nmengantarkan peserta didik dalam mengenal <PERSON>, para\nma<PERSON><PERSON>, kit<PERSON><PERSON><PERSON><PERSON>, para <PERSON><PERSON> dan <PERSON>, serta\nmemahami konsep tentang hari akhir serta qadā dan\nqadr. <PERSON><PERSON><PERSON> inilah yang kemudian menjadi landasan\ndalam melakukan amal saleh, berakhlak mulia dan taat\nhukum", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:46", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "51e2c129-3e52-4a55-acf3-99ff0f011994", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:22"}, {"kompetensi_dasar_id": "51e3e509-bce3-4751-93d6-63ddf1efa39e", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 802031400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan teknik memegang kamera video", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "51e43ef7-3531-4da8-b422-d474cb39f12d", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804101100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa pekerjaan elektromekanik dari bahan non logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "51e45853-8fd1-4160-aeea-f3210e093566", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:13", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:01:13"}, {"kompetensi_dasar_id": "51e569c1-7fd2-4421-a553-6e2039a727c4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804040200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pen<PERSON>tian pondasi batu kali untuk konstruksi bangunan gedung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2019-11-27 00:28:15", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "51e61004-ed35-4514-8c8b-d7b76a7ab301", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843010720, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan posisi duduk dalam mema<PERSON>an piano", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "51e6cd33-2b10-4e74-a22d-e9375ce7c3de", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jeni<PERSON>, fungsi dan cara kerja komponen kelistrikan pada sistem kontrol mesin perkakas (transformator,tahanan, kapasitor, sensor, kontaktor, relay, motor, peralatan proteksi)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2019-06-15 14:49:59", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "51e83aea-2412-4cf2-8e3c-abc2c53a3e0c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "51e83e8d-d2d0-4ab4-99f7-f4c1ad8e4a3b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan sistem pengapian elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "51ec68ce-4e39-47a3-ad68-8b40b65c2926", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasarkan aneka produk industri kimia skala kecil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:38", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 16:08:38"}, {"kompetensi_dasar_id": "51ec6c32-ad17-4959-ad08-65ed79cabad7", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 803061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan peralatan instalasi system audio paging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2019-11-27 00:29:11", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "51ed9665-8237-48a8-af48-a7fe9b591e53", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati cara kerja Final Drive assembly.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "51ede895-9da5-42c8-add1-14cbca484a71", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 824050900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan element visual eye contact pada produksi film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "51f085b4-d050-4b9a-b769-2909e5450b8b", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 802032300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip-prinsip desain logo", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "51f28a74-695e-46c8-bcff-9f5641725404", "id_kompetensi": "2.3.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penulisan partitur musik sesuai  makna, simbol, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2013, "created_at": "2019-06-15 16:06:01", "updated_at": "2019-06-15 16:06:01", "deleted_at": null, "last_sync": "2019-06-15 16:06:01"}, {"kompetensi_dasar_id": "51f3acb8-a19f-4856-a032-7b013d99e702", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 822040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis output penggambaran CAD 3D", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2019-06-15 15:12:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "51f43df2-043e-462e-becc-272ca7503a78", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401131600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis kadar le<PERSON>k", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "51f445d2-4ed1-47f6-b8c2-bf5487d27ced", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan perannya  sebagai  pembawa damai sejahtera dalam kehidupan sehari-hari se<PERSON>u murid <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:10"}, {"kompetensi_dasar_id": "51f4bae0-5700-4863-a118-336ad05cd192", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis dokumen sumber dan dokumen pendukung pada perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "51f68a30-7365-482e-8a4f-8f4c7156fa4b", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji instalasi Penerangan 3fasa untuk lapangan olah raga sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "51f6b097-16f1-4638-bfa7-706bcfee3810", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:33"}, {"kompetensi_dasar_id": "51f6de06-51b8-4aa7-af57-ee6177e982c5", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 826110100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pengukuran sedimentasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "51f7690a-3c35-4d59-a939-bbc040f808cc", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 828041100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Melaksanakan kegiatan rapat (teleconference)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "51f889a7-75e4-4136-8124-74e3f492ae72", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 800061500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan etika profesi dental asisten saat melakukan asisten", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "51f941c6-f58c-4364-a682-25d587d527b7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda datar (survace grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:04", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:03:04"}, {"kompetensi_dasar_id": "51f9653f-942e-41ac-8b7d-e971a93341c3", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803041400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi proseduroperasi komunikasi satelit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "51faac43-1a0b-4b6d-ae03-0ccc9fa198bd", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:21"}, {"kompetensi_dasar_id": "51fb183d-68f9-4627-a055-16146e1b5477", "id_kompetensi": "Merefleksikan ", "kompetensi_id": 3, "mata_pelajaran_id": 700109000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "P<PERSON>rta didik mampu <PERSON>, melibatkan diri secara aktif dalam pengalaman atas kesan terhadap bunyi-musik, peka dan paham, serta secara sadar melibatkan konteks sajian musik dan berpartisipasi aktif dalam sajian musik yang berguna bagi perbaikan hidup baik untuk diri sendiri. se<PERSON><PERSON>, l<PERSON><PERSON><PERSON><PERSON>, dan alam semesta. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:47", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "51fc02bc-0469-4e34-b604-391aabb53c18", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan ungkapan terkait pengalaman berwisata dengan memperhatikan fungsi sosial, struktur teks dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:54:09"}, {"kompetensi_dasar_id": "51fc2880-4d8f-47f5-bcb8-21588e3a4715", "id_kompetensi": ".", "kompetensi_id": 2, "mata_pelajaran_id": 827180100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}]