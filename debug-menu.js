// Debug script to check navigation menu issues
// Run this in browser console

console.log('=== NAVIGATION DEBUG ===');

// Check if user data exists
const userData = JSON.parse(localStorage.getItem('userData') || '{}');
console.log('User Data:', userData);

// Check user abilities
console.log('User Abilities:', userData.ability);

// Check if user has Waka permissions
const hasWakaPermission = userData.ability && userData.ability.some(ability => 
  ability.action === 'read' && ability.subject === 'Waka'
);
console.log('Has Waka Permission:', hasWakaPermission);

// Check user roles
console.log('User Roles:', userData.roles);

// Check if user has waka role
const hasWakaRole = userData.roles && userData.roles.includes('waka');
console.log('Has Waka Role:', hasWakaRole);

// Check navigation items
import navMenuItems from '@/navigation/vertical';
console.log('Navigation Items:', navMenuItems);

// Find wakur navigation
const wakurNav = navMenuItems.find(item => item.title === 'Monitoring');
console.log('Wakur Navigation:', wakurNav);

if (wakurNav && wakurNav.children) {
  const bukuIndukItem = wakurNav.children.find(child => 
    child.title === 'Cetak Buku Induk' || child.route === 'progress-buku-induk'
  );
  console.log('Buku Induk Menu Item:', bukuIndukItem);
}

console.log('=== END DEBUG ===');
