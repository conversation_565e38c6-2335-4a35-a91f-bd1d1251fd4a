[{"kompetensi_dasar_id": "30bb34f6-6bb3-448b-8a27-dc6b3b01162a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:31"}, {"kompetensi_dasar_id": "30bb6b87-920c-4282-ae10-6821928d82df", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 827390800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate maintenance and repair oils, fuels and lubricating system", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "30bba78f-f24a-41ac-b844-09f3cdf0de13", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara melakukan olah gerak dengan berputar di perairan sempit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:02", "updated_at": "2019-11-27 00:29:02", "deleted_at": null, "last_sync": "2019-11-27 00:29:02"}, {"kompetensi_dasar_id": "30bdc60e-f8ea-4d68-834e-77e29072df7b", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 827190120, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemanenan hasil pengembangbiakan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "30be2544-8212-43d7-b1ce-b176a91c5ac4", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 828210100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangani check-out untuk tamu individu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "30be940e-b8eb-4039-a2b9-1935d2b69f0f", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pen<PERSON>, penawaran dan persetujuan dalam teks negosiasi berkaitan dengan bidang pekerjaan lisan maupun tertulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:57"}, {"kompetensi_dasar_id": "30bed194-dc91-4269-96ea-7192db8a624e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843030500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> hasil analisis seni <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "30c06666-3117-49b7-8bc1-6efab684a6de", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> kese<PERSON>, kese<PERSON>atan kerja dan lingkungan hidup", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "30c0de4c-4e3b-4f78-b8d9-5c44b3dd0450", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawa<PERSON> filter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "30c64fdb-01fd-401c-8ebc-671870aaff57", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-ni<PERSON> k<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, perdamaian dan keu<PERSON>han ciptaan sesuai dengan ajaran <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:06"}, {"kompetensi_dasar_id": "30c83ef5-8eae-4d4f-8ed5-f0e716c8dcdf", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 814030900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki hasil produksi yang tidak efektif dan efisien (waste)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:26", "updated_at": "2019-11-27 00:29:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:26"}, {"kompetensi_dasar_id": "30c87023-54d4-490e-899b-ec494086898d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 802020900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil monitoring dan kontrol kinerja server", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "30c9dd32-7ef5-4b26-8880-476835436b55", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:29"}, {"kompetensi_dasar_id": "30cc48e3-d57f-40fb-8941-8a636d85d958", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:56:58", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:56:58"}, {"kompetensi_dasar_id": "30cd44d1-7c59-44ea-91cd-de982aa787ff", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil evaluasi dari berbagai media massa tentang peran Indonesia dalam hubungan internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:31"}, {"kompetensi_dasar_id": "30cd9b8d-e649-431b-b0ac-543fc174152d", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 825020400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis perlakuan khusus untuk benih dan bibit tanaman hias secara kimia, fisik dan mekanis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "30cddfcf-b2e9-4ef6-8651-6bf23c46c510", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802032700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "menerapkan cara membuka perangkat lunak untuk menggambar teknik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "30cf393c-1509-4540-85f8-84e66e4f199d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825020200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pembuatan bedengan tanaman sayuran sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:04", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:04"}, {"kompetensi_dasar_id": "30cf406f-2bbb-46f9-a16f-af99f49aff0a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804150400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengelolaan bengkel manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "30cfabbd-e1df-4233-b46d-0d6551825a42", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem Engine Management System (EMS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:02", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:33:02"}, {"kompetensi_dasar_id": "30d09edf-dd7f-4d99-9744-3efb5465c50b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803080900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kondisi operasi sistem dan komponen perangkat keras PLC  berdasarkan operation manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "30d1b492-954f-40a4-84fc-458116ca7f2b", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 820100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pemasukan udara engine", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "30d2988e-270f-4302-8cc7-da00ee981518", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menya<PERSON><PERSON>g Air\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:15"}, {"kompetensi_dasar_id": "30d2a856-488f-429e-a7af-26e5fe64fac6", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika konsumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:08", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:06:08"}, {"kompetensi_dasar_id": "30d349a6-5d07-4362-8812-27c3ca81d55f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "30d50282-2f9b-4a40-89de-ff90c5552c41", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:50"}, {"kompetensi_dasar_id": "30d54808-eb24-459d-83fe-21578ed3ac71", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:48"}, {"kompetensi_dasar_id": "30d5f9ca-56f5-4e6c-9a14-81714e57715d", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukanperhitunganberbagai proses berda<PERSON><PERSON><PERSON><PERSON>ter<PERSON>dinami<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:29"}, {"kompetensi_dasar_id": "30d69a9d-b1cb-4130-8bbf-e32719958130", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "30d7dcdd-de1e-4e0e-b75e-e7ed07852a8e", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 843100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan soliloki", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "30d8140b-ee35-46f0-8a98-0b10b8aee8d5", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:58", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:26:58"}, {"kompetensi_dasar_id": "30d8a2d1-559a-4738-80d8-4833a31991f8", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827130110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem kelistrikan dan alat kontrol pada mesin <PERSON>in", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:06", "updated_at": "2019-11-27 00:29:06", "deleted_at": null, "last_sync": "2019-11-27 00:29:06"}, {"kompetensi_dasar_id": "30d8eae0-7321-4afb-9b30-b6747cc69e44", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa papan hubung bagi utama tegangan rendah (Low Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:41", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:06:41"}, {"kompetensi_dasar_id": "30d97d86-8f8a-42f9-a1f3-1c933c65ee6f", "id_kompetensi": "4.3.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>  tea<PERSON><PERSON><PERSON> simbol, j<PERSON><PERSON>, dan fungsi dengan beragam teknik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2019-06-15 14:49:54", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "30d9cabb-c241-464a-a04b-0af3d0655c2f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemograman  mesin bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan teknik pemograman  mesin bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:26:55"}, {"kompetensi_dasar_id": "30da52ee-f3c5-430f-93e6-fb0e78d07193", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip K3LH mengikuti SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:41"}, {"kompetensi_dasar_id": "30dabfc4-f12c-43af-a3ec-41b27e1e5990", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843070200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan etude dengan intonasi yang baik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2019-06-15 15:07:31", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "30dad2a7-ec74-45f6-a61f-27e1c6bb54c0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "30db4677-f763-4879-80ec-1f0a9ae4600c", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 803090400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat bagian sensor dari sistem robotik pada instrumentasi medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "30dc9d71-0ece-4775-9f6c-abc6bf675b8f", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis tentang system hukum dan peradilan internasional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:05", "updated_at": "2019-11-27 00:30:05", "deleted_at": null, "last_sync": "2019-11-27 00:30:05"}, {"kompetensi_dasar_id": "30dd9aa0-bec9-4861-b7d9-28d385e972d8", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 822090110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosa Sistem Transmisi Otomatis Kontrol Elektronik  (ECT)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "30dec480-bff4-463f-a6b2-703fdba89f3d", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:43", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:43"}, {"kompetensi_dasar_id": "30e00f92-f3e6-430c-8b22-16447ef81377", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821171000, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan fungsi komponen kelistrikan refrigerator dan tata udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "30e1518e-be99-4058-a97b-ce1dcd726f18", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:52"}, {"kompetensi_dasar_id": "30e187ca-eb7c-4b6e-b961-16555479841d", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:39", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:39"}, {"kompetensi_dasar_id": "30e21082-1174-467a-85d6-6f471fbd5c89", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar konstruksi saluran irigasi sesuai spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:14", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:14"}, {"kompetensi_dasar_id": "30e2b272-fdad-4f32-90bf-30d310229c1e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:29"}, {"kompetensi_dasar_id": "30e2e06a-1915-40b7-835c-563f8b5c3a25", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:57"}, {"kompetensi_dasar_id": "30e382be-6f1a-4ff8-bc23-9bb7a55b05b6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827390400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain of thyristor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "30e4389b-6481-4ddb-b4a6-8781a5937f6c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan operasi bagian\r\nbagian utama\r\nmesin bubut \r\nberda<PERSON>kan ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:58:08"}, {"kompetensi_dasar_id": "30e519d0-bcd3-4cf5-83b1-9781747fe566", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar sketsa suatu objek atau benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:33:44"}, {"kompetensi_dasar_id": "30e646d3-9eec-4e03-9cbc-3cf0f31c787b", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 801030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan life review therapy pada lanjut usia partial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "30e68ab8-cf0b-4fae-9a8e-6ec27924abf4", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 821080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara memotong dan membelah kayu dengan gergaji tangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "30e6cbdd-fb76-4f0c-9dca-6d144235fc19", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251170, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> persa<PERSON>an dasar akunta<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "30e7412e-a76a-4186-9593-920daf773da9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843062300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontrasikan pola garap sajian karawitan iringan etnis nusantara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "30e7b263-820a-432b-b8e0-7bba8ab51d1a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis k<PERSON> alat ukur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "30ecd263-2c4d-4ffe-bb02-ad8379cc6a41", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 828210100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> penanganan reservasi individu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "30ed3394-65aa-4b2a-81f9-1655c51799e3", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827350106, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur pen<PERSON> di kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "30edda0a-9fc9-4cad-a42b-8eaafdb902f7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825130200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prosedur pen<PERSON>n kem<PERSON> lahan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "30edfd9b-2cd0-47e4-b2d0-cae2e402b13e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:28"}, {"kompetensi_dasar_id": "30ee986b-e850-4006-8e34-d3e01daf1e28", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843090500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat instalasi tata suara sesuai gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:32"}, {"kompetensi_dasar_id": "30ef7bb6-e27a-4336-b21a-2b7f9cc89e2b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 806010200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem refrigerasi komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2019-11-27 00:29:16", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}, {"kompetensi_dasar_id": "30efdeeb-b732-45d9-9baf-7fc76ba1479d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pembuatan makanan minuman/ bahan bakar/ pengolahan limbah dengan memanfaatkan mikroba", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "30f0976c-69c4-4703-8df8-15e0a83ec4f1", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar Instalasi tenaga listrik satu fasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "30f09f20-c856-4a7b-8c3f-5f147e4e920b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 809020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan persiapan pencetakan satu warna sesuai pesanan cetak dengan teknik cetak tinggi/ flexografi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "30f19d8f-315e-431a-95cc-fb3d6fa1f346", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "30f1f41d-a480-49f8-a3cb-1d422c96e1d2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemesinan gerinda datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:30", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:30"}, {"kompetensi_dasar_id": "30f43488-19d6-43f1-8668-9acc9fcb539a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 839110100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan karya kerajinan fungsi pakai dari berbagai bahan limbah berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "30f4b150-ac37-4159-ae62-38b18946fc33", "id_kompetensi": "4.55", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginstalasi jaringan distribusi pada PLTMH isolated", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "30f55436-ed99-45d0-be1a-b4b05e121a77", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401251340, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan komunikasi bisnis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:46", "updated_at": "2019-11-27 00:29:46", "deleted_at": null, "last_sync": "2019-11-27 00:29:46"}, {"kompetensi_dasar_id": "30f5b37f-c108-4f9e-8087-4e1c80d826a6", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan gambar detail komponen mesin dengan CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:48", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:08:48"}, {"kompetensi_dasar_id": "30f61667-3a8f-4dc4-aebc-6c0554ca350e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:44"}, {"kompetensi_dasar_id": "30f6e404-991b-4667-b78b-8a515014062d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820070200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami cara merawat berkala sistem rem sepeda motor sesuai SOP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "30f79fec-6e30-46ef-894d-b13561a1e721", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pemilihan bahan pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:02", "updated_at": "2019-11-27 00:28:02", "deleted_at": null, "last_sync": "2019-11-27 00:28:02"}, {"kompetensi_dasar_id": "30f83371-3079-4677-92ce-261ab0773b4f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:39"}, {"kompetensi_dasar_id": "30f87cfc-c538-4a34-b773-879699d61edf", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804130700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara pembuatan cetakan pasir silika sesuai spesifikasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "30fa08e2-3898-48cf-91c0-4d080ec6750f", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang fungsi naik dan fungsi turun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:13"}, {"kompetensi_dasar_id": "30fd799d-3638-4c10-9d7a-1a60a4e90a5b", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanaka penyetelan jalur proses bertahap berkelan<PERSON>tan yang meliputi penentuan proses kerja, pemilihan dan penggunaan perkakas tangan dan peralatan serta melaksanakan pengaturan jalur proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "30fdc750-2663-4119-987d-82193b471980", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:54", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:54"}, {"kompetensi_dasar_id": "30fe43c0-bd42-4314-9df4-8b1f01d8f42b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827350700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain time and equation of time", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "30fe7126-178b-43a3-90b0-886e82574dd5", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi kelistrikan PLTS tipe Penerangan Jalan Umum (PJU)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "30fe9a9b-5a22-42bf-9d5b-a7d8b5edba75", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> ident<PERSON><PERSON><PERSON>, dan prosedur pela<PERSON>  pengolahan dan pengujian limbah cair.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:05"}, {"kompetensi_dasar_id": "30fedb6d-0de8-42fb-8569-0ce88b2d5223", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 829020200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan/mendemontrasikan dry cleaning,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2019-06-15 15:07:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "30fef71c-9e81-4218-9d1f-005bdf46c669", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804120300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengelasan pelat pada sambungan sudut posisi vertical (3F) dan posisi atas kepala (4F) dengan las MIG/MAG", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "3100b573-9812-4bb0-a627-edfa74018838", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 814030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menempatkan fasilitas produksi sesuai dengan lokasi yang telah ditentukan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "31015858-e694-4044-a686-10852746dce7", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 840030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara mendekorasi keramik clay body plastis dengan teknik impress", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "31015f95-4d87-4ada-aafb-27a9c72bb877", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukankan macam-macam dan fungsi perlengkapan Alat Pelindung Diri (APD) pada pengecoran manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "3101ce09-da84-43ab-b808-e0d7aee6c47b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melak<PERSON><PERSON> fun<PERSON>i editing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "3101e352-312e-480a-a283-76e991d17a9b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804040110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dokumen kontrak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "3102033e-7d24-4a5a-b631-61b76eb42287", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 800070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan diagnosa kelainan dan penyakit kulit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-10-19 23:19:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "31026408-8946-43f2-9a63-824724239cdc", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melakukan penataan bahan kimia sesuai sifat dan data keselamatan  bahan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "3103e11d-f2fd-4390-a5e0-927c8a4e219f", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 825070110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "3105a0eb-6824-45b7-aa29-498e581d2253", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON><PERSON>  ", "kompetensi_id": 3, "mata_pelajaran_id": 800000107, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON>ir fase E, peserta didik mampu mengidentifikasi komponen utama dan menjelaskan proses kerja motor 2 langkah dan 4 langkah, menerapkan persiapan form pemeriksaan, manual perbaikan, tools, dan SST di tempat kerja, menerapkan pemeriksaan komponen dengan alat sesuai dengan manual perbaikan serta menyimpan hasil pemeriksaan. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:19", "updated_at": "2022-11-10 19:56:55", "deleted_at": null, "last_sync": "2022-11-10 19:56:55"}, {"kompetensi_dasar_id": "310633ef-686c-4986-96e6-e827d41d7d75", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks ilmiah faktual (factual report) lisan dan tulis tentang benda, binatang dan gejala/perist<PERSON><PERSON> alam, terkait dengan <PERSON> pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:05"}, {"kompetensi_dasar_id": "31068e13-51d1-4888-b0c1-3a1fec745587", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kemajemukan bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:48"}, {"kompetensi_dasar_id": "3106e221-3466-4a6f-9db5-9b069e20827b", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 804111000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi program G Code", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "31071ae5-4faf-47d2-bcfe-62d2ac7f2383", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 825050200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis teknik pengembangan agribisnis pupuk dan pestisida hayati", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "310790d8-218d-4817-a09e-d90ce6ecfabb", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       <PERSON><PERSON><PERSON> diit pasien perioperatif", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "31089100-be32-492d-9e33-1812fc44dd03", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Merencanakan sistem sambungan pipa dan komponen\r\npipa\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:24", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:24"}, {"kompetensi_dasar_id": "310979b8-44ab-46c1-b326-29e98101d0f9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 875150020, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan perawatan piston engine fuel systems", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2019-11-27 00:29:53", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "310a00e5-f5a3-44fc-897c-ef00ec5c6522", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemograman  mesin bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan teknik pemograman  mesin bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:59:59"}, {"kompetensi_dasar_id": "310a082f-a3be-48ea-94ae-42ee1cea9016", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menya<PERSON><PERSON>g Air\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:21", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:21"}, {"kompetensi_dasar_id": "310b9466-bafc-4ad4-91fc-53dd48aee837", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 817010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan bentukan gaya geologi dan bentang alam hasil gaya geologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "310bbcc3-36a8-4280-8237-41e7ebe6ddd2", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi kotak kontak biasa(KKB) dan kotak kontak khusus (KKK) sistem satu fasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "310ce858-0218-4f0b-b8ff-cf9d9f190dcb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dan karakteristik jenis kebakaran dalam prosedur pengg<PERSON>an <PERSON>at Pemadam <PERSON> (APAR)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "310da966-e7b9-4fdf-bad7-1ece0931208f", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 300310400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mendemonstrasikan ung<PERSON><PERSON> (<PERSON><PERSON>) dan la<PERSON> (Kinshi) se<PERSON>ai dengan konteks penggunaannya pada teks transaksional lisan dan tulis dengan memperhatikan fungsi sosial, struktur teks, dan unsur keb<PERSON>an", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "310de26e-1ed1-42e8-82b9-25b6f9c2f9c7", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 843061500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan teknik dasar dan pola permainan instrumen secara sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:02", "updated_at": "2019-11-27 00:29:02", "deleted_at": null, "last_sync": "2019-11-27 00:29:02"}, {"kompetensi_dasar_id": "310e0112-3a56-4037-b2be-1c0134cc49ef", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan perawatan kulit wajah secara manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "310e3a7a-ced9-4fa9-b040-840f26676113", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401141600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> se<PERSON><PERSON> el<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "310fd58f-5345-4d6e-ab24-0e7d29c6ae2d", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:54"}, {"kompetensi_dasar_id": "311051d3-7a7d-4aa4-8a8c-3260d4f896a2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 828180110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung biaya perjalanan wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "3112102b-4559-467c-9df4-7825748f3e29", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memresentasikan sikap dan\r\nperilaku wirausahawan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:32"}, {"kompetensi_dasar_id": "31148073-cc01-458b-99eb-ce543da9ad92", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:15"}, {"kompetensi_dasar_id": "311575bd-b1f5-43a3-a40d-c47e04f5427e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bahan, sistem pengoperasian dan perawatan dalam proses <PERSON>inan dan pembekuan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:41"}, {"kompetensi_dasar_id": "311624de-aff1-447e-9fb7-b4a45864a721", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401251620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis public relation dalam bisnis ritel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "3116d757-a64b-4653-b152-ec378f32960c", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 807021000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menciptakan berbagai benda kerja  sheet metal secara mekanik sesuai dengan langkah kerja,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "3116da4d-9327-469c-84ef-af090eeb2bf8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:40"}, {"kompetensi_dasar_id": "31173295-9d69-4dbb-92f7-4548f87e258e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja instalasi kontrol motor berbasis programmable logic control (PLC).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:35", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:35"}, {"kompetensi_dasar_id": "311885cf-0c36-4659-a1b4-7397c7de4c33", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802040900, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis estetika seni audio visual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "311888bd-ac75-4a76-8769-8a44419f39b6", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 807021410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> sifat-sifat bahan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:41", "updated_at": "2019-11-27 00:29:41", "deleted_at": null, "last_sync": "2019-11-27 00:29:41"}, {"kompetensi_dasar_id": "3118ecf7-0f51-48f7-9e5f-7c3fd73d51d0", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara kerja (rancangan) pemasangan penangangkal/penangkap petir (Lighting rod)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "311962f8-8003-4370-b66b-5a02113305c3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 814110200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami prinsip penomoran benang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "311a312b-dff7-4d89-af6d-ad0fc885c9c0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian pengatur model PD (Proportion Defferential), dengan menggunakan penguat operasional (operational amplifier)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "311ac749-6bcf-4c21-bc68-73863ea73b46", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401251102, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan entry saldo kartu piutang, kartu utang, kartu item perlengkapan (supplies), kartu item pelayanan jasa atau kartu item barang dagang, kartu aset tetap pada perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:00", "updated_at": "2019-11-27 00:30:00", "deleted_at": null, "last_sync": "2019-11-27 00:30:00"}, {"kompetensi_dasar_id": "311bc86f-850b-4c68-b3b0-74b1144ce276", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "311bf216-6fbd-4fac-9382-d491fdf80dad", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:51"}, {"kompetensi_dasar_id": "311c2229-9f6e-4df5-bc3c-0773903aec6a", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 818010500, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami Ana<PERSON>nga<PERSON> (AMDAL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:32", "updated_at": "2019-11-27 00:27:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:32"}, {"kompetensi_dasar_id": "311c5fcc-6bfa-46e9-a292-792281913cfb", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:25", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:25"}, {"kompetensi_dasar_id": "311c881c-c9ea-4897-a750-17caf7552fba", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> proposal dan mempraktikkan usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:14"}, {"kompetensi_dasar_id": "311e9208-5e97-4852-ab59-4218b8efdf73", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401251180, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komputerisasi data akuntansi desa/kelurahan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2019-06-15 14:58:24", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "311eb8e7-2e82-4a18-ac79-163ff67783ba", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi performansi unit tata udara domestik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}, {"kompetensi_dasar_id": "311ec3cc-df38-44c2-8ef8-5d7c15131b12", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:28", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:28"}, {"kompetensi_dasar_id": "311fc30d-8bb3-4b72-b384-05b8e08b409b", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> <PERSON> murni, mayor, minor, diminished dan augmented dalam tangga nada mayor 4# - 7#,4b - 7b", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "311fec87-fee1-44d5-a7d7-66c07e9159a5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan order/program kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "31247b3b-d4c6-4ed3-9f3c-39eda8a2148c", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 807021910, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa hasil pembentukan dengan pisual dan template", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "3124e243-87ff-4f45-bc81-d5759580c7e0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:53"}, {"kompetensi_dasar_id": "312533cd-ebae-4ac5-8ea1-b31ac0e7c608", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:18"}, {"kompetensi_dasar_id": "31258450-cec9-4b9c-8639-186e3192c472", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 816030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencelupan kain dari serat sintetis cara suhu tinggi dan tekanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:29", "updated_at": "2019-11-27 00:29:29", "deleted_at": null, "last_sync": "2019-11-27 00:29:29"}, {"kompetensi_dasar_id": "31258fb7-3932-430c-84e8-3f8f12fc4903", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 828160100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan bagaimana membangun pengetahuan produk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "312599d4-3f5e-4db2-b5f9-a36f7cc3cc2d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan alat timbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:03"}, {"kompetensi_dasar_id": "3126110e-bed9-42ba-9df4-767c3d7b3059", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "312773e9-8800-4e6a-a2dc-a023359c9aab", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat peta situasi jalan dan jembatan sesuai spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:27", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:27"}, {"kompetensi_dasar_id": "312bbf5c-77d2-4dd7-8ab9-807bdc59fb4c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem engine manajemen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:08", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:08"}, {"kompetensi_dasar_id": "312bc4e8-3132-400e-9445-38770e5dd372", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengu<PERSON>r tegangan catu daya tinggi pada rangkaian penerima televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "312bd131-6972-4a97-88c0-7fd58ccaab90", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat produk kimia industri skala kecil misalnya sabun mandi/deterjen/parfum/sampo mobil/tinta/semir ban/ semir sepatu/etanol/air minum dan lain-lain sesuai dengan hasil analisis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:38", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 16:08:38"}, {"kompetensi_dasar_id": "312c88cd-f513-4a8c-bca4-766a93002dd7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827290500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan diversifika<PERSON> olahan rumput laut untuk produk pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "312d0dcc-3db7-4727-b8d8-829d6005d3d7", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> keli<PERSON>n sederhana", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:52", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:52"}, {"kompetensi_dasar_id": "312de0e0-ab78-4ec8-ab36-8cc745989c86", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 843080500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> sulukan pada pedalangan utuh dalam cerita Mahabharata dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "312df2ff-621d-4356-8db1-bae3bf7fb7ed", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 801030800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kesiapan peralatan rehabilitasi sosial pada klien yang terintoxikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "312e67c6-20a7-4892-b390-6136bb0da572", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi teknik instrumen (technique of the instrumen) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "31301cde-d611-43f2-8eb8-978de8e0b203", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 803070900, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> kerusakan pada rang<PERSON>an penguat driver elek<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "3130f695-170d-46c7-b390-18af27d49d68", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804160800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi perintah dalam perangkat lunak CAD untuk pembuatan dan pemodifikasian gambar CAD 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "313214bb-a6c1-4c58-8598-b05016a62866", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> diagram satu garis gardu induk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "313336b9-3470-4085-9bfd-5497f5ac8489", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800081100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kelompok unsur-unsur dalam reaksi imunologik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "313422f8-68a1-414b-8cad-c04dff4eede4", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 828190120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemanduan wisata lintas kota", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2019-06-15 14:58:27", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "31346a0f-7c55-4b27-9012-11564890b572", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827350600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part B section I. Rule 4-10", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "313586f5-85cb-4dad-9b2c-b5d6bc124d42", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 817020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": " Melakukan pengujian parameter fluida reservoir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2019-11-27 00:27:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "3137978d-af53-4af2-9865-d4de68c388c5", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyetel kecepatan putaran mesin berdasarkan tabel yang tersedia dan perhitungan untuk proses pemotongan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "3137a018-0e01-431d-bc67-13abafae3153", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah tentang peran pelajar, ma<PERSON><PERSON><PERSON> dan tokoh masyarakat dalam perubahan politik dan ketatanegaraan Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:35"}, {"kompetensi_dasar_id": "31390608-5ad7-445c-9a25-8130f228df83", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 824050900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan aspek-aspek teknis penyutradaraan televisi dan film", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "31399020-c8c4-4b32-a5fa-68c8b4098c44", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 843120400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengombinasikan jenis <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "3139cbb8-b7db-49c4-b510-ca9a54d558ea", "id_kompetensi": "4.45", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat bagian-bagian sipil piko hidro sesuai gambar kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "313e2713-d5f3-468b-9838-5018d0c31759", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan konsep dasar artistic tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "313e59be-ae1c-475b-b68c-294f1b73068f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pen<PERSON>n bahan baku clay body secara manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "313eb7c3-95a0-4cb4-b023-bd577bba3b85", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi dalam perlombaan salah satu nomor atletik (jalan cepat, lari, lompat, dan lempar) dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:44", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:02:44"}, {"kompetensi_dasar_id": "313ed505-d208-4ac6-91cf-ba7246d3247d", "id_kompetensi": "4.2.2", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON> (31): 13-14 dan Q.S. <PERSON> (2): 83 denagn lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:20", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:20"}, {"kompetensi_dasar_id": "313fdd88-ead8-4333-a375-a4a38058a3b6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 831070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> macam-macam desain/model jacket", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "314069f3-d95a-4e02-aedf-3726950bacf2", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:52"}, {"kompetensi_dasar_id": "31416de2-2028-45ae-b1dc-84f0c1b11f1a", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:23"}, {"kompetensi_dasar_id": "31417f45-ac74-44a7-ae44-f3a8288ea594", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> per<PERSON>han dan perkembangan politik masa awal proklamasi dan menyajikannya dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "3141a29d-a5d5-4aea-b1ea-4491f197755e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 815010800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan mesin Open End", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "3142ef7d-5640-426a-9d5d-1cf272466b2f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "31437174-32ed-4ab7-bde8-20e991fd1171", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> konsep per<PERSON>an dalam agama Buddha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:01:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:13:28"}, {"kompetensi_dasar_id": "314394e8-9f38-4716-a007-e107fecb4e8e", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi dan aplikasi Displacement / Measurement sensor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "31464311-17f3-439b-9e41-fa53b06627dc", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802010500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penanganan kesalahan pada program", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "314793f6-480f-442b-b326-91312f1c674c", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks ilmiah faktual (factual report) lisan dan tulis tentang benda, binatang dan gejala/perist<PERSON><PERSON> alam, terkait dengan <PERSON> pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:32"}, {"kompetensi_dasar_id": "3147a3e1-d382-477f-9e69-941591bed397", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 823060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang reaktor pirolisis dan destilator asap cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:37", "updated_at": "2019-11-27 00:27:37", "deleted_at": null, "last_sync": "2019-11-27 00:27:37"}, {"kompetensi_dasar_id": "3147d379-023f-4bf5-aa7c-b9d70fdb4d2a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 831050500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hiasan sulaman pada busana", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "314891ed-8b8c-47d9-a5d7-595745daf90f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 800020600, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komunikasi terapeutik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "31491207-f7ed-483f-a6c6-737420179cd7", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "mengevaluasi kegunaan artistic tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "314a42ea-c33e-4e00-ab7d-cdcfc489d49e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami akor trisuara dalam penulisan akor empat suara dalam dua paranada", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "314af500-dc56-433e-b449-07aeae41f491", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahan baku dan bahan pembantu mengi<PERSON>ti stok<PERSON>, si<PERSON><PERSON> kimia fisika bahan dan intruksi kerja dari industri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:30"}, {"kompetensi_dasar_id": "314b3620-08c9-41dd-bbf0-8abcd7a3471a", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengubah color temperature", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "314b60fd-c535-43d8-b1f2-333b73467bfd", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>un<PERSON><PERSON><PERSON> sikap dan perilaku se<PERSON>ai den<PERSON> (Satya dan <PERSON>)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:04:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:04:07"}, {"kompetensi_dasar_id": "314f6c6b-e9a6-4e05-9982-130ed9c0dc5d", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Fuels system", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "3150c7d3-bb1b-4b63-81bd-013434a98878", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 831050510, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan sulaman bordir dalam suatu produk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:36", "updated_at": "2019-11-27 00:30:36", "deleted_at": null, "last_sync": "2019-11-27 00:30:36"}, {"kompetensi_dasar_id": "3152204c-ef7d-44d3-bb61-38b136325d32", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perawatan/ maintenance boiler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "31524047-9dbf-41c0-a6f9-a7a98fe6e456", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:33", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:33"}, {"kompetensi_dasar_id": "3152458c-3529-418e-9c87-a29c432561fb", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 800090200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi narkotika dan psikotropika serta penyalahgunaannya berdasarkan undang-undang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "3152bcdf-1190-4155-aad2-e780d45fbb1f", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengevaluasi pemberian pakan dan air minum ternak ruminansia pedaging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "3156067b-885e-43b3-b924-a36fb0275e30", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> sistem bahan bakar in<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:09", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:09"}, {"kompetensi_dasar_id": "31562c26-2ab9-4476-a88b-547846f5d22b", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825010220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan alat pelindung diri (APD) untuk pekerjaan pengelasan dengan las gas dan las listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "31566ed7-94fe-4571-8663-9ed7493f2236", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 831040400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> unsur  desain pada benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "31572e07-5a46-43f0-983b-20935980eb27", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:52", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:52"}, {"kompetensi_dasar_id": "31579d42-b33c-4781-82a3-8e7f65921301", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis makroskopis dan mikroskopis faeces", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:19", "updated_at": "2019-11-27 00:30:19", "deleted_at": null, "last_sync": "2019-11-27 00:30:19"}, {"kompetensi_dasar_id": "31585227-f32d-45e7-9784-a9b487e268f1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:13"}, {"kompetensi_dasar_id": "3158be25-ee31-4452-8188-b7a6a06e21fe", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 826130100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan teknik panen agroforestry", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "315a945d-f5cf-48e6-9879-66dcd0faf952", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843080200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis tugas-tug<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "315b245c-2e96-403c-99be-fec770cb8f2c", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 820100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menjelaskan cara kerja engine 4 langkah ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:44"}, {"kompetensi_dasar_id": "315dcb78-caa9-4d34-ac8f-d6668010c4d8", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 806010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeliharaan alat penukar kalor unit refrigerasi komersial berskala menengah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "315ec46e-acd9-4f09-a55f-8f704e73fe5b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 840020140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penerapan teknik dekorasi pierching (toreh tembus) benda keramik teknik putar pilin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "31607d5c-737b-479a-a036-a32e9573fe87", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800061100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemberian obat umum kepada pasien di klinik gigi pasca tindakan medis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:27"}, {"kompetensi_dasar_id": "3160b833-86f0-45cf-b383-ce63f994ecc2", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401110000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan identifikasi dampak dari penggunaan polimer di bidang pariwisata", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "3160e045-8ffb-46c2-929b-a9f96bb01779", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 817080100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati tipe aliran dalam dalam pipa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "31640dd2-64e5-43f1-b1dd-c4d68d5a86bb", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401251400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis kelayakan bisnis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:46", "updated_at": "2019-11-27 00:29:46", "deleted_at": null, "last_sync": "2019-11-27 00:29:46"}, {"kompetensi_dasar_id": "316484f4-e1de-45b2-9887-d0ea443c019d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>r nilai-nilai demokrasi pada konteks lokal dan global mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:02"}, {"kompetensi_dasar_id": "3164a345-a307-4e8a-972c-ca24168b772b", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 828190130, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penghitungan tarif normal secara manual dan/atau Airline Distribution System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "316654ef-0eb0-4015-8332-72ba42e186fb", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804011000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat boks peralatan elektronika sesuai pesanan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "31687db6-de2d-4c59-a632-42d19c9f4670", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 801030700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis permas<PERSON>han bimbingan konseling keluarga lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "3168fed9-bf0e-4757-ba4a-a4802f8799f8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821090320, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa gambar konstruksi tengah kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "3169f823-2c9b-4abd-a2bf-932cb273a7dc", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 828031100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan berbagai jenis uang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "316aaf7c-dd17-4a65-afe5-53b8cbb6d5a5", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 824050900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyutradarai pada karya audio visual untuk program televisi  non drama menggunakan single camera system", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "316b090b-741b-4508-b88a-cdf3e9537f00", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 843050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan aransemen empat suara ke aransemen yang lebih kompleks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:06", "updated_at": "2019-11-27 00:28:06", "deleted_at": null, "last_sync": "2019-11-27 00:28:06"}, {"kompetensi_dasar_id": "316be193-543d-4cba-8152-141cc31ed385", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 828190100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>  w<PERSON><PERSON> (tour leader G<PERSON>)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "316d1234-f431-4e5b-8b6d-43d5a674779b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pembers<PERSON>an lahan de<PERSON> taman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "316db34a-db3d-404f-a5ca-6f3d0b4d224f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:16"}, {"kompetensi_dasar_id": "316ed744-d110-45a0-99d8-3d345e80d819", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Meneladani pribadi Yesus Kristus yang rela menderita , se<PERSON><PERSON><PERSON>, wa<PERSON><PERSON>, dan bangkit demi kebahagiaan manusia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "316fb5b9-047e-45d8-91ab-92d42362ba02", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gambar konstruksi bangunan air dan bangunan pertanian dan komponen alat mesin pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "316fc1a4-25f8-4ad7-9832-4bb99f4bb339", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 801010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hiera<PERSON> hukum di <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:04", "updated_at": "2019-06-15 15:07:04", "deleted_at": null, "last_sync": "2019-06-15 15:07:04"}, {"kompetensi_dasar_id": "3170872e-0592-4185-9a94-0b972485834b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempertunjukkan  prosedur loating repair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "31713513-8dba-4999-bfa3-fdb4c5bee09c", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam rangkaian elektronika  digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "317178c3-0b03-450c-9064-4f0111ac2b3b", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 822100110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosa Sistem Panel instrumen cluster (dasbord) Kendaraan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "31718ace-3857-4726-b704-d4fd4730560e", "id_kompetensi": "3.1", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan titrasi penetralan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "31728006-b249-4d54-990b-daede0b7e3e8", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 817020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis mekanisme pendorongan dalam reservoir", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "3172c3d3-c5d6-4545-a8a4-d2b89f060502", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyusun teks interaksi transaksional lisan dan tulis yang melibatkan tindakan member dan meminta informasi terkait perbandingan kata sifat dengan memperhatikan fungsi social, struktur teks dan unsur kebahasaan yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "31734a0f-fb13-4cc3-abed-abb30ae4e5ae", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 830120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan pengelompokan berbagai teknik dan desain nail art", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:34", "updated_at": "2019-11-27 00:30:34", "deleted_at": null, "last_sync": "2019-11-27 00:30:34"}, {"kompetensi_dasar_id": "31736b9e-fd3f-41d4-ad04-368e75550bf0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 815010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> lap hasil mesin Blowing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "3173fcab-5d88-44c1-96da-a1971fbd3d85", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827390500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Show properties and uses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "317528fb-144e-4ae9-bca1-727f837702d1", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan permas<PERSON>/ isu dari berbagai sudut pandang yang dilengkapi argumen dalam berdebat berkaitan dengan bidang pekerjaan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:59"}, {"kompetensi_dasar_id": "3175b8d9-9a53-4fdc-9a25-abb64c233486", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 821090500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> dan menerapkan tatacara  outfitting kapal fiberglass", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "31765d00-57f1-451e-bec8-23f62af5124b", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengintegrasik<PERSON> makna <PERSON> (Yi) sebagai jalan bagi manusia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:04:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:04:19"}, {"kompetensi_dasar_id": "3176a812-6536-4b01-ac56-76c0249bf261", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 811010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi montase (montage) film isi majalah warna separasi secara manual dan elektronik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "317786f8-7263-4a9c-984a-2dc7d3f56d9a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:37"}, {"kompetensi_dasar_id": "317811c4-a285-448d-ac76-ab067afea576", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:05"}, {"kompetensi_dasar_id": "3178786d-a6a7-4fa1-8f75-58f3b281752d", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 843040400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis jenis tata rias panggung dengan peran dalam pementasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "3178e08a-9a5e-41c3-8dff-76885e3c2672", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804051200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan komponen furnitur dengan peralatan tangan.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:41", "updated_at": "2019-11-27 00:28:41", "deleted_at": null, "last_sync": "2019-11-27 00:28:41"}, {"kompetensi_dasar_id": "317a6bb1-291f-414a-97d9-a9b33ec075e7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, menyajikan model mate<PERSON>ika dan menye<PERSON><PERSON><PERSON> masalah keseharian yang berkaitan dengan barisan dan deret aritmetika, geometri dan yang la<PERSON>ya.", "kompetensi_dasar_alias": "<p><PERSON>y<span>elesaikan\r\nmasalah yang&nbsp;</span>berkaitan dengan\r\npenyajian\r\ndata hasil pengukuran\r\ndan\r\npencacahan dalam tabel distribusi frekuensi\r\ndan histogram</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:44", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:44"}, {"kompetensi_dasar_id": "317a7d79-f578-4b9c-a3d0-04b89080cd2b", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 802031700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta produk multimedia interaktif berbasis waktu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "317a9dad-1e5e-4c0d-80b0-4fb8db82f0ad", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:16"}, {"kompetensi_dasar_id": "317b5525-8d9c-4bc6-9942-226ec5849ecd", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 827350400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Identified weather forecasting", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:12", "updated_at": "2019-11-27 00:29:12", "deleted_at": null, "last_sync": "2019-11-27 00:29:12"}, {"kompetensi_dasar_id": "317cb4ae-288b-4ba2-8803-6d8d9e6b7158", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 815010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil pen<PERSON> lap mesin Blowing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "317d6b57-7479-48fb-a97b-45f7ff446d1b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cara pengikatan ke belakang metoda Cassini pada pengukuran lahan pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "317dc929-53ea-41f0-a6fe-959a06b58507", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan konsep dan aturan menggambar proyeksi dan isometri dalam peker<PERSON>an plumbing", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:26", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:26"}, {"kompetensi_dasar_id": "317e1265-fa2e-4090-b9d0-a7930fb6f9e4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 822190500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan aspek-aspek pengelolaan Unit PLTB skala kecil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2019-11-27 00:27:34", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "317e47be-e1a9-4b60-9562-a3067cda8c31", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> ma<PERSON><PERSON> (per<PERSON><PERSON> bakti) sebagai pokok kebajikan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:57:49"}, {"kompetensi_dasar_id": "3181810f-74cf-483f-a267-faf042031c9f", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 823030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan reaktor biogas rumah pada awal dan rutin harian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:17", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:51:17"}, {"kompetensi_dasar_id": "3181c368-5791-4cfd-b955-7d26018fa168", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "3183beb9-cd78-4a32-ba1d-d3097918ab3b", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 804040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung RAB pada pekerjaan perawatan bangunan gedung yang tergolong rehabilitasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "3183f515-8392-44f3-95d6-536162316f29", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 829010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penanganan hal-hal khusus di kamar (cotigency skill)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2019-06-15 15:07:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "318402ea-4765-4d65-ac56-2118cdb5ff8b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis peranan Agama Buddha dalam ilmu pengetahuan dan teknologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:57:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:57:22"}, {"kompetensi_dasar_id": "318434da-8a38-444a-89b5-1920485b65d6", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 875150020, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  Engine Storage and Preservation", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "3184fb3d-92e7-47c3-8176-d443478a5c22", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "3185a81e-8e6a-4fa8-803f-f6e5375e4594", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik dan prinsip penggunaan alat gambar teknik manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "31864548-c8ea-4541-b249-cb9884999c7b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:29"}, {"kompetensi_dasar_id": "3186c4bd-e1b9-4bda-b637-1ee2183d57d0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan prosedur <PERSON> dan <PERSON><PERSON><PERSON> serta <PERSON>n Hidup dalam pelaksanaan pekerjaan <PERSON> Bangunan Gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "3187c7e1-3fd9-4636-af46-7b692ce6b729", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 824060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> biaya produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "3188be56-e5ee-42df-b04a-5adf71773267", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827350100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penggunaan alat navigasi elektronis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:13", "updated_at": "2019-06-15 15:07:13", "deleted_at": null, "last_sync": "2019-06-15 15:07:13"}, {"kompetensi_dasar_id": "31896bc6-86d7-4d1a-b8df-e6db481235fe", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi dalam perlombaan salah satu nomor atletik (jalan cepat, lari, lompat, dan lempar) dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:29", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:29"}, {"kompetensi_dasar_id": "318a589b-f97a-4020-9332-4a585cee2e51", "id_kompetensi": "4.36", "kompetensi_id": 2, "mata_pelajaran_id": 821200300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur cat duco", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "318d2a62-85ea-4cc9-af63-38e32dc4a101", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827190100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekayasa teknik pemijahan ikan  (alami, semi buatan, buatan)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "318e1bf9-21cf-429e-bff4-8886f132bea9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:35"}, {"kompetensi_dasar_id": "31911c4d-23a1-466f-9df5-bcca64611815", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur keselamatan dan kesehatan kerja serta lingkungan hidup K3LH  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "31914777-6e17-40cd-97aa-9659f21ca6db", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan nilai-nilai dan isi yang terkandung dalam cerita rakyat (hikayat) baik lisan maupun tulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:28"}, {"kompetensi_dasar_id": "3193f3d2-7126-4c29-88da-38279a730479", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 829060600, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan Daftar Angka Kecukupan Gizi (AKG)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-11-27 00:30:31", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "31945fda-cb15-470e-ad66-e6d26b988d00", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "3196acd1-15cf-466d-a789-19ce3649b4f5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin bubut, dengan su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:59"}, {"kompetensi_dasar_id": "3197b201-d10e-4e92-8f01-2a174f561fc1", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 803070800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  komponen pendukung sitem minimum mikrokontroler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "31980346-cd52-4765-b732-152b66132dfe", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:54", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:54"}, {"kompetensi_dasar_id": "31987521-972e-484d-b234-e45d8b23d2a4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825030400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Memahami teknik penyiraman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "31988fd2-599c-47d4-af6f-86f5f378b629", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi instalasi listrik kawasan be<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:30", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:04:30"}, {"kompetensi_dasar_id": "31994eca-0122-42e1-9f8f-06bb05613e2d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaplikasikan lagu jaman <PERSON>ok tingkat menengah untuk instrumen spesialisasinya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "319b1744-e0d2-488f-a728-e5e98f23e73a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 801030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pelayanan Rehabilitasi Sosial korban NAPZA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "319b2190-a86d-4ec3-ba66-c1da989e3d6a", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 843090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan nilai estetik artistik tari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "319b630f-39bc-40f8-bb7c-cc875227ccd2", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825020800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyiapan lahan tanaman pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2019-11-27 00:27:40", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "319b6504-4eb7-4af8-9f44-6170ddb86ee1", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827190110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penebaran benih pada pendederan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "319d0945-c7e6-4f87-8e8c-ee877a785c63", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "319d38ad-ff69-4f42-86a2-08a02422bff5", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 401251180, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pencatatan  akuntansi kewajiban satker, dan akuntansi kewajiban desa/kel<PERSON>han", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "319d5cd5-ffe0-46e5-96e7-a81c701b1ddd", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401141600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sediaan suspensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2019-11-27 00:30:23", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "319e14cb-4a59-452c-865b-e57157eba412", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:50", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:03:50"}, {"kompetensi_dasar_id": "319e6df3-0e04-46a3-8183-2ff64170665b", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 843070110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasi jenis non ritmis vokal karawitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "319e8b31-6ca3-451d-b61a-6d9d92a11bd9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian pengontrol suhu dengan menggunakan penguat operasional (operational amplifier)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "319ec0b4-7aa4-4b72-bd21-b239b1986343", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825200100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pemeriksaan kualitas daging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "319fe4a4-eff2-4a9b-baa2-04a9b1201d37", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi jasa bank lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2019-06-15 15:07:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "31a0f29d-057a-4f85-92d2-2cc39c77a05a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "dMelakukan perawatan peralatan listrik yang menggunakan motor listrik DC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "31a2b94e-a227-4a41-994d-dea112336c9d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> gaya dalam struktur bangunan dengan cara analitis maupun grafis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "31a2be47-0813-4a9a-8f23-0ce2d611dcda", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:43"}, {"kompetensi_dasar_id": "31a4b435-1771-430e-b8a9-d4756cb92d51", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:59"}, {"kompetensi_dasar_id": "31a5cd72-cfa0-41e4-9b33-108fb2111b20", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 817140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur operasi reaksi kimia pada unit absorber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "31a77db6-06a7-43cc-8214-0ffc87fa1a5a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membuat prosedur peker<PERSON>an \r\npembubutan berdasarkan \r\ngambar kerja ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:28", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:28"}, {"kompetensi_dasar_id": "31a86b56-3e96-426e-a3b2-3e1acba9d15a", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:15"}, {"kompetensi_dasar_id": "31a8f326-dadc-4603-8754-f70cfc7bd386", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821090310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pekerjaan gambar Mouldloft kapal <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "31a9122d-050e-407a-968f-2a3ed8d3bbba", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "31aaa771-f844-4579-b7ce-ef725cb73ede", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 814031400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON> macam-macam dokumen muatan angkutan kereta api", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "31ab603a-f11e-4d7b-81b7-ac952b7c9903", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:44"}, {"kompetensi_dasar_id": "31add3b3-0017-4948-9a2b-185c8956a862", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 825210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kualitas dalam seleksi benih dan prosedur penebaran benih pada pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "31adea0f-97e3-4f9f-be2e-b1ec23b60990", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827350900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan crane sling, boat swain store dan carpenter store", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2019-06-15 14:58:08", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "31ae4e39-7138-4c39-b654-e21d1a989581", "id_kompetensi": "Pengelolaan Laboratorium Kimia ", "kompetensi_id": 3, "mata_pelajaran_id": 800000113, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu mengelola bahan dan fasilitas laboratorium, termasuk Laboratorium Gas, Laboratorium Oil, Laboratorium Water dan laboratorium Polymer yang masing-masing mempunyai karakteristik yang berbeda. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "31aee78c-f029-4959-87e2-9a6e1c6cad73", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "31af0671-cb07-4dc0-87ba-4ec52459c714", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 300310700, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengidentifikasikan nama nama arah ( 方向 ), letak suatu tempat dan benda sesuai dengan konteks penggunaannya, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:12", "updated_at": "2019-11-27 00:30:12", "deleted_at": null, "last_sync": "2019-11-27 00:30:12"}, {"kompetensi_dasar_id": "31af80a0-7136-4369-a249-35f8f73d410e", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804050400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> spesif<PERSON> dan karakteristik cat pada konstruksi bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "31af998f-54e6-4232-9527-53cb4fdbd595", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan percobaan rangkaian arus searah (DC) dan menyajikan data hasil percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "31b06035-3c9e-4b3c-906a-112d99a90e70", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 817090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> kick", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "31b0f105-db84-472e-806d-fc6339eef576", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401251030, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembentukan kas kecil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "31b343c3-76ed-4543-ae3d-30a0efc34210", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825063600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis seleksi dan culling ternak unggas petelur fase starter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:11", "updated_at": "2019-11-27 00:28:11", "deleted_at": null, "last_sync": "2019-11-27 00:28:11"}, {"kompetensi_dasar_id": "31b369ee-f865-4e6b-bffc-3984413788f5", "id_kompetensi": "Keselama<PERSON> dan <PERSON><PERSON><PERSON><PERSON> serta <PERSON> (K3LH) dan budaya kerja industri ", "kompetensi_id": 3, "mata_pelajaran_id": 800000107, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu menerapkan K3LH dan budaya kerja industri, antara lain: praktik-praktik kerja yang aman, bahaya-bahaya di tempat kerja, prose<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>aan da<PERSON>, dan penerapan budaya kerja industri (Ringkas, Rapi, Resik, Rawat, Rajin). ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:18", "updated_at": "2022-11-10 19:56:55", "deleted_at": null, "last_sync": "2022-11-10 19:56:55"}, {"kompetensi_dasar_id": "31b61869-de57-4620-afbf-ea963dad659c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dan prosedur kerja alat dan mesin produksi pertanian, laboratorium, klimatologi, penyimpanan dan prosesing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "31b7636d-b2e9-44f4-be11-7cb6e4c3f5f3", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:24"}, {"kompetensi_dasar_id": "31b871e1-e230-4cc3-aca8-92a7bdf14677", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843062300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan sistem notasi karawitan etnis nusantara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2019-06-15 14:58:19", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "31b8b4ad-105b-44ab-b05e-c9a9c6bc9ae4", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807021500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar simbol listrik dan elektronika Menerapkan gambar symbol listrik dan elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "31b9d2cc-bb5e-41b8-85c4-d35019e45019", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843062300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi repertoar  gending/lagu kategori dasar pada karawitan etnis nusantara mandiri dan iringan dalam berbagai irama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "31babf3b-5934-46ee-a68c-cffe6cda38cd", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis konsep desain/\r\nprototype dan kemasan produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:17"}, {"kompetensi_dasar_id": "31baedd8-56a9-4a21-bf7b-111d5e7ffef6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.1\tMengidentifikasi alat potong mesin frais", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "31bb4750-4632-4f7e-b225-1bbd759e0c3c", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 800080230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan komunikasi dengan kolega dan pelanggan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "31bb95bb-c18b-4c3a-a80f-493fc14f770c", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur teknik membubut eksentrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "31bf8b69-656c-435c-b549-92ea1e2852a9", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan workshop equipment", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:44"}, {"kompetensi_dasar_id": "31c10479-f7da-47ea-8444-f922db6cf67d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:35", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:35"}, {"kompetensi_dasar_id": "31c1d722-9a0b-4953-bdfc-3e6a55f82457", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:20", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:20"}, {"kompetensi_dasar_id": "31c23297-c654-4896-8ad0-44ad3b08b7a6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:15"}, {"kompetensi_dasar_id": "31c25673-d414-4a25-9ad7-56a768a6e5e7", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 822050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyiapkan peralatan kerja dan robot yang akan dioperasikan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "31c2eefd-c44e-420a-887c-665af6e943e7", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:46", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:46"}, {"kompetensi_dasar_id": "31c3c7fe-b741-4733-8e8e-981c930e5312", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>ak<PERSON><PERSON> n<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "31c46d2e-58b8-4941-a25d-99955c094453", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:31"}, {"kompetensi_dasar_id": "31c57506-0ca4-4d38-839e-ba950bccc3a3", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 828190130, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  pemesanan tiket penerbangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "31c680c8-7998-4552-86c1-0e5942d25a3f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:12", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:12"}, {"kompetensi_dasar_id": "31c83e23-2310-46eb-a62a-25311b7acfcf", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:22", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:22"}, {"kompetensi_dasar_id": "31c848bb-f7f7-478f-abcc-97e292ef31b0", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> berb<PERSON>i jenis jacking, blocking dan lifting", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "31c96d93-de0a-40c9-a2aa-6e5fe51e39f0", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:18", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:07:18"}, {"kompetensi_dasar_id": "31ca2b79-b9a5-43c4-9631-d51ff1d9f85f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827110340, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> perlakuan panas terhadap logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "31cb594e-5c28-4760-9420-dde7d0bbd2e6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:32"}, {"kompetensi_dasar_id": "31cba77d-55b9-49ba-84e4-7dcdde0e0aa4", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "31cc38a0-e6be-4ee0-b204-8e8ace590e5d", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 843070300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan ornamentasi vocal ritmis dengan menggunakan iringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "31cd3852-7a43-46e9-8ef5-a49924611d96", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 807022700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis parameter pemotongan di mesin frais", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "31d00cd2-5cf6-4c6f-aba4-5fef8c9ca369", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:46", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:46"}, {"kompetensi_dasar_id": "31d13adc-5943-4417-aa55-f6299fb84a8f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja macam-macam tranducer pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "31d1612f-04ae-4a3c-bf9e-214fc4189958", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:22"}, {"kompetensi_dasar_id": "31d242e2-6bb9-4bd2-8a85-7d1eaa7c1e14", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:07", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:07"}, {"kompetensi_dasar_id": "31d4dc43-2f62-487e-85d3-95eaaeecd2d4", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 841010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Teknik dan langkah pembuatan perhiasan berbatu permata teknik ikatan pave", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "31d58762-807c-4364-885e-b3ba592f5e84", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:37"}, {"kompetensi_dasar_id": "31d5cf22-afb7-4b2e-bb03-f833935d6fe9", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:34", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:34"}, {"kompetensi_dasar_id": "31d61a8d-8b88-415a-955e-feae5b272dd3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengadministrasian fasilitas dan aktifitas laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "31d643b6-4c97-4645-9130-7e4ba574744a", "id_kompetensi": "4.31", "kompetensi_id": 2, "mata_pelajaran_id": 825100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan pompa air disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "31d72254-b6a3-4e53-8ebf-bd3e19123168", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 831070700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pola celana panjang sesuai desain.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "31d7e775-71d6-4685-9ad8-9193f7ba8f87", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 805010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengukuran konstruksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "31d93605-a2cf-4310-8ba6-5b2600144ae1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:25", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:25"}, {"kompetensi_dasar_id": "31db35ea-390e-48c6-8644-bb5ac8ed75e9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:10"}, {"kompetensi_dasar_id": "31dd04b9-1ad7-43a9-acea-593da78532de", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 839010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan keselamatan kerja meliputi kecelakaan kerja, api dan kebakaran dan alat pelindung kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "31dd05e2-22bd-4ad3-a9be-16de47032c6b", "id_kompetensi": "Proses dan teknik dasar pengoperasian alat dan mesin penanganan dan pengolahan hasil pertanian ", "kompetensi_id": 3, "mata_pelajaran_id": 800000131, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>da a<PERSON>ir fase <PERSON>, pese<PERSON> didik mampu memahami konsep, p<PERSON><PERSON><PERSON>, dan prosedur melalui praktik terbatas pengolahan hasil pertanian (nabati, hewani, dan ikan) untuk menghasilkan produk setengah jadi (bahan baku) atau produk jadi; proses dasar meliputi: pen<PERSON><PERSON><PERSON> ukuran (pemotongan, pengirisan, pemarutan, pencacahan, penghancuran, dan penggilingan), proses termal (pendinginan, pembekuan, pasteurisasi, sterilisasi, pengeringan, pemanggangan, penyangraian, dan penggorengan), proses kimia dan biokimia (penggaraman, penggulaan, pengasaman/fermentasi), dan proses pemisahan (pengayakan, penyaringan, destilasi, ekstraksi, pengendapan, penggumpalan dan evaporasi). ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:21", "updated_at": "2022-11-10 19:56:58", "deleted_at": null, "last_sync": "2022-11-10 19:56:58"}, {"kompetensi_dasar_id": "31df0998-4ef1-45e1-ad40-e0a06fa8c2b9", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pem<PERSON><PERSON><PERSON> bakteri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "31df1075-1347-4ea4-be16-ee5a92b77c97", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.1\tMengidentifikasi mesin frais ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:57", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:27:57"}, {"kompetensi_dasar_id": "31df846c-b40b-4bd3-8793-8da4017801fd", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401110200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengembangan bioteknologi serta manfaat dan dampaknya dalam masyarakat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:24", "updated_at": "2019-12-13 08:07:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:24"}, {"kompetensi_dasar_id": "31df99a0-7243-432a-8a62-6d933c0d96f2", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 827380100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dasar-dasar elektronika di kapal niaga.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "31e02273-cde3-4e5f-bd6b-3d79422aec6f", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800081200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan Glukosa Darah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "31e08f8c-3716-465d-ab7e-2ef8fa002213", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "31e1f828-8eae-4dc5-a04a-731c65541bb9", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan titrasi pen<PERSON> (argentometri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:28", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:27:28"}, {"kompetensi_dasar_id": "31e2fe64-4d16-44d6-a695-901ab86be87d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip K3LH mengikuti SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:00", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:00"}, {"kompetensi_dasar_id": "31e38de6-1fe4-41ab-a294-99f2f08b9c51", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 801031300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan interaksi secara efektif terhadap penyandang disabilitas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "31e3b0db-94ce-480a-8c8a-c3b090f32b79", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 802040700, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasikan segmentasi audien", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "31e3dbf5-806a-4865-b2e4-ab91b3809dbf", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan eksperimen rang<PERSON> penghitung (counter)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "31e4434f-6b45-4358-9a0c-f9325fa311dc", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat motif tenun satin dengan ATBM", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "31e776b3-695c-4bfb-9140-442d2b971715", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 828190110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencari informasi pemanduan tentang ekowisata pelestarian alam <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:52", "updated_at": "2019-06-15 14:52:52", "deleted_at": null, "last_sync": "2019-06-15 14:52:52"}, {"kompetensi_dasar_id": "31e8ba1f-f7fc-4467-a3c2-18b6a29597b6", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap hidup se<PERSON>ai dengan <PERSON> (Zhongyong)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "31e9d7e4-a329-42bd-84a7-c5e0794ade45", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 816040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki hasil proses pembuatan screen bermotif", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "31eac920-a7c2-4882-a2ab-c777f096dd04", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 826080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penerapan teknologi  Sistem Informasi Geografis (SIG) terapan dalam  inventarisasi  keanekaragaman hayati", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "31ebd3a0-e39d-4811-bf14-65932e6df819", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses pencel<PERSON>an benang dan kain protein", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "31ecb096-8f9b-4264-aebd-664038290eff", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 802032700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan modifikasi gambar obyek 3 dimensi dengan perangkat lunak dengan tepat dan efektif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:52", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:08:52"}, {"kompetensi_dasar_id": "31ed7274-7eb4-4acb-9139-18738e4d5d1b", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 807022700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang benda kerja alur ekor burung luar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "31ee1471-5c13-4af0-b2e8-619b1e1e5d83", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "31eecfe3-1a4e-45fb-a875-3984b26eb78b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencana tata letak  komponen Instalasi penerangan pada bangunan sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "31effadd-a0b5-4c09-b16c-249455a6ada6", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 807020510, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses membongkar rivet (removing head rivet)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "31f02a58-5fe7-43c6-9e7d-11307ca9aed6", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827270100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, menalar dan menyaji pemanenan kerang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "31f1011b-059b-4747-9404-242299ee41e3", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> jenis alat dan bahan cetak offset gulungan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "31f59029-fd76-4351-a72d-5a09e0f53465", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>ren<PERSON><PERSON><PERSON> rangkaian pembangkit gelombang non sinus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "31fb5d3f-b488-47fd-a711-ecf9719b7bb5", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 822090110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki  Sistem  Kontrol Elektronik dari  ESP (Electronic Stability Programs)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "31fbf7dc-ff79-49be-9d41-177271026d14", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:39"}, {"kompetensi_dasar_id": "31fdfc98-1f49-4aed-9900-a179039a1aa0", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 821070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi cara mencari panjang garis, bend<PERSON>, bidang dan konstruksi kapal sederhana sebenarnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "31fe9d3e-e6ed-405b-a3ff-f0a48cf18fcd", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 802040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis rancangan imposisi beberapa halaman isi barang cetakan (tabloid, majalah, dan buku pelajaran)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "31ff617a-35a0-4f23-a684-91c56fc58248", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804101300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi jenis-jenis energy", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "32006565-4b7e-4747-93a5-8d11a09aaa37", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:22"}, {"kompetensi_dasar_id": "32017472-8d40-41b6-8685-0fb46a084980", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengendalian proses  pada sitem proses terbuka  dan  sistem tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:19"}, {"kompetensi_dasar_id": "3202ea9f-5e3f-44ec-aa92-447622c4a41c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi dalam simulasi perlombaan salah satu nomor atletik (jalan cepat, lari, lompat dan lempar)yang disusun sesuai peraturan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:48", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:48"}, {"kompetensi_dasar_id": "32035ccc-8301-419a-996b-abf2ed04d4a8", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 825061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON> laporan usaha ternak unggas petelur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:08", "updated_at": "2019-11-27 00:28:08", "deleted_at": null, "last_sync": "2019-11-27 00:28:08"}, {"kompetensi_dasar_id": "32038441-6553-4e78-b725-abec25d16d83", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 800090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bakteri yang ada dalam lapisan plak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "3203d95f-3fcf-4b41-a41d-ef72f252c9ac", "id_kompetensi": "3.44", "kompetensi_id": 1, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan diagram rang<PERSON>an robot/mps", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "3203e423-fadb-4e40-9650-f55f796ca167", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan karakter dan kesuburan tanah pada tapak untuk penggunaan dan keberlangsungan tapak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "32054a0d-a084-4a87-ab8c-9dddf975098f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:35"}, {"kompetensi_dasar_id": "3207b707-f942-4750-98e7-98f8745a062b", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800080230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kesehatan lingkungan kerja di laboratorium medik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "3207edce-004c-424b-9f7b-db8f746c3cbf", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:18"}, {"kompetensi_dasar_id": "320884fb-1de5-402d-873f-e00b8093ad7a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804011400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menentukan jenis dan karakteristik bahan logam ferro", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "320bc75f-9d7c-4548-82ed-88527e70360e", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan material dan bahan kimia  sesuai SOP.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "320e0374-ffc2-469d-8c96-7b74a86af00d", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 802010500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data pada file", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "320f0ffe-edbf-46d4-919c-747f5a02418e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804130700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat cetakan sesuai spesifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "320fa13a-e955-4a55-bbaf-021130b37f87", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami tujuan kalibrasi instrumentasi medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "3211d8f8-c00e-418a-9ce7-fbedfe874b96", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 816010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan mesin tenun teropong", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:50", "updated_at": "2019-11-27 00:28:50", "deleted_at": null, "last_sync": "2019-11-27 00:28:50"}, {"kompetensi_dasar_id": "32127250-61e9-4039-98d5-a9f7fb9ec666", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:16"}, {"kompetensi_dasar_id": "3213ca9b-3d03-4183-81ab-a9b3f4a50826", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 828120300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "32155e6d-c640-46b2-89df-dd83498814a8", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 802040700, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bentuk-bentuk komunikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "3217bc48-e9d5-427c-a376-145901206852", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan ragam cengkok/motif dasar permaianan instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2019-11-27 00:29:05", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "32181a06-be14-4e15-ad68-488a32c16bec", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "321843d1-1e55-45b6-9052-c5c52913580e", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401131600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis kadar protein", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "3218c1f4-ac02-43cd-86b2-1b96bc8e2ec7", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor yang mempengaruhi struktur bangunan berdasarkan kriteria desain dan pembebanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "321998a7-3909-487a-8ad3-2616fb79c147", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "321ac4e6-c4ec-4846-800c-5d864637b4ad", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 814110200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan proses pembuatan kain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "321b533f-1550-4946-9b1d-86f880fe1b26", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan operasi bagian\r\nbagian utama\r\nmesin bubut \r\nberda<PERSON>kan ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-10-19 23:19:27", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "321c3187-218f-447e-82dd-520910b31375", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:13"}, {"kompetensi_dasar_id": "321ccabc-38c1-446b-97aa-1a1993ae04e0", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 802032200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan  perawatan dan perbaikan sistem kerja kamera", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "321d1e97-2c9e-43e8-af4f-df9da649e343", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:26"}, {"kompetensi_dasar_id": "321dad42-3125-4ce8-aca5-d1b11064c274", "id_kompetensi": "4.59", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencontohkan induktansi bersama (mutual inductance) untuk mendeskripsikan pengaruh terhadap induksi elektromagnetik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:51", "deleted_at": null, "last_sync": "2019-06-15 15:06:51"}, {"kompetensi_dasar_id": "321e3eee-d32b-4aec-a0a5-f9600827c809", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:06", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:06"}, {"kompetensi_dasar_id": "321eeca7-ba09-4141-a7fd-daf7ca17ba1e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 828120200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Me<PERSON><PERSON> jenjang karir dalam organisasi industri pariwisata", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "32206e79-f6be-4164-9a1f-4c4dbc9c298e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401251040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan entry transaksi-transaksi pembelian bahan-bahan, per<PERSON><PERSON><PERSON><PERSON> (supplies), aset tetap dan pembayaran utang pada perusahaan jasa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "32217e6a-2f9d-4ef0-898a-6fcf5d27b10d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 817160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi rancangan pele<PERSON>an", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "322330a2-cae7-40ed-a604-eb96daacbff4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:55"}, {"kompetensi_dasar_id": "3223a514-8413-455e-b0a2-437abd73a579", "id_kompetensi": "4.45", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> nilai-nilai keteladanan tokoh-tokoh dalam sejarah perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:09:16", "updated_at": "2019-06-15 15:13:19", "deleted_at": null, "last_sync": "2019-06-15 15:13:19"}, {"kompetensi_dasar_id": "322546d1-ccd8-441d-8416-88d87d43e61c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:29"}, {"kompetensi_dasar_id": "32265cbe-68ab-432f-8c90-09265ecb6d6e", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 820070500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan system sinyal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "3227fe0e-dc85-440b-ba35-592ec468556d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802032700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  fungsi dan cara memodifikasi gambar obyek 3 dimensi dengan perangkat lunak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:34:01", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:34:01"}, {"kompetensi_dasar_id": "322855c7-9a06-434a-9865-8ad639813759", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:38"}, {"kompetensi_dasar_id": "322882d0-6197-4ec3-8024-10576df9dfc7", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 843100100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan <PERSON> tubuh  keterampilan individu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "3228bb08-60c2-4874-9afd-c04934e53720", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804131000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prosedur p<PERSON>an Instrumen Logam ukur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "3228ddf2-38fc-483f-a711-3b89fb242db3", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 825063400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pengolahan limbah ternak unggas pedaging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "322951ac-c51c-4e86-a5a1-65b1e06b863f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:56", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:56"}, {"kompetensi_dasar_id": "322a222b-0c5d-46d4-8180-c635684830c3", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> shot", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "322ad4f7-19dd-4b35-8485-d436a165c132", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 808050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pekerjaan Sheet Metal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "322b077c-5075-4364-86fe-d533ab8ff8aa", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 821170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan teknik mengelas pelat baja dengan proses las GTAW/SAW pada posisi Flat/bawah tangan 1G", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:22", "updated_at": "2019-11-27 00:29:22", "deleted_at": null, "last_sync": "2019-11-27 00:29:22"}, {"kompetensi_dasar_id": "322bef2b-ba47-4de6-9657-b76086167e06", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:21"}, {"kompetensi_dasar_id": "322cc345-a4e8-4580-97dc-9db729991d8e", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 824060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengulas networking baik dalam program maupun pengiklan untuk mendukung Siaran Online", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "322daffa-e0cf-416f-98f0-a0efd63666c1", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melukis ekspresi teknik aquarel objek langsung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "322de9d4-9b57-4878-9942-f29b87b09bee", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan <PERSON> sebagai pelindung dalam kehidupan sehari-hari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:01:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:13:37"}, {"kompetensi_dasar_id": "32315883-8eb8-462b-82d2-6f797d1071cf", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 816050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses penyempurnaan anti mengkeret (sanforising)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "32317c15-5369-42b2-bc75-0ae71a0e6ee1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825170100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>ahuan Penyakit non infeksius.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "32318c38-ce54-4051-a032-cdc9fbafa70a", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807021410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> laporan hasil per<PERSON>n", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "3232623e-20fd-4da6-afb1-4a688c98f7ed", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 840020130, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah clay body massa plastis tanah liat Stone ware secara masinal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "3232c761-af50-4d2e-87e7-5c643a1d3332", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:36"}, {"kompetensi_dasar_id": "3233a030-aa3f-456b-81cb-591a9e8b661e", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 830050100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan perawatan hairpiece.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "3233e101-800b-42f4-8293-388fb153c37a", "id_kompetensi": "2.4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan  partitur lagu karya sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "3234818a-72d6-45a2-be13-4ce7cc6c70ac", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:35"}, {"kompetensi_dasar_id": "3235332b-fb57-40a4-ae8b-5201644e1db7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:09"}, {"kompetensi_dasar_id": "323540cf-d1fa-4d45-883a-7e75580f2287", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827290500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> penanganan limbah dan hasil samping pengolahan rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "32363c42-4e0c-405c-bfb5-e17066997452", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825230200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan produk olahan daging", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "3236ad4e-04ff-489a-a6f2-c1dfccf6bcb8", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300310500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memodifikasi tindak tutur untuk menerima dan memberi informasi terkait aktivitas sehari hari dan penjelasannya dengan memperhatikan fungsi sosial, struktur struktur teks,dan unsur kebah<PERSON>an pada teks interpersonal dan teks transaksional tulis dan lisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:56:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:14"}, {"kompetensi_dasar_id": "32370fb6-f507-4460-8481-c3ce44310937", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401130400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan senyawa turunan hidrokarbon dalam proses pembuatan sabun opaq/transparan skala laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "32391db1-1b19-4b9b-9a27-83f4b2a8266e", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:15"}, {"kompetensi_dasar_id": "323988cc-3009-460d-9eba-41218af6f431", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis naskah operasional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "3239d6ce-cf95-4980-a358-aaa0864736b8", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804130600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara pembentukan radius sesuai ketentuan pada sudut-sudut sambungan pola polistrerin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "3239e310-1b50-499a-89f6-156b7ff983d7", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis disain setting dalam tata panggung pertujukan tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "323a26b2-7f5f-4334-8e46-9647f228ae8f", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melakukan penataan bahan kimia sesuai sifat dan data keselamatan  bahan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:24"}, {"kompetensi_dasar_id": "323af4f1-41d4-4b0c-9517-2483553dd805", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami arti HAM dan hubung<PERSON>ya dengan tuntutan keadilan yang <PERSON> kehendaki.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:50"}, {"kompetensi_dasar_id": "323b8f1c-1fc8-4370-839d-8f264e994fe7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 814030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep seven tools dalam pengendalian mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "323be256-a1b9-48c4-97d3-89de80084cee", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:42", "updated_at": "2022-10-19 23:19:15", "deleted_at": null, "last_sync": "2019-06-15 16:03:42"}, {"kompetensi_dasar_id": "323c4a7c-3ba0-4011-8030-00a600ac2caa", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan program aplikasi komputer untuk gambar teknik bidang mekanisasi pertanian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:05:53"}, {"kompetensi_dasar_id": "323d076f-d4ae-48fd-861c-708e83559786", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800081200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis transudat eksudat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "323d37fb-9c27-4a78-a8e8-9fb2848ec7a0", "id_kompetensi": "4.37", "kompetensi_id": 2, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan ide kreatif untuk memproduksi sandiwara radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "323d49f3-3d28-4abf-9a58-4c631baa3ce9", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis KCKT", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "323dc1af-b8cc-4ae6-a619-2598107d1a7a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika konsumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:55", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:59:55"}, {"kompetensi_dasar_id": "323efc29-8d9c-4fca-a469-0a150fc38a12", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 806010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pengujian relai proteksi sistem tenaga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "3240247a-3f4a-46ef-aaf3-c9d0fda8c8c8", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401141500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pengu<PERSON>an se<PERSON>an obat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "32404a3b-a7d5-48bf-9b36-0d60ec7fe238", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803041300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki antenna dan feeder di pelanggan,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "3240cea8-ab02-4e77-9f84-e67c2eeaa776", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 822110110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerjemahkan listing program dari diagram alir yang telah dibuat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2019-06-15 15:03:20", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "32414ced-732a-469b-bd72-56bb7d127425", "id_kompetensi": "4.35", "kompetensi_id": 2, "mata_pelajaran_id": 825020520, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan teknik perawatan tanaman penghasil getah sebelum mengh<PERSON>lkan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "32422c09-51f6-4b20-acce-b38baa4303c4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 800020500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis kasus dinamika masyarakat pada kelompok sosial, pranata sosial, dan mobilitas sosial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:10", "updated_at": "2019-11-27 00:30:10", "deleted_at": null, "last_sync": "2019-11-27 00:30:10"}, {"kompetensi_dasar_id": "32426968-177e-4cb9-8ed8-23167680e148", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827390800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain maintenance and repair centrifugal pump", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "32427b39-84de-44cd-b8b8-686789e8fd62", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 834010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami perkembangan seni relief Timur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "32430374-801e-49ad-a52f-3a298a65f146", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:20", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:20"}, {"kompetensi_dasar_id": "3244358c-e9ee-4d85-8ccd-9859dc6e08a6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 808020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bentuk-bentuk benda geometri si<PERSON> sejar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "32447897-e3dc-406a-83fa-567ca0ed6191", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "3245688d-3448-4d74-b531-9326ce8f8c82", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:17", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:17"}, {"kompetensi_dasar_id": "32464dfa-144c-433f-b07b-d18aea96946f", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 843070300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis vokal non ritmis dalam karawitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "3246a1e9-3a96-4320-8edf-160902735276", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "3246e11b-dd47-46a6-b243-26b50f69501e", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemasangan Panel tegangan menengah 20 kV (Medium Voltage Main Distribution Board)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "32471a0f-5362-44aa-a357-b7c67aa2c379", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip pemeriksaan kualitas air dan makanan metoda TPC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "3247692d-bd2c-4f97-9fd1-641553cded87", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "32484ecd-0332-44f5-8f5e-daff9c9ae506", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI) dan menya<PERSON><PERSON><PERSON> dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:25", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:25"}, {"kompetensi_dasar_id": "3248e82f-80ae-43af-8a3c-fd17959aa87c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalai kontrol motor berbasis programmable logic control (PLC). Seperti air conditioning, lift, escalator dan conveyor).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:27", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:27"}, {"kompetensi_dasar_id": "324a262c-43cc-40dc-9270-cd203a48a571", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 401140400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian mutu pada pemeriksaan hematologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:33", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:59:30"}, {"kompetensi_dasar_id": "324a91b1-04ed-4134-b5ad-a28c680ee72e", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 803070800, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan arsitectur (rancang bangun) mikrokontroler", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "324bca9e-8e2f-4fd7-8c1f-b083ba578e01", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 821080300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengobservasi bagian-bagian mesin amplas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "324c1d5d-408a-45c3-bcb5-aba9d15a209a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemograman  mesin bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan teknik pemograman  mesin bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:06", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:06"}, {"kompetensi_dasar_id": "324ee25a-4ecd-476a-b89e-d2d3dea78800", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:43", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:43"}, {"kompetensi_dasar_id": "324eeb4d-1ebd-4dd4-94a3-2a3f83dd95b9", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804170100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan hukum   kelistrikan dan elektronika  pada sistem instrumentasi industri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "32501a00-81b8-446e-8b63-61de57e6cc7c", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis intonasi suara dan tuning masing-masing instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "3250492e-121a-4f3c-99fe-ac47ee011ad1", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 804060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur pembuatan saluran irigasi dengan beton", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "32509d3f-8406-4ac8-a166-e69e059291e9", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 827370500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan melakukan prosedur isyarat bahaya sesuai dengan standard IMO", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "3250cd64-6d37-4160-8f85-e5393d4fe471", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "3250dcb8-029a-4fe5-a990-48acdf4eee72", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:34", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:34"}, {"kompetensi_dasar_id": "3252dcf5-c71c-4f98-a225-3899e58377bb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis special service tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:42", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:42"}, {"kompetensi_dasar_id": "325323d0-492d-4322-b27d-7c4055c6dfe0", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pemberian saran kepada tamu mengenai perleng<PERSON>pan housekeeping", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "32541a32-1049-4eb3-ade6-8cc1ed6c5769", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan sakelar otomatik berbasis suhu dan tekanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "32545743-77fd-435a-ac5e-0b66ccdc3857", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 800090200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> Bahan-<PERSON><PERSON> (B3) bagi mahkluk hidup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "3254b3ad-2d62-4ab3-9ca8-6c6c042cff15", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis konsep desain/\r\nprototype dan kemasan produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:47", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:47"}, {"kompetensi_dasar_id": "3256214f-1da7-4654-940b-70ed921572ac", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hak dan kedudukan wanita dalam keluarga berdasarkan hukum Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:00", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:00"}, {"kompetensi_dasar_id": "3256be63-ae80-4afa-8c17-583794e20d74", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan alur kerja di bengkel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "3257654d-a380-410f-9bd0-040eb612e4e4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:34"}, {"kompetensi_dasar_id": "325793a0-a162-46f4-8d01-ed8194f392f3", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemilihan jenis dan kapasitas turbin serta tata letak turbin sesuai hasil studi kelayakan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:15"}, {"kompetensi_dasar_id": "325a59ba-567a-41e7-ad87-8bf80382cee4", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pem<PERSON>n kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:03", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:03"}, {"kompetensi_dasar_id": "325bf0d9-c905-446c-95b1-9d99c1b73810", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827080300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pengaruh bahaya dari tiap-tiap bahan penyebab polusi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "325ca5da-5dac-459f-b4d8-c39e849641df", "id_kompetensi": "3.5", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur analisis elektrogravimetri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "325ce35e-9927-4468-b30b-e1efdbefb2ee", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300310600, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mendemonstrasikan p<PERSON><PERSON><PERSON>, pen<PERSON><PERSON> suku kata, penekanan/ intonasi dari kalimat dengan mencocokkan dan membedakan secara tepat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:12", "updated_at": "2019-11-27 00:30:12", "deleted_at": null, "last_sync": "2019-11-27 00:30:12"}, {"kompetensi_dasar_id": "325d4f58-31db-44a6-8ae3-6c354eb053ba", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 821171000, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil rang<PERSON>an <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "325f292a-a43e-4e6f-891b-b18733576efc", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825030400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Memahami teknik pemasangan patok/ajir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "32604241-c1b6-478e-b223-ff14d63fbb7d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 814160200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan karakteristik limbah padat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2019-11-27 00:29:16", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}]