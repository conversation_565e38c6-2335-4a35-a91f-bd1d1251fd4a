[{"kompetensi_dasar_id": "1ccf6406-cf0a-463c-abea-3e3d12872a3d", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks berita sederhana dari koran/radio/TV.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:28"}, {"kompetensi_dasar_id": "1cd00151-85cb-4df3-aac0-9f6687d44bab", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:28"}, {"kompetensi_dasar_id": "1cd119be-007b-4b5f-a1ec-bd306e507bf7", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks il<PERSON>h fak<PERSON> (factual report), lisan dan tulis, se<PERSON><PERSON>, tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, terkait dengan Mata pelajaran lain di <PERSON>las XII, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:10"}, {"kompetensi_dasar_id": "1cd14e60-b7cd-44a8-ad77-71d43ddf4c3a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 830100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemasaran usaha salon.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "1cd171d5-fc8f-4cd8-bfdc-01d9e249f9ef", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:39", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:39"}, {"kompetensi_dasar_id": "1cd1cad0-0b7e-46b6-bc61-e7a30fee5d1d", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kegiatan budidaya perikanan perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "1cd208fb-b34a-4794-bc6e-03c379a87d56", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 843080200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> karakter wayang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "1cd21d61-0241-4980-a8e3-1c00aea677b8", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan  prinsip kerja piranti control berbasis rele", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "1cd2f757-f58d-408c-8943-ce468fe4b087", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bahan kimia dan penanganannya  berdasarkan tanda bahaya sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:05"}, {"kompetensi_dasar_id": "1cd3adc0-a26e-489d-bce9-3a14c59e786c", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>pta karya pengolahan dari bahan nabati dan hewani menjadi produk kesehatan yang berkembang di wilayah setempat dan lainnya sesuai  teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:33"}, {"kompetensi_dasar_id": "1cd453b2-8b40-4f85-879b-e3ef89402695", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan mesin  bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan mesin  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:11", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:11"}, {"kompetensi_dasar_id": "1cd48803-825a-4db5-832a-4294539c8b78", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 808020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar bentangan  benda geometri kerucut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "1cd4a11b-2fdb-4dae-98c2-78f2317eeb23", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827391100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Show manipulator element", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "1cd544d1-c3dc-435c-87f5-c87ac07c1e87", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> ciri-ciri cermin dan lensa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "1cd56e1e-e1c4-48e5-9bb7-47ebfaf0aad2", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> prosedur <PERSON>an RAB pada peker<PERSON>an plambing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "1cd58c6f-4293-4ae7-b078-790fb741906f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804130700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan komposisi campuran pasir dan volume pasir yang akan digunakan untuk membuat cetakan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "1cd5cd90-b5f4-4be5-ad9e-782105dee707", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar penggu-naan kontrol suhu dengan sistem mekanik, elektrik serta  hubungannya dengan interface dalam pembuatan sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:44"}, {"kompetensi_dasar_id": "1cd6cbce-4692-4d75-aa5b-8dccb74a5316", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "1cd80cf9-b434-4e54-a503-8f35f6b1473d", "id_kompetensi": "Keselama<PERSON> dan <PERSON><PERSON><PERSON><PERSON> (K3LH) dan budaya kerja industri ", "kompetensi_id": 3, "mata_pelajaran_id": 800000114, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu menerapkan K3LH dan budaya kerja industri, antara lain: praktik-praktik kerja yang aman, bahaya-bahaya di tempat kerja, prose<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON> k<PERSON>aan da<PERSON>rat, dan penerapan budaya kerja industri (Ringkas, Rapi, Resik, Rawat, Rajin), termasuk K3LH pada pengendalian pencemaran air, pengendalian pencemaran udara, pengelolaan limbah B3 dan non B3 dengan menggunakan material safety data sheet (MSDS) atau panduan lain yang sesuai. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "1cd949de-4938-41ac-b204-e5b116e5fba8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin bubut, dengan su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:46", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:46"}, {"kompetensi_dasar_id": "1cd9a760-10df-48a5-94fa-bc89b0419c36", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 300310800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengimplementasikan tindak tutur untuk menyatakan dan menanyakan tentang tindakan/kegiatan/ kejadian pada waktu yang akan datang (indicative futur) dengan memperhatikan fungsi sosial, struktur teks, serta unsur kebahasaan pada teks interpersonal dan teks transaksional tulis dan lisan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "1cda2829-4b9b-460d-91a8-f5ebd31900aa", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:48"}, {"kompetensi_dasar_id": "1cda7144-b364-495c-8887-f3ee104c7888", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:32", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:32"}, {"kompetensi_dasar_id": "1cda8d36-2d7c-4b1a-8607-b687e532e46a", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pengisian SPT tahunan PPh Badan Bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "1cdb1f38-5b66-4a2e-babc-16205bfae668", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON>kan bahan dan alat (alat berat) dalam pekerjaan jembatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "1cdb595f-ee61-45ca-9bcd-71914680fb76", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sediaan galenika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "1cdc16fc-0d3b-47ea-9271-fc550455ef5c", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 839110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menciptakan usaha karya kerajinan dari bahan kulit yang berkembang di wilayah setempat dan lainnya sesuai teknik dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "1cddbf5d-d833-4763-8d16-430adf16bb2e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam special service tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "1cdfa7bd-da00-4007-b07d-43215c65cfc3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820130300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevluasirancang  pembuatan cover jok", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2019-06-15 15:03:19", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "1ce103a9-0a41-4d70-b379-bcd5d3855601", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:29"}, {"kompetensi_dasar_id": "1ce137dd-ee51-4a83-8900-37f5ed9d5806", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kemajemukan bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:48"}, {"kompetensi_dasar_id": "1ce1c0e4-529d-4d76-8261-ec9809dadd15", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan  benda kerja rakitan pada mesin frais, dengan menggunakan berbagai cara/ teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:34", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:34"}, {"kompetensi_dasar_id": "1ce2d4db-7c3f-464c-85ee-01d357737896", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 822040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan fungsi perintah dalam perangkat lunak CAD untuk memodifikasi gambar 3D", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2019-06-15 15:12:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "1ce3885f-1fef-45df-8562-6f92a32fb2e9", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 827060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara penentuan posisi kapal dengan GPS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "1ce40bb0-235c-4250-9267-a009945bbba0", "id_kompetensi": "2.3.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami musik kreasi berdasarkan jenis dan fungsi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:35", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:01:35"}, {"kompetensi_dasar_id": "1ce65ba8-98ef-434d-a2fc-9375ba2aee50", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan  kegiatan usaha bank umum dan bank perkreditan rakyat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "1ce7eace-d469-4f07-807e-aa7f5485d268", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa spesifikasi piranti-piranti elektronika daya dalam rang<PERSON>an listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "1cea9c83-50fc-487f-a84c-6029c96bd431", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON>ata meja (Table set-Up)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-11-27 00:30:31", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "1ceb2b7b-e27e-4ded-9cec-e6571d34fefd", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807022400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyimpan dan membuka file sesuai Prosedur Operasional Standar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "1cebe9b4-5e86-4fd1-83df-781b4511545b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821120310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami motor bantu kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "1cebf4a0-a3fb-4496-993b-819012bd450b", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401251104, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan dokumen-dokumen administrasi zakat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "1cecf901-b0fa-436b-a19d-061054b15212", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami fluida sistem kemudi alat berat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "1ceddf8f-b0f3-4771-b5de-6b7527e9e293", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan data spasial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "1cedfde2-253a-4043-8d69-fd1c7dfe4e6d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 808060620, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkreasi fabrikasi part pesawat udara sederhana dari bahan material komposit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "1cf03878-9802-429d-b54b-841e657bb700", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827210400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pendederan atau pemeliharaan benih paska larva sampai menjadi benih siap tebar hasil pemijahan alami", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "1cf221cc-4e7c-4cf4-8ddf-1586a7dd832d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807021600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan elektronika optik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "1cf2678f-0db0-499e-888c-fdfd738cd97f", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 401130000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan proses teknik pemisahan fraksi-fraksi minyak bumi serta kegunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:59:19"}, {"kompetensi_dasar_id": "1cf4c511-9502-4b27-951a-200cb6f9b6a0", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan checker transistor bipolar /FET.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "1cf4c8ab-6844-4349-87c6-88821d2c7102", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 807021920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa hasil pengelasan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "1cf55ef3-d938-4936-b31b-009e6c520865", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 802031100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsip - prinsip dasar animasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "1cf57932-c685-4167-8bc5-7990f6513e65", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 839120200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merada dan membuat isen-isen komponen produk kulit mentah tatah sungging tiga dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "1cf5a1f9-c2bf-47ae-b82d-31829f129329", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peker<PERSON>an retrofit pada sistem tata udara domestik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "1cf5b3c6-45a6-4a66-a99f-71614252b790", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 822080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> fungs<PERSON>, t<PERSON><PERSON><PERSON>, cara kerja, wiring dan prosedur diagnosa Sistem Kontrol Diesel Elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "1cf79e23-f629-405b-b5e9-b0e569adbbb8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804011000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "1cf86ed6-f2c1-416d-be58-c300c0e86976", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengartikulasi aplikasi sensor flow", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "1cfb22ea-4f22-436d-a728-088496adb7c5", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 828200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis pemasangan/pelepasan elemen pameran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "1cfb6c67-e3c0-48e8-b84c-5fab3b7fbd95", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 300310100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengimplementasikan teks sederhana terkait desk<PERSON> orang (washf al-insan), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "1cfc15f6-c1ca-40b0-8941-2c57a7f1e8f1", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 817010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami geologi minyak bumi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1cfc39de-a6ad-412e-9d54-09b3472abbe3", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 821190300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistem kendali elektromagnetik pada industri dan kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "1cfda4ed-f523-4111-8081-87986cc67136", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 820070200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan pada sistem rem ABS", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "1cfec1dd-d0ed-41a2-a107-2f4b1d19a7d2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi dalam simulasi perlombaan salah satu nomor atletik (jalan cepat, lari, lompat dan lempar)yang disusun sesuai peraturan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:51", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:51"}, {"kompetensi_dasar_id": "1cffbb63-3abe-4a0d-95b1-29c11f2e1887", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "1d012209-cd07-49e7-ba54-514e2cca7943", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:16"}, {"kompetensi_dasar_id": "1d03d5dc-2f9f-4234-984a-b7964c5b35f2", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 827110330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi dock order", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "1d03fc9c-746f-4417-a86c-f8e16e90ef4c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 830050100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan pengeringan rambut blow dry", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "1d0450f2-ab9b-49aa-b3f4-d52d20a372a9", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 825062400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan siklus reproduksi hewan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "1d047b9e-dce8-40ab-9355-df0460a1adb0", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 843110300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik jeda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "1d053530-b38a-456b-943f-99587d187509", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807021920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan konsep dasar pengelasan Oksigen Asetelin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "1d073b3b-45f3-4b93-8941-4104660a9314", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803060900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kerusakan pada rangkaian catu daya tinggi penerima televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "1d074ffb-d76f-4789-bd34-1992813fcc53", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 401251400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> re<PERSON>ana pengembangan usaha", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:46", "updated_at": "2019-11-27 00:29:46", "deleted_at": null, "last_sync": "2019-11-27 00:29:46"}, {"kompetensi_dasar_id": "1d07634f-faf1-44f0-b58b-91ca7aaf175c", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:29"}, {"kompetensi_dasar_id": "1d077015-995b-478e-ab37-88ad3b49d7ad", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami nama-nama interval", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "1d08d6aa-b512-469a-9db5-40968ecba993", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:25", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:25"}, {"kompetensi_dasar_id": "1d0a0ba0-0229-49ea-8581-affe84302aff", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843062200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> unsur-unsur berkarya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "1d0b9e02-fde1-4a57-b2ba-1fc5996b1d6e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "1d0bbb90-0422-4ee4-add7-02e16ff0d131", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 816020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses pengelant<PERSON>n / pem<PERSON><PERSON> bahan tekstil (bleaching) dengan reduktor dan ok<PERSON><PERSON>, titrasi zat pengelantang serta penerapan konsep K3LH.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1d0bbcd2-106b-42d3-9e71-fa6e8d36c5e2", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:13"}, {"kompetensi_dasar_id": "1d0bf985-74c7-4753-82eb-1289c49d7105", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:16", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:02:16"}, {"kompetensi_dasar_id": "1d0d9727-0b0d-46a5-a931-4b3da4520f78", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827350700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain daily motion and horizontal system of coordinates", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "1d0da25b-4b75-44bc-ad2c-533cc7e0a390", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola  menyerang dan bertahan) salah satu permainan bola besar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:09", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:09"}, {"kompetensi_dasar_id": "1d0de63c-70bb-4486-b11a-d6d1278d488d", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 828190120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> penanganan keadaan darurat pada saat melakukan perjalanan wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2019-06-15 14:58:27", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "1d0deb46-dfca-4148-ac9e-1fac38af9def", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil evaluasi dari berbagai media massa tentang peran Indonesia dalam hubungan internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:20"}, {"kompetensi_dasar_id": "1d0e058e-a094-4cf1-b7c9-173e24354929", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 807022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami jenis Integrated circuit (IC)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "1d0e7890-9217-4ba8-b1bb-17c8d4bb7514", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:50"}, {"kompetensi_dasar_id": "1d0ee42b-8bb1-4286-ad14-f848cc2eb245", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses dasar pembentukan logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:25"}, {"kompetensi_dasar_id": "1d0efaea-6b74-4e2c-9710-d5cec7e733db", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 822190500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasangan menara PLTB skala kecil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "1d0f68f6-fcdf-4f1c-ba8b-e1b283c61c80", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 843060700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dasar komposisi tari berkelompok", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "1d0f857a-c17c-4bd6-b9ae-809088e2afa9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  cara kerja peralatan tangan bertenaga listrik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2019-06-15 14:52:54", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "1d11409e-a4f1-40c1-a74f-f55d52ad0956", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengidentifikasi potensi dan resiko kecela<PERSON> kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:14", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:14"}, {"kompetensi_dasar_id": "1d11f639-2db4-4666-8142-124600904701", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 839110200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyeset komponen produk kulit non alas kaki dan non busana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "1d12b8e6-ad32-42a3-873f-5dc494ace467", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip-prinsip pengendalian kontami<PERSON>i", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:39", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:39"}, {"kompetensi_dasar_id": "1d13808c-6eef-4ca9-9909-6f6a7cb28c56", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "1d138d52-224b-472f-bf41-bf15cde6c999", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:16", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:16"}, {"kompetensi_dasar_id": "1d1409e9-56cf-445e-9ab5-d4f4d7ddf519", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843050600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik editing <PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "1d141f5a-0141-4e1a-add3-f9c46a357202", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825270500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Men<PERSON><PERSON> sis bahan hasil pertanian secara Fisik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "1d144669-d0f9-46d1-ba4b-685a50028660", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> sistem bahan bakar in<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:06"}, {"kompetensi_dasar_id": "1d150820-b9d7-4621-a25c-966730d36e15", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:54"}, {"kompetensi_dasar_id": "1d1813e7-0d01-4bcd-bd5b-e54afb5f5485", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi penanganan hasil telur tetas parent stock.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "1d18da1e-699d-4c8b-a615-45c0793357b4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan mekanisme debit kredit akuntansi bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "1d18e1d0-862c-45ff-b2c9-da4cbf4db22d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola besar dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "1d196f81-fddc-43e9-8f35-3de8a6adc067", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki berbagai Engine Indication (engine instrument)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "1d19eed3-591d-41f4-bdde-d9bad60750e2", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan operasi bagian\r\nbagian utama\r\nmesin bubut \r\nberda<PERSON>kan ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:01:08"}, {"kompetensi_dasar_id": "1d1a1dcc-378c-49cb-ac35-21580b5bc4bf", "id_kompetensi": "Proses bisnis di bidang teknik energi terbarukan ", "kompetensi_id": 3, "mata_pelajaran_id": 800000117, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami proses bisnis di dunia energi terbarukan mulai dari perencanaan instalasi, pembuatan panel dan pemeliharaan serta perbaikan alat energi terbarukan seperti energi surya, hidro dan angin serta energi biomassa. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "1d1a1e58-8693-42a1-b4ab-0f28c941f34a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem koloid dan kegunaannya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2019-06-15 14:49:55", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "1d1a2f30-19af-4450-8f01-c6fcfa13db5d", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 831102000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis desain dasar tiga dimensional dengan prinsip transformasi volumetrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "1d1bbc5e-7b2c-4939-af0a-13bfc9d165db", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan konsep rangkaian logika pada rangkaian elektropneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "1d1c754f-d45d-448a-aed3-ec2d52756078", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:45"}, {"kompetensi_dasar_id": "1d1e7e01-b92f-40e3-b2ca-922a1544bf53", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:52"}, {"kompetensi_dasar_id": "1d1ed295-a557-412e-8143-1f7c904566da", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827360200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate lists the single-letter signals which may be sounded only in compliance with the requirements of the International regulations for preventing collisions at sea", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "1d1f0a2b-1fa8-4367-b3e5-b8d9e15844cf", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 829040400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesk<PERSON><PERSON>an  bahan makanan dari serealia (gandum dan beras) dan hasil o<PERSON>nya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "1d1f8376-132a-4587-a48a-3c0df716bd15", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:11"}, {"kompetensi_dasar_id": "1d1fb615-0e64-4a07-9bc9-70c1be09cffd", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401251030, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis harga pokok produksi per<PERSON>an manufacture", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "1d2097fa-4707-4cba-9150-92be07e441e2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:15", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:15"}, {"kompetensi_dasar_id": "1d2106dd-fc56-42d6-a981-368bbb135cfb", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 803080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeliharaan Sistem Kontrol PLC pada SCADA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "1d215012-a1a2-4c2a-afbe-9c6e937f23ac", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 814030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep seven tools dalam pengendalian mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:29", "updated_at": "2019-11-27 00:29:29", "deleted_at": null, "last_sync": "2019-11-27 00:29:29"}, {"kompetensi_dasar_id": "1d24a128-54ad-4d99-99f3-cec35fad630d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis volume pekerjaan pada pelaksanaan konstruksi bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:29", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:29"}, {"kompetensi_dasar_id": "1d2605f8-cf82-46f5-9fcc-c89b84b580a0", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penggelapan kulit badan dengan heliotherapy teknologi suntan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "1d27c5a6-df75-47b7-91de-791393fb2f91", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "1d27ca38-fb74-439a-9c6d-f4bf4981a0d4", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827390300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain geometrical tolerances", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "1d297438-ee50-4143-9b1f-42d8d49feea9", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan alat timbangan neraca analitis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "1d29db26-b90c-4266-8738-e0ee0049bb20", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:30", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:30"}, {"kompetensi_dasar_id": "1d2ae900-fc5a-445d-acf6-4779123733dd", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pen<PERSON> & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "1d2b2b97-d143-4c2d-9a10-8ecdcaac60a0", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 803071400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami parameter saluran gelombang mikro", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "1d2c255b-913b-4881-9d8b-514be4e9ce48", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401131400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil analisis gravimetri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "1d2d0297-002e-4013-9744-637b89ed32e3", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 808060620, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik reparasi / maintenence part yang terbuat dari bahan material komposit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "1d2d10af-120b-48ed-b554-4147f1ed3269", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807020200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> \" lap patch repair of aircraft skin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "1d2ed53a-52ce-4998-8a6b-ad4c7a9e52c6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> p<PERSON> pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:35"}, {"kompetensi_dasar_id": "1d2f02f7-2a95-48b2-a7a2-55fa707f9624", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kecepatan putar dan pemakanan pada mesin frais untuk berbagai pekerjaan berdasarkan parameter yang ditentukan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "1d2f42b8-4eae-4861-9922-a82373283269", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 823170600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> langkah-langkah komisioning dan tindakan pengamanan kegagalan operasi PLC", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "1d2f5829-a6a5-477e-8673-d06f0a7f35dc", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat file backup untuk data akuntansi perusahaan manufaktur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "1d3016db-e5f4-4839-83e5-1eeed076c72d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 801030500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan  intervensi pelayanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:04", "updated_at": "2019-06-15 15:07:04", "deleted_at": null, "last_sync": "2019-06-15 15:07:04"}, {"kompetensi_dasar_id": "1d3063ba-7379-4185-9da4-21dc30e40b5b", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 843050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> penjarian tangga nada mayor dan minor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:06", "updated_at": "2019-11-27 00:28:06", "deleted_at": null, "last_sync": "2019-11-27 00:28:06"}, {"kompetensi_dasar_id": "1d30c95c-07b9-443d-baa1-4f5c5f34cee5", "id_kompetensi": "3.43", "kompetensi_id": 1, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penanganan kelahiran ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "1d3118d9-d278-4208-832b-ca940c8eb60c", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 827390600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Show automatic control system", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "1d31a74b-8bcb-4c54-b8fc-5bffb06c49f9", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data dari situasi nyata, memi<PERSON>h variabel dan mengkomunikasikannya dalam bentuk model matematika berupa persa<PERSON> fungsi, serta menerapkan konsep dan sifat turunan fungsi dalam memecahkan masalah maximum dan minimum.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:50", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:50"}, {"kompetensi_dasar_id": "1d3223fc-25c1-4457-b018-db1e125e4721", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks prosedur lisan dan tulis berbentuk resep", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:06"}, {"kompetensi_dasar_id": "1d329ac5-f716-4ec8-968f-5a737253025a", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 820030700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menelaah kontrol elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "1d32c8bc-d444-4fe0-816d-8bb4da33ffdd", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 837030100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan desain ruang keluarga apartemen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:41", "updated_at": "2019-11-27 00:28:41", "deleted_at": null, "last_sync": "2019-11-27 00:28:41"}, {"kompetensi_dasar_id": "1d335c0d-045f-4cb7-955c-98219348635d", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 830050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencabutan bulu pada bagian tubuh(waxing) dengan teknik depilasi dan epilasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "1d33cdca-23d8-427b-b858-1aea19e67a07", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan titrasi pembentukan senyawa kompleks (kompleksometri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "1d3508dc-b5e2-44d3-9150-4957c83fcd17", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi data analisis kromatografi Kertas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "1d35749e-b269-4f2e-83b1-3c2bd27d878c", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 824050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> biaya produksi film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "1d376ebd-d0f5-450f-971f-da2c2791bcb5", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825250600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> persiapan sarana instalasi udara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "1d37def2-bcd1-464e-bb35-a7c05b8efb5c", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi laporan neraca untuk perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "1d38023f-0c20-4677-9fba-c906127b2933", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 823030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses dan hasil pengoperasian dan pemeliharaan reaktor biogas skala rumah tangga dan komunal untuk macam-macam bahan baku yang dilakukan secara mandiri, efektif dan kreatif sesuai K3 dan di bawah pengawasan langsung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "1d393ec4-6601-49d0-a248-541b1e79ad26", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON> kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:40"}, {"kompetensi_dasar_id": "1d3aadad-1a5b-4f1e-9257-8b6973050cd7", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825210600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan seleksi  benih dan prosedur penebaran benih pada pembesaran   komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:48", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:48"}, {"kompetensi_dasar_id": "1d3ad4ef-d84d-40f5-94e0-33b8ab5d1b0d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa proses pembuatan rangkaian filter frekuensi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "1d3b4b4e-e6af-4598-991f-72476982a7fd", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 814150200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil pengujian hasil produksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "1d3c74c7-b28c-4185-8056-470dfcc07e4d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja dan prinsip pengoperasian peralatan penukar ion.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:41"}, {"kompetensi_dasar_id": "1d3d1697-6205-49e4-8974-889c7b920af4", "id_kompetensi": "2.3.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis musik kreasi  berda<PERSON> ma<PERSON>na , simbo<PERSON>, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:25", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:25"}, {"kompetensi_dasar_id": "1d3d9b62-4fc7-4e1a-b317-21b709160971", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:13"}, {"kompetensi_dasar_id": "1d3dea38-4c68-4974-81a7-4ac33d74c17e", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis inovasi prosedur perbaikan instrumentasi medik berbasis komputer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "1d403e2c-c15c-4f51-a7a9-f9d35ad2e906", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat dua garis sejajar dan saling tegak lurus dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:53"}, {"kompetensi_dasar_id": "1d40482f-3685-40d9-b9a5-778efc451f09", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:24"}, {"kompetensi_dasar_id": "1d416192-b2f7-43ad-ba38-b0c465af0406", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802031300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> sudut pengambilan gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "1d42dd6b-416b-489b-9c61-3f883be7fcb4", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 826100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain teknologi Sistem Informasi Geografis (SIG) terapan dalam pemetaan lokasi kegiatan ekowisata", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "1d43afb8-0a31-455d-8319-955f3eef6117", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 820070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat perencanaan kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "1d440531-4913-4778-b4e5-6fae2de0790e", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain Search and rescue", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "1d447769-07b3-4ed5-93f1-91fd1e775a52", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 839050100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan motif batik tulis klasik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "1d451058-2832-4869-afbd-ce09f43a7076", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804100100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep listrik (arus dan potensial listrik)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2019-06-15 14:49:58", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "1d458184-7624-42c6-b98c-adb58cafa6e0", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:51"}, {"kompetensi_dasar_id": "1d45a719-a753-4bb4-966a-0fbf1d71942f", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam bentuk konstruksi plafon kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "1d45d9a1-1f27-451b-8002-0b46620a665c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803070810, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat urutan pemecahan masalah dengan menggunakan diagram alir", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "1d4644f0-58db-434c-a249-e8aab1c86748", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 803050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami aplikasi rangkaian UJT (Uni  Junction Transistor)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "1d4652f5-79ce-44d6-bc42-a520c3460f60", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 803081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil pengukuran derajat k<PERSON> (pH)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "1d48c98c-a2a7-4356-89a7-51f66a38723b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil penentuan kadar suatu bahan berdasarkan titrasi asam basa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "1d4e122a-837a-4585-af12-f0d9d3df415c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan panggilan hidupnya sebagai umat Allah (Gereja) dengan menentukan langkah yang tepat dalam  menjawab panggilan hidup tersebut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:48"}, {"kompetensi_dasar_id": "1d4ebe4e-3db3-409b-b1f0-266d3840b431", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 825021300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menevaluasi hasil komunikasi secara tertulis dan lesan dalam kegiatan produksi perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "1d4ec7b9-30d0-47bf-a487-04f33ac6b372", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843061110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memformulasikan teknik pengembangan melodi instrumen dan vokal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:18"}, {"kompetensi_dasar_id": "1d4ee3a4-5154-4511-89eb-1e9cd3f0e9a0", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 825230200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi bahaya dan titik kritis (HACCP) dalam pengolahan hasil hewani", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "1d4f1486-ee0c-4b92-b918-44bd736de609", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 828110100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan pencegahan terjadinya penyakit akibat kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "1d52ef0e-5d88-4f84-8d3d-0edac38e5ce2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses grinding dan sizing Sieving.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:41"}, {"kompetensi_dasar_id": "1d5524c5-9451-4c72-9cc6-8bb4f543d979", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 820040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan mekanisme blok silinder dan kelengkapannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:25"}, {"kompetensi_dasar_id": "1d555069-2121-4a2b-9e2f-ad43e975d2f7", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "1d55b471-5da8-4450-afb9-cdfaf632fe72", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 825022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Merancang kebutuhan alat proses produksi pada produk baru perkebunan dan herbal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "1d5670d3-d695-4db3-8431-39dd5b1722ea", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 817010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan prinsip stratigrafi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1d576e7b-a073-4575-ad61-e69b173d381c", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 843060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis karya tari berdasakan konsep pengembangan gerak yang bersumber dari tari non tradisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "1d58782b-8bf6-4296-893e-c93873829d63", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  gambar teknik bangunan baja  mencakup  gambar arsitektur bangunan, gambar renc<PERSON>, gambar kerja (shop drawing) dan gambar pelaks<PERSON>an (as built drawing)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "1d58bf62-b277-49b1-aa9a-2ff1b87eefd7", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencetak laporan keuangan bank", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "1d598ab2-9354-4f4f-8c41-f9e254a9d4ad", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik dan prinsip penggunaan alat gambar teknik manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "1d5a349d-eb5f-45e7-bd1f-151808c4af08", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "1d5b152d-4270-44e2-beaf-501f7d8bc6bd", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 827200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pengendalian hama dan penyakit pada pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "1d5b2d33-5678-46c8-b4ef-09bcb1ddfbf4", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 816010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pengetahuan macam-macam proses pen<PERSON>, perawatan peralatan cucuk dan konsep K3LH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1d5c8e0e-e390-4a76-a376-e02dfe2ad2ce", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi laporan keuangan bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "1d5de2bd-3074-4a10-887d-2a07cbf92010", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung RAB pada pekerjaan perawatan bangunan gedung yang tergolong renovasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "1d5e0661-40e9-47d0-ba7a-7079d8952d96", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks prosedur lisan dan tulis berbentuk resep", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:06"}, {"kompetensi_dasar_id": "1d5e07a8-18a2-406a-b459-a520ddf2b0e6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 814070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melak<PERSON><PERSON> bongkar muat barang sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1d5e4840-af0d-4b20-a604-256406ff5548", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami spesifikasi dan karakteristik kayu  4.3 Menyajikan spesifikasi dan karakteristik kayu", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:46"}, {"kompetensi_dasar_id": "1d5ec022-67ac-479a-8fcc-92648cf9e0cb", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 843090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah setting dalam tata panggung pertujukan tari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "1d5f2eec-e691-46cd-8b08-15540f341736", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON>ri kemajemukan bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "1d6009bf-7ce2-4859-b2ae-da69dd113f3e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi yang berkembang di wilayah setempat dan lainnya sesuai teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:40", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:40"}, {"kompetensi_dasar_id": "1d60bdf7-42d1-43d0-aa8a-3daaaeb251e3", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 801030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan   faktor pen<PERSON> masalah sosial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2019-06-15 15:07:03", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "1d61d09d-cce5-4ed2-bffc-e72cc3ec7d3e", "id_kompetensi": "4.40", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses testing and commissioning system HMI", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "1d6222b8-9b47-4b20-b840-2d32d4a1ee10", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kelayakan usaha  agribisnis ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:06", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:06"}, {"kompetensi_dasar_id": "1d63dd04-33c2-4acd-a366-5e54151d43b5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 822160100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> turbin, fly wheel dan alternator/ generator.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "1d64188c-8d58-44ec-b71e-abc1444664f1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:45"}, {"kompetensi_dasar_id": "1d645d9c-0322-49d9-a001-380e56ccaffc", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:51"}, {"kompetensi_dasar_id": "1d64c4f4-a136-4139-8d6f-0cfaf82bf6cc", "id_kompetensi": "4.2.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (31): 13-14 dan Q.S. <PERSON>Ba<PERSON> (2): 83 sesuai dengan kaidah tajwid dan makhrajul huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:34", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:34"}, {"kompetensi_dasar_id": "1d64d938-250b-4071-907e-99534097d136", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 834010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat desain patung tiga matra teknik digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2022-11-10 19:57:42", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "1d660349-76e5-4248-a5fd-6ca8e3683af0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 815010800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan proses mesin <PERSON> Spinning", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "1d688209-92d9-483b-97ff-e663b2dced83", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan hubungan makna beriman kepada malaikat-malaikat Allah swt dengan perilaku teliti, di<PERSON><PERSON><PERSON>, dan waspada", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:00", "updated_at": "2019-11-27 00:30:00", "deleted_at": null, "last_sync": "2019-11-27 00:30:00"}, {"kompetensi_dasar_id": "1d68d26d-a7aa-41a2-b207-9608112b6cc9", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 828190130, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Memproses reservasi tiket penerbangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "1d6a2230-7cd5-4163-a371-f20d9f297f65", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan pengurutan kulit kepala dengan berbagai metode", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:33", "updated_at": "2019-11-27 00:30:33", "deleted_at": null, "last_sync": "2019-11-27 00:30:33"}, {"kompetensi_dasar_id": "1d6b65df-e4c3-4826-8f47-f2cb8e8051ce", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor-faktor kema<PERSON>an dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:37", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:05:37"}, {"kompetensi_dasar_id": "1d6d4905-095d-46ad-a1a5-00e8af080daf", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pribadi <PERSON>us <PERSON> sebagai sahabat sejati, to<PERSON><PERSON> idola, dan <PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:20"}, {"kompetensi_dasar_id": "1d6e6264-e736-4f48-8712-5674c99dcaad", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401131300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil analisis parameter bahan alam/produk industri secara kimia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2019-11-27 00:29:57", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "1d7194de-55d0-45c7-8562-f8c508356b17", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan menu gizi mengacu program diet untuk lanjut usia penderita penyakit persendian dan tulang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "1d71f0ab-8081-4d3b-ab06-28902d45027a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencana dan menginstal CCTV untuk sistem keamanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:08", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:06:08"}, {"kompetensi_dasar_id": "1d723229-a740-4cc6-ae2b-f6b50fb5e560", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 823030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasangan baffle (sekat) yang terbuat dari HDPE geo-membran pada pinggir kolam bio-digister sesuai SOP dan spesifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:51:17", "updated_at": "2019-06-15 14:51:17", "deleted_at": null, "last_sync": "2019-06-15 14:51:17"}, {"kompetensi_dasar_id": "1d7320eb-747f-413b-b971-9bd307641cb0", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 820140300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem bahan bakar diesel Common Rail", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "1d757b68-44ea-4b87-98a5-3891614be028", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses statistik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "1d75f82f-93eb-4bf3-8065-a3a32ba04bbe", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan  simbol simbol dalam gambar teknik plumbing", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "1d76936c-1981-4fff-b985-1a612c2ba8a2", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis kualitatif metode H2S dalam pemeriksaan kation golongan 1 dan 2 dan pemeriksaan anion.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "1d77f9e9-02a4-42ad-834b-2ea3792bda95", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "1d790c36-adfa-4a1a-bb5f-a2e8b9d3f932", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 843062100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> gending/lagu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "1d7ae9f6-750d-4cd4-8d2a-4955ee9fa5ab", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 842040200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses finishing water based pada produk kriya kayu dengan teknik oles/kuas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "1d7b3440-500c-4319-ba13-313182814886", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang integral tak tentu dari fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:37"}, {"kompetensi_dasar_id": "1d7bc40f-f10a-4a0e-999b-a41b061e27a4", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 826080100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi jenis tumbuhan yang di<PERSON>i", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:20", "updated_at": "2019-11-27 00:28:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:20"}, {"kompetensi_dasar_id": "1d7c03d2-1d49-4106-adaf-cc3a4f2e2db5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis strategi yang diterapkan negara Indonesia dalam menyelesaikan  ancaman terhadap negara dalam memperkokoh persatuan bangsa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:51"}, {"kompetensi_dasar_id": "1d7dc6a7-a95e-4ef4-be48-2f8ba344ceef", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:39", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:28:39"}, {"kompetensi_dasar_id": "1d82f791-2330-4920-aa97-b6c691e8e34a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON> pen<PERSON> i<PERSON>asi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "1d834f13-5aaa-4471-b2a8-9dab5c1dd5e7", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:36"}, {"kompetensi_dasar_id": "1d83e33a-89a6-442b-ad3a-d8b2ee6f1801", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:06", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:06"}, {"kompetensi_dasar_id": "1d85b965-bec1-44e0-a8cf-3ed3eeb5ceb7", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "1d866ef5-482a-4838-876b-41aed1518f11", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 827210200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> inovasi pengendalian hama dan penyakit yang menyerang komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "1d86cba4-28dd-44d4-964f-0f0271c9de62", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 817050100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan  pen<PERSON>n sumur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "1d871f1a-6365-4287-bab9-2cb20f459c6a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:34"}, {"kompetensi_dasar_id": "1d8799b3-7813-4bc9-a937-875d0138ecfc", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 829081000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Membuat kue Indonesia dari terigu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "1d883133-c434-420f-84bf-4e0063b36688", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kondisi lingkungan dan kebutuhan dalam menentukan konsep dan gaya eksterior", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "1d8917dc-d25a-4573-a25f-160ba73f85ba", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengoperasikan Sumur Flowing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "1d89676a-23bd-4e93-9394-1bfef3222c65", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 816020100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:29", "updated_at": "2019-11-27 00:29:29", "deleted_at": null, "last_sync": "2019-11-27 00:29:29"}, {"kompetensi_dasar_id": "1d89d78a-348c-4f7f-8633-91823fa1d653", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemasangan dudukan dan modul surya PLTS tipe Solar Home System (SHS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "1d8a8e26-ac26-4a31-980d-bce54b7882f4", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843120500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan background dalam tata panggung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "1d8b06a5-577f-49ed-a1bd-e296dde1b063", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 840020130, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bahan-bahan glasir keramik bakaran rendah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "1d8caec2-9044-4db1-b83a-7f3913379455", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis makna persemba<PERSON>ngan kepada <PERSON>ian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:14:08"}, {"kompetensi_dasar_id": "1d8d129d-02fc-4d89-bf25-b310809e4708", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 817140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil analisis  variable operasi dalam proses pencairan gas alam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "1d8e60f8-4e8b-44cb-889d-701f1f37f041", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 821080300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengoperasian mesin amplas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "1d8f1be8-1bd3-4217-a687-aaa011dfb762", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik editing sekuen montase", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "1d911bea-197c-411b-a916-2dcc5b6b3f79", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 830060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melakukan pemang<PERSON>an rambut pria dengan teknik barber", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:33", "updated_at": "2019-11-27 00:30:33", "deleted_at": null, "last_sync": "2019-11-27 00:30:33"}, {"kompetensi_dasar_id": "1d92b555-b640-4550-a203-53e76d06faa3", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perubahan demokrasi Indonesia dari tahun 1950 sampai dengan era Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:23", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:23"}, {"kompetensi_dasar_id": "1d92befc-6944-4d3f-8151-ca731fc217b7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeval<PERSON><PERSON> kompo<PERSON>, harmoni, dan estetika pada dekorasi dan ornamen eksterior", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "1d934e8b-f707-477d-bc9c-36f4cfb0f079", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk mengucapkan dan merespon ungkapan meminta per<PERSON> (extended), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:40"}, {"kompetensi_dasar_id": "1d93773e-b0d9-42da-8e0f-0e98d1256d02", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Berp<PERSON> aktif   dalam membangun bangsa dan negara Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:10"}, {"kompetensi_dasar_id": "1d941d04-b1a6-414f-8632-903743ea3e79", "id_kompetensi": "2.3.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis musik kreasi  berda<PERSON> ma<PERSON>na , simbo<PERSON>, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:26", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:26"}, {"kompetensi_dasar_id": "1d959ef1-ad72-4426-b1aa-22e84549aa2d", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 842040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> berb<PERSON>i bentuk ukiran kayu tradisionil bentuk trawangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "1d95bdaa-115e-44a1-b478-81b2ff72221a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843090400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik perna<PERSON>san dalam seni teater", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "1d96a676-c997-4a1e-ac16-fc4ba58fb3a1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan modulasi sinyal analog dan sinyal digital pada system radio pemancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "1d9722ae-be86-4044-94bc-dbbe7119604b", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401131110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penggunaan peralatan pengukur temperatur..", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "1d97e7dc-61bd-410e-b1e6-0c21453a5f1e", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 827370500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan pengiriman berita bahaya dengan EPIRB dan INMARSAT", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "1d9961f0-4a02-4ec7-8a41-3489706b947b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 829020400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami hygiene sanitasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "1d99d6e4-1015-42eb-9bf0-2108b65bba94", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:28"}, {"kompetensi_dasar_id": "1d99fc05-2ec5-42cb-a119-ecd9f5513941", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 815010300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pen<PERSON> fak<PERSON>, kons<PERSON><PERSON>, prosedural penanga<PERSON>  mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "1d9a392d-bced-45c7-aa14-9d1d084c3483", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "1d9a40ec-a56b-46c7-b356-5d80d9ef0f2c", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perubahan demokrasi Indonesia dari tahun 1950 sampai dengan era Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "1d9a5653-f91f-4175-9e8c-8ab6a3f4a10c", "id_kompetensi": "4.37", "kompetensi_id": 2, "mata_pelajaran_id": 824050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>t rencana tindak lanjut hasil evaluasi pengawasan proses produksi film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "1d9aa2bf-8449-42a6-9208-5427383c53b4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804101400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis-jenis peralatan proteksi pada unit pembangkit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "1d9c5677-61d0-44d7-9c29-72d03aad8df4", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:43"}, {"kompetensi_dasar_id": "1d9c8af8-bded-4ddb-ada0-df2d721c2cd9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401130610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is pengolahan limbah cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "1d9ce9e3-9871-4d14-9dc0-e2a850174b9a", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang crane pada rumah pembangkit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "1d9d050f-d36d-4f9b-9ce0-c684e8739ecc", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dasar-dasar editing film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "1d9de65d-47f1-4305-b023-c085d76afaaf", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 827210600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun studi kelayakan usaha industry pengolahan hasil perikanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:51", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:51"}, {"kompetensi_dasar_id": "1da01770-098b-4c6f-b789-5dc6418bbf3d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi dalam perlombaan salah satu nomor atletik (jalan cepat, lari, lompat, dan lempar) dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:24"}, {"kompetensi_dasar_id": "1da023d2-1f95-4846-85d7-88fe6dbdbef5", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 800100100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis tugas perkembangan manusia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:24", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-11-27 00:30:24"}, {"kompetensi_dasar_id": "1da04a50-5629-4407-8466-9ed104c3e2c7", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan klasifikasi instrumen kontrol", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "1da150ac-9c57-4461-99ee-33ba9b3acd13", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:06", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:06:06"}, {"kompetensi_dasar_id": "1da238e8-8180-4988-af6a-ae64a2531aa9", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis, pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait kegiatan/tugas-tugas rutin sederhana (simple routine tasks) dengan memperhatikan fungsi social, struktur teks dan unsur kebahasaan yang benar dan sesuai konteks dunia kerja.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:11:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:11:59"}, {"kompetensi_dasar_id": "1da36f71-b9e7-45bb-bc59-e3b5417d3b7b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>  putaran mesin   \r\nberdasarkan kecepatan potong \r\nbahan benda kerja sesuai table \r\nyang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "1da5ffdb-797a-4c80-bcbc-97e96786af15", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks ilmiah faktual (factual report) lisan dan tulis tentang benda, binatang dan gejala/perist<PERSON><PERSON> alam, terkait dengan <PERSON> pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:04"}, {"kompetensi_dasar_id": "1da679d9-9b8e-4e7b-b5c8-32cae9a9d2a8", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar per<PERSON>, tata letak dan detail", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "1da6cad7-8e2f-448b-a202-c21453faf08e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 828190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan program perjalanan wisata", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-12-15 19:11:58", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "1da6ccab-52d8-4638-8d9c-b7272570e437", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:43"}, {"kompetensi_dasar_id": "1da70496-9d9e-4037-bca4-6f82d654c547", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses mesin konversi energi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:42", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:42"}, {"kompetensi_dasar_id": "1da71418-91cd-46ed-8dae-2ed5ea08ab6b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi simplisia dari hewan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "1da7978b-594f-4ee7-8ec3-0173e5b8d5c6", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memperbaiki sistem rem", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-10-18 06:44:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "1da9d0af-2502-49a6-ad98-2e6dda22ddfa", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843060700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis konsep dasar komposisi tari tunggal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "1dabe1c8-b6ad-4acb-a96b-f55933448d4b", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 825270600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis kandungan total bakteri pada bahan hasil pertanian dan hasil olahnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "1dac3a07-5513-4277-bf55-1fcf7a0b3733", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat dua garis sejajar dan saling tegak lurus dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:17", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:17"}, {"kompetensi_dasar_id": "1dacfec3-4a38-4841-a514-227e5bcd3e0d", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807021310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON>an Altimeter berdasarkan prosedur pengujian dan pemasangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "1dad9cc1-7420-421a-b954-0ceb9c9fe92a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pem<PERSON>n kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:50"}, {"kompetensi_dasar_id": "1daf78f0-a27a-4628-8694-5604902dccf8", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 842040300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar produk kriya kayu dengan konstruksi sambungan dasar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "1db045f3-e247-456f-afd8-b7547e9bb89a", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 822030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>un<PERSON>kkan jenis dan macam kopling dan menghitung kekuatan kopling.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "1db0ba17-aa73-49ed-a177-b67e5f78603c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 821060120, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami lambung timbul (Freeboard)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "1db125d9-b5a5-4ce5-b3af-cc2b6bf859c7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 802033200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pewar<PERSON>an gambar latar (coloring) 2D Layout", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "1db17705-2a71-4135-97ac-60ffd822602e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 835010200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep teknik cetak ganda relief", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "1db1ed94-3e81-4f36-9011-874739d7fbef", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 817080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati spesifikasi casing dan pemeliharaan casing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "1db2194a-e5a5-44f9-8850-c2f61a5485e2", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat-sifat transformasi geometri (trans<PERSON>i, refleksi garis, dilatasi dan rotasi) dengan pendekatan koordinat dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "1db365e9-e3f1-4e16-8923-c21f5a219182", "id_kompetensi": "4.94", "kompetensi_id": 2, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencoba dan menerapkan metode pencarian kesalahan pada gerbang dasar rangkaian elektronika digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "1db36ee8-3713-4beb-8bf4-fc623301ef47", "id_kompetensi": "4.44", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pemrograman Function dan Action untuk merespon kondisi tertentu pada proses dengan VBscript atau C Script", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "1db37393-23fc-4894-9769-65d32b1fa238", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 831101300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> gaya desain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:49", "updated_at": "2019-11-27 00:28:49", "deleted_at": null, "last_sync": "2019-11-27 00:28:49"}, {"kompetensi_dasar_id": "1db3bc4b-7bf8-45ec-8f99-007ab7adf4a2", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat zat warna.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "1db44df5-4304-403f-8dd0-3784472bd5c1", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820030400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Sistem Udara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "1db45175-4bfb-430e-bd4d-1ad20101a2de", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar proyeksi orthogonal dan isometrik dari objek gambar atau benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "1db69295-1338-4ae2-a6f0-5094cbb7f580", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji kabel frekuensi radio dan interprestasi spesifikasi data teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "1db6b60a-3c74-4318-a025-33ca52dc360a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis standard ISO mengenai tata letak gambar dan layout kertas gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:30", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:08:30"}, {"kompetensi_dasar_id": "1db82a99-b3c5-4e87-8f90-5bc8917bee1f", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 818010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengukuran dengan alat ukur penyipat datar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "1db92e68-459b-4fea-b223-377694d68b98", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pengai<PERSON> tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "1dbabf4c-64dc-42d9-9928-a569f8f3700a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825250600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan rencana produksi pada pendederan ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "1dbadff0-0beb-4ed4-9a27-a29907b32c03", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:47", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:47"}, {"kompetensi_dasar_id": "1dbbeca0-a9f7-4356-94d6-58ebf3312405", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan teknik pemeliharaan bibit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "1dbc75f1-1dd5-4637-96d5-87f8a3ed0b72", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>an lagu jaman <PERSON>ok tingkat menengah dalam tahap pembacaan dan pematangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "1dbe01c3-0edc-4421-bdce-f7b1cc52e7cf", "id_kompetensi": "4.1.2", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON><PERSON><PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159 dengan lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:02", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:02"}, {"kompetensi_dasar_id": "1dbe4e7e-82e1-4d45-b540-99c57a48374d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:24"}, {"kompetensi_dasar_id": "1dbe8fab-5fd8-422f-a519-9c448b22d152", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803060900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik perawatan dan pemeliharaan peralatan elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "1dbf8b2a-6cb6-4e7d-aebc-4e5b7b6eca1b", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803080900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip operasi mikrokontroller", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "1dc14c7d-6b3e-4c46-80fc-6b62c35889b6", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 842040400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> berbagai pisau mesin kayu stationer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "1dc21e9a-39f4-49be-bd67-f4be61a15bee", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843011730, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik memainkan  instrumen pokok atau vokal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "1dc2c27e-0db4-42d8-89f3-5dfa60bc25dd", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:48"}, {"kompetensi_dasar_id": "1dc33803-5ac5-465e-a8ab-a91fb5a605f3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:39"}, {"kompetensi_dasar_id": "1dc353cc-d01b-401f-afd9-5b9f49f40b22", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825190100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>an prosedur v<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "1dc3ec87-7a95-4aee-9e12-7811112d76cf", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 801030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melakukan interaksi secara efektif terhadap korban NAPZA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "1dc50141-866c-4f4d-8d7e-d299b8780aa5", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804011500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> aturan teknik gambar mesin dan tanda pengerjaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "1dc55b56-5fd1-4736-adbb-6ae9bb872d6b", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 843062000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hubungan antara karawitan sebagai illustrasi dengan cabang seni lain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "1dc58ca7-eaf9-426d-ad9c-56f0ccb6bd9f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800100100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami  penggolongan ilmu yang mempelajari  jiwa manusia  berdasarkan skema penggolongan psikologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-10-19 23:19:21", "deleted_at": null, "last_sync": "2019-06-15 15:06:48"}, {"kompetensi_dasar_id": "1dc59589-89a1-4a1e-a4b5-652c47e953c7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem penerangan dan panel instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "1dc636de-d3cd-4210-a573-018a19bf61e2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "1dc8a143-cd55-4c95-96a7-3c791ff58c28", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kostum untuk produksi film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "1dc984db-2354-4452-bfbb-d93bcfb5e6ab", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis continuity sound", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "1dcbd242-41ef-4d35-8a02-c9160a8c2149", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan datasheet dioda zener untuk keperluan eksperimen.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "1dcc1e1c-8cbf-499f-8abb-bb0c9ddd36bb", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang sistem  instalasi pembumian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "1dcc666a-a431-4291-838a-bbfe1a4c9bca", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:37", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:37"}, {"kompetensi_dasar_id": "1dccec16-a8ef-4793-9527-0243fdf1908f", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, teknik dan isi tarian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "1dcd76cc-13b7-4fa0-8446-ec426f692d8a", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 826050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengujian mutu benih tanaman hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "1dce53b8-ed8d-4212-a694-6f2f19eebe9f", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:15"}, {"kompetensi_dasar_id": "1dcf560b-0f4c-4647-804c-26acc479a9f3", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan per<PERSON>an PPh Badan terutang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "1dcf73e5-85e1-4182-ae4e-ecc6f858aba8", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 816040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memb<PERSON>t desain/gambarpencapan kasa putar dengan komputer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "1dcfba33-c0b5-428e-b06d-9c4547735e4b", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menggunakan berbagai ukuran pemusatan, letak dan penyebaran data sesuai dengan karakteristik data melalui aturan dan rumus serta menafsirkan dan mengkomunikasikannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:16", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:16"}, {"kompetensi_dasar_id": "1dcffbd7-2f33-4996-8bec-0cb429795715", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825240200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan pembukuan administrasi produksi produk diversif<PERSON><PERSON> hasil perika<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "1dd04c0f-4095-4677-bbf2-2ea672ad248a", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 828010104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menerapkan pencatatan transaksi bukti peneri<PERSON>an kas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "1dd36334-057b-4711-9a7b-d602ada5a762", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:40"}, {"kompetensi_dasar_id": "1dd3915d-971b-4624-be1f-ddf742848a4f", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:58"}, {"kompetensi_dasar_id": "1dd4dad8-0073-48d8-a257-60c84e245386", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan kecepatan putar mesin frais untuk berbagai macam kecepatan potong bahan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "1dd52ef6-9858-47cb-975f-50c44202a078", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 802010700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data server melalui aplikasi web client mobile", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "1dd54a76-39a7-4dec-a0d8-2676872ec699", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:44", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:01:44"}, {"kompetensi_dasar_id": "1dd552ab-c782-4736-9a27-fae7359cf2e6", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825250600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara memperbaiki wadah pendederan yang rusak/bocor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "1dd5de12-277c-42a9-9e23-cf6997ca0db6", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 830090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penataan sanggul dendeng", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "1dd60351-e079-47ee-887c-8cb3d280a660", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:41", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:41"}, {"kompetensi_dasar_id": "1dd68038-2142-43ac-89e7-ed822760b4ff", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan program komputer  dalam mengontrol proses  pen<PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:33", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:33"}, {"kompetensi_dasar_id": "1dd792e9-e4b8-4a95-9c8a-66969b5ce3d9", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401141400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi yang mempengaruhi kesetimbangan kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:11", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:11"}, {"kompetensi_dasar_id": "1dda0b76-57ce-465f-8e7f-e8c4d337faf3", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melacak gangguan pada sistem kontrol Run-Jogging Forward-Reverse berbasis kontaktor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "1ddbe8ea-028e-4b35-b9c0-10051c462f7c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:45"}, {"kompetensi_dasar_id": "1ddc821b-f48b-428d-9fc6-2344b71dfd1e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan komputerisasi file data akuntansi untuk perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "1ddccdea-8155-4e5e-9e60-7234cd661420", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401131110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> peralatan proses pem<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "1ddea6e3-85cd-42a5-88fb-9d80eaf3e9d0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:17"}, {"kompetensi_dasar_id": "1dded88d-1ec9-401b-b699-a82a94c19ecb", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perolehan data foto udara dan karakteristik citra", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "1ddfbd9a-2fa0-44f0-98d4-33267d4cce69", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825200100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penanganan ternak di RPH", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "1de12836-92a4-4f18-97ed-dd7c7f004096", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 818010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengolahan bahan galian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "1de15efd-bce9-4aae-abfb-e45c3404a149", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 822130100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengetahuan dasar dan prosedur perencanaan anggaran biaya konstruksi bangunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:32", "updated_at": "2019-11-27 00:27:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:32"}, {"kompetensi_dasar_id": "1de1cca9-4072-4ae1-9bb2-e2b5b8ae5135", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825230500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menggunakan SCADA (Supervisory Control and data acquisition) untuk mengontrol sistem otomasi pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:46", "updated_at": "2019-11-27 00:28:46", "deleted_at": null, "last_sync": "2019-11-27 00:28:46"}, {"kompetensi_dasar_id": "1de23009-8b6f-4a08-9a23-302ab2a3a79d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 801031500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Men<PERSON><PERSON><PERSON> hasil asesmen dalam praktik peker<PERSON>an sosial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "1de280db-7a0a-4cee-bf5f-4396037d1ba4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803071200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambarkan terjadinya gambar pada TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "1de3e968-3110-41b3-b2ab-83e1e25942e0", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan spesifikasi dan karakteristik kayu  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:04", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:04"}, {"kompetensi_dasar_id": "1de40aab-d6a0-4825-9b78-8f5715f6827b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (K3LH) dalam kegiatan laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "1de4edd7-4659-4381-88b1-3ddcbf153109", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja macam - macam transmitter pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "1de6d812-3e96-4bf4-84a2-5eeac4802338", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825240300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembukuan administrasi produksi produk mollusca, crustacea dan pengalengan standar eksport", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "1de7c505-0032-49cd-8950-30cfde57b99d", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> dasar-dasar sensor", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:25"}, {"kompetensi_dasar_id": "1de7c8bb-8afd-4436-85fa-f336ce65fa1b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 824060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rundown  features", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "1de9026c-eb12-49cd-b6ed-dd57c5253b19", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401251080, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sumber-sumber dana dan pembiayaan yang diberikan perusahaan modal ventura", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2019-06-15 15:07:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:15"}, {"kompetensi_dasar_id": "1de9aba2-9d49-4ff2-9fe6-db5b36dfc8d1", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja(rancangan) pemasangan instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:54", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:54"}, {"kompetensi_dasar_id": "1dea1d12-e553-4b8d-aab4-8dde12a09847", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:19", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:19"}, {"kompetensi_dasar_id": "1deae015-2322-40c3-902d-df3c76b6bc9b", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804050900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemasangan beton pra cetak berdasarkan persyaratan dan ketentuan yang berlaku", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "1dece739-5e4a-4467-b35a-a8e0cb98efb2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827350106, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan hubungan kemanusiaan dan tanggung jawab sosial di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "1ded3a4d-cab9-4d66-86dc-9a1f600ef7a4", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804101500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur per<PERSON>ikan perala<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "1dee557f-17b0-4f9a-ac5e-9c554c5b75e9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 802031400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik memegang kamera", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "1dee5760-7529-4ba0-bcab-ac29640d8c4a", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dasar-dasar dan symbol pada system pneumatik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:26"}, {"kompetensi_dasar_id": "1dee6005-beb5-4c3f-bf37-64f835f309ca", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827210500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengambilan contoh pada kegiatan industri perikanan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "1defcdd1-633a-4d9b-83b2-6a35a310599d", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 600090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengan<PERSON><PERSON> keluhan p<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:30", "updated_at": "2019-11-27 00:30:30", "deleted_at": null, "last_sync": "2019-11-27 00:30:30"}, {"kompetensi_dasar_id": "1defe59d-b117-49cd-a744-72200ee514a0", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 839020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan macam-macam program komputer untuk menggambar nirmana, huruf, bentuk, dan ornamen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "1defed95-04a4-46c1-9772-bcab8d82110d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843120500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memverifikasi perlengkapan pan<PERSON>ung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "1df00129-3177-4ffb-ad2f-4a16ad1fbaba", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep pada Turboprop, TurboShaft, turbojet, turbofan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "1df2100c-2072-4b4e-9204-abd0a70859ac", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, menyajikan model mate<PERSON>ika dan menye<PERSON><PERSON><PERSON> masalah keseharian yang berkaitan dengan barisan dan deret aritmetika, geometri dan yang la<PERSON>ya.", "kompetensi_dasar_alias": "<p><PERSON>y<span>elesaikan\r\nmasalah yang&nbsp;</span>berkaitan dengan\r\npenyajian\r\ndata hasil pengukuran\r\ndan\r\npencacahan dalam tabel distribusi frekuensi\r\ndan histogram</p>", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "1df6659e-45f9-428b-b536-3148d4f30c3f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks yang menyatakan fakta dan pendapat, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:12"}, {"kompetensi_dasar_id": "1df787f8-fa26-4a61-8c4c-6abd1adb211e", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 817140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi hambatan yang terjad selama proses pencairan gas alam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "1df7af1d-a455-42b5-b5b4-d50198447f54", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 814032000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "       Memahami informasi yang diperlukan dalam evaluasi pengambilan barang kiriman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "1df7d63d-84df-4f49-ab77-448d01cb3538", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari surat lamaran kerja, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "1df9ee07-a46d-414c-bdb1-e348fd859d11", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 802030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta desain grafis packaging dos ulang tahun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-10-19 23:19:22", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "1dfbfd7f-55c2-4cb6-8116-11091e628fdc", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 843011100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data sejarah perkembangan alat musik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "1dfc4f5d-4cdd-4002-aef3-b2d82487d8b1", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan  tata laksana beng<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "1dfc7744-dff6-4a5a-a50b-7ca8991519ef", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik yang berfungsi untuk membangun nuansa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "1dfe96b4-b46b-4663-8750-b60809241b5b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825050300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik pengoperasian alat Laboratorium Pengujian Mutu Benih", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:06", "updated_at": "2022-10-19 23:19:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:06"}, {"kompetensi_dasar_id": "1dfeb3ef-c137-4fb8-86e5-c4d21ce3b0d2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komponen FET dan MOSFET sebagai penguat daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "1dffd9c3-b491-443a-befd-3ed122d9b6c2", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 806010200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki unit refrigerasi komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2019-11-27 00:29:16", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}, {"kompetensi_dasar_id": "1e002a61-d836-41a4-9af3-020a98f34e49", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 807020300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Water/Waste system (ATA 38)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-10-18 06:43:58", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "1e011814-c785-4214-b339-e61e0c3b11da", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:14"}, {"kompetensi_dasar_id": "1e01a68b-5770-4062-9ab7-142807daca1d", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi ragam gerak tari tradisi putri berdasarkan karakter tarian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "1e03404e-4d14-41bc-9b6a-f02c5cd21604", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur teknik pembuatan benda kerja rakitan pada mesin bubut, dengan menggunakan berbagai cara/teknik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "1e03f7ca-a1f8-43c8-96d8-004a05a1ba3e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "1e04327d-4039-4f44-9cdf-f3de15b06881", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi interpersonal lisan dan tulis sederhana yang melibatkan tindakan memberikan ucapan selamat bersayap (extended), dan responnya dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "1e0484a8-ea60-40d0-afd3-1209a1ed1cdf", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis repertoar ansambel/orkestra jaman <PERSON>tik level menengah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "1e059306-dc56-4778-85f4-2dc6f59660e4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "1e0c2000-0b46-411f-99a6-0615239d1b2a", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 824060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is be<PERSON> camera movement", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "1e0ca3e8-452e-461a-b470-c56b13d7bf21", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> macam-macam susunan bentuk teks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "1e0eb82b-7057-4382-b30e-707f8f4d98bb", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemesinan bubut  kompleks untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:28", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:07:28"}, {"kompetensi_dasar_id": "1e0ef0a0-f4a6-4727-ba67-9f89632f1ff8", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827351000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi klasifikasi mesin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "1e10bfc3-0243-4577-9cee-b46b63385d84", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> keli<PERSON>n sederhana", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:52", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:52"}, {"kompetensi_dasar_id": "1e10eed8-7e49-457b-9002-6d03bcd93921", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:14"}, {"kompetensi_dasar_id": "1e133a43-1791-460d-9c7c-4c6362805050", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerpkan prosedur pelapisan saluran irigasi dengan beton", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "1e136b24-d467-433d-a656-fa149499335e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800081000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis sampel yang representatif untuk pengujian histologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2019-11-27 00:30:20", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "1e1410c5-be83-4194-b45a-10952acbc69c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> peran pelajar, ma<PERSON><PERSON><PERSON> dan tokoh masyarakat dalam perubahan politik dan ketatanegaraan Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "1e1424e3-6c98-4f73-bc3c-e589d20f704d", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 806010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun table pengujian relai proteksi sistem tenaga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "1e1816ab-35fd-42cb-a463-6f0e261914cc", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804010500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan jenis-jenis software aplikasi untuk gamba rangkaian kontrol listrik dan rangkaian elektronika", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "1e195d50-e7f9-4dd0-a654-9fd62016fcda", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Be<PERSON><PERSON> aktif dalam menjunjung kehidupan yang multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:20"}, {"kompetensi_dasar_id": "1e1a8778-1e09-4042-aef8-b3b6c6c8ed46", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:10"}, {"kompetensi_dasar_id": "1e1b831f-aa6b-4125-93b5-d668a11d3364", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengelasan pelat dengan pelat berbagai posisi pada sambungan sudut dan tumpul : posisi di bawah tangan dan posisi mendatar menggunakan las oksi asetilin.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "1e1c8e47-d0a4-4602-be6a-f5825a4d1129", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur penyiapan laboratorium untuk analisa rutin", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:13"}, {"kompetensi_dasar_id": "1e1db809-d70c-43f7-b988-6ba9d4738695", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "1e1e7306-a98a-47bc-9e8e-49172fd8fa50", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 835010200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta cetakan tuggal/rusak patung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "1e1e8762-a26d-42b0-87fd-6223ce26d32a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 802031600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>  e<PERSON><PERSON> k<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "1e1f7bce-9bb8-47a2-9f9b-111df31b76c6", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 830080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa desain pewarnaan rambut komersial dan artistik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "1e2058b8-3c9c-4783-8469-0c8b0ff13109", "id_kompetensi": "4.77", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan ekperimen elektrolisa dari sel bahan bakar tipe Proton Exchange Membrane (PEM) dan menerapkan sumber energi listrik sel bahan bakar (fuel cells)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "1e234ed6-1d0f-4f35-8070-6395d5997bd8", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 821170100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelas pelat baja dengan proses las SMAW pada posisi vertical (3F)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "1e236105-2259-4f5b-b47d-31956390f331", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 814170200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan tindakan perawatan dan perbaikan mesin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2019-11-27 00:29:16", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}, {"kompetensi_dasar_id": "1e248b84-ef88-4e77-974e-a92642a96014", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "1e24a6a8-482c-479a-a452-abce9cd27d0e", "id_kompetensi": "3.35", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi spesif<PERSON><PERSON> kompo<PERSON>, peralatan dan prosedur kerja PLTS", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "1e262b02-a338-434a-a849-656df2b75a56", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengulas jenis iklan radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "1e263d63-7d26-472c-b28c-8b1c1e1fa7fd", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825020400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengendalian hama dan penyakit secara fisik, kimiawi  dan terpadu sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "1e269ba5-e1f4-4c58-96cd-7fe2d33da975", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "1e27bc2e-3a86-485f-a6be-815ecfeb0f9b", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:35", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:01:35"}, {"kompetensi_dasar_id": "1e2821b5-eb5c-4bcf-b84a-eafc5db82c0d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401131700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses penye<PERSON><PERSON> kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "1e289d03-ed96-49a6-bf2c-8a4284fd1265", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis persiapan pengoperasian Traktor Pertanian Roda 2", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "1e292882-b998-4445-8f73-ee9c19b76e46", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800061300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan sterilisasi alat teknik basah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "1e29b79a-e50f-4b3d-9f0b-1f86c5d2b7b3", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis strategi yang diterapkan negara Indonesia dalam menyelesaikan  ancaman terhadap negara dalam memperkokoh persatuan bangsa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:20"}, {"kompetensi_dasar_id": "1e2b0138-d482-4d66-8604-040c37853850", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data dari situasi nyata, memi<PERSON>h variabel dan mengkomunikasikannya dalam bentuk model matematika berupa persa<PERSON> fungsi, serta menerapkan konsep dan sifat turunan fungsi dalam memecahkan masalah maximum dan minimum.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "1e2b3e44-cc81-40e8-a1b2-566675575632", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825210200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses pengg<PERSON>an suhu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:49", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "1e2c0a57-a7b0-4c31-af68-84d39d57687c", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 825060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi produksi pembibitan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:02", "updated_at": "2019-11-27 00:28:02", "deleted_at": null, "last_sync": "2019-11-27 00:28:02"}, {"kompetensi_dasar_id": "1e2c5b55-da64-4db3-8eb0-2ae516e8d2d8", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 800061300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan sterilisasi bahan kain dengan autoclove", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "1e2c816a-64d2-4972-99af-59536e782faf", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:05"}, {"kompetensi_dasar_id": "1e2de0b9-fb3c-4351-8206-5f5d11e4715c", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "1e2eda53-4eea-4c4d-aa11-56722c781841", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843120400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyalin data komponen lampu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "1e3013cf-0dc1-48ed-8776-e6cf7d24a473", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 839110200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembuatan desain produk kulit non alas kaki dan non busana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "1e30d244-d01a-4ab7-b470-dccde29689f7", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 824050600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> fokus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "1e31ffd1-f3e1-40c0-8474-cb24339f7232", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827040100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pelayanan medis di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "1e34a77a-6cbe-4ee2-b6a6-cd5dcb104e02", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis beban tekan, tarik, punter dan lengkung dan material pendukung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "1e3503cb-bf91-4f63-8edd-af1a23392216", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 803081000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi Sistem Kontrol Elektropnumatik berbasis rele", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "1e361339-0270-472d-aee6-b23b93afbd12", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik penyelesaian akhir tenun kepar dengan teknik makrame", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "1e372cff-f305-4868-8fa4-f3b5f62cc479", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:59", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:59"}, {"kompetensi_dasar_id": "1e377e8d-56b3-4274-8bdc-a8878af60750", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 843062400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan repertoar gending/lagu kategori dasar karawitan mandiri irama tertentu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "1e37e040-777a-459c-894b-409c63b7a0ce", "id_kompetensi": "3.8", "kompetensi_id": 2, "mata_pelajaran_id": 401130610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengujian sifat unsur dan senyawa golongan transisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "1e3944f8-c67d-402e-92fe-58576cab0b9a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843030100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat resume  sejarah seni rupa Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "1e397e4a-569b-44de-bc1e-fcded75b3826", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:20"}, {"kompetensi_dasar_id": "1e39ffe7-5ad5-4ce6-8bba-c81fab6901a2", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ungkapan terkait pesan singkat melalui telpon, fax, email, surat dengan memperhatikan fungsi sosial struktur teks dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:03", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:42"}, {"kompetensi_dasar_id": "1e3a22f8-cd12-4e9e-bed5-b64c83732727", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 821070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil teknologi pembangunan kapal baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "1e3a3a58-cb4c-430a-aa11-ae3c296f1fec", "id_kompetensi": "3.50", "kompetensi_id": 1, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis jenis-jenis system control untuk robot mobile", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "1e3a6fc0-d38b-42ca-aea6-1904c0177ab8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> spesif<PERSON><PERSON> dan karakteristik beton.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "1e3adb9b-24f2-4955-86bb-10ad511ceac9", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:01"}, {"kompetensi_dasar_id": "1e3bad80-1a8c-4b7e-af26-b57d250d53a2", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:08"}, {"kompetensi_dasar_id": "1e3c01ca-c673-440b-9582-b85668496e79", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843120300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "1e3d0c7e-13ae-49ca-8fc6-141836f1d43f", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pengendalian hama dan penyakit pada pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "1e3d57e7-a252-40fb-9a1e-5ccf0f3a7867", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:01", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:01"}, {"kompetensi_dasar_id": "1e40b647-ca51-4d18-bfc5-d44bf446a3ab", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 801031300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelompokkan jenis-jenis rehabilitasi sosial penyandang disabilitas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "1e411614-e2c8-43ab-96ff-c1407cb0d135", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 814032000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "      Menerapkan tata cara pemeriksaan barang kiriman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "1e41548b-e76f-488b-a9c9-2ff612c54134", "id_kompetensi": "2.3.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penulisan partitur musik sesuai  makna, simbol, dan nilai estetis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "1e423999-c43b-4834-a48d-86a1f7470860", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 600090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menganalisis pemberian tanggapan terhadap keluhan pelanggan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:30", "updated_at": "2019-11-27 00:30:30", "deleted_at": null, "last_sync": "2019-11-27 00:30:30"}, {"kompetensi_dasar_id": "1e42608c-8943-4b5f-b0f9-61ca20a36c1c", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemasangan dudukan dan modul surya PLTS tipe Ground Mounted", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "1e43a3db-a4ed-4b9d-aece-cd615f6ac5b3", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan sediaan tinctura", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "1e43f625-b704-4e75-94b0-b1fec194b119", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin bubut, dengan su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:59"}, {"kompetensi_dasar_id": "1e440ac6-cef8-4890-af5b-28740a490fc1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "1e444bba-cea9-46cd-93d6-58668a3ec9c5", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 839100200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membentuk sepatu sebagai produk alas kaki", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "1e451e2f-82e0-48c0-866a-4844477aaf14", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pengoperasian inkubator untuk pemeriksaan laboratorium medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "1e45cce7-5d08-4dcc-bcd8-6484561fd836", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sifat dan karakteristik bahan untuk analisis kualitaitf (analisis jenis)  metode klasik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "1e460065-8351-45ce-b8bd-4915837f1476", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825030300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      <PERSON><PERSON><PERSON> prosedur dan teknik pembentukan tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "1e46bdba-5710-46bf-8fa7-6ddf1b572cda", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:31"}, {"kompetensi_dasar_id": "1e474b0f-c5e6-4019-944b-74bae6ee0948", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 828180110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung single extra charge", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "1e47cb55-0cf5-4f35-985e-6053b64fc59f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 800061400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan keperawatan dasar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "1e486ae5-4a6a-481c-a451-284fc76f085e", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 814030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> prosedur pengendalian mutu barang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "1e48e197-c496-4e3e-8194-af200d0a4089", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bahan-bahan listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "1e499cd7-1252-4323-876e-559d953a5407", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 830050040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pen<PERSON>ahuan dasar SPA Therapy meliputi definisi, sejar<PERSON> dan terminologi SPA)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "1e49da11-e4eb-4b85-83ea-f12fed707dc5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin frais, dengan  su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:34", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:34"}, {"kompetensi_dasar_id": "1e4a8924-a335-42b8-9015-0404186905df", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401251330, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses pela<PERSON>an produk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "1e4b0e54-da8d-4de2-93c8-4480748c88d0", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang jig dan ficture sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "1e4bc13c-43f1-479a-9029-fd95ef28e3af", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 843070300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan repertoar lagu non ritmis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "1e4cd81b-d1e8-4e58-8a7b-dbfea87d4889", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 804051200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan laminasi kayu secara manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "1e4ced31-c8e9-4ce6-8b6b-1e8b086b63b1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "1e4d3225-cbc5-4de4-85b0-35503ab1eae2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827270100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis mutu hasil per<PERSON>nan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "1e4d87c6-3b05-4538-9747-8e1ca6e75787", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:58"}, {"kompetensi_dasar_id": "1e4d9446-bddc-484a-bb7d-10b660007985", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827110350, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> rangkaian sistem pengendali mesin utama (main engine)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "1e4e1fc7-82c2-4bc9-b52d-7a9563adf87b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan administrasi fasilitas   laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:56"}, {"kompetensi_dasar_id": "1e4e2dcc-823a-4f70-824d-0173bab88687", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 821170100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelas pelat baja dengan proses las SMAW pada posisi overhead/atas kepala (4F)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "1e4ec2a0-73b5-4b5b-8558-b5491f43513a", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 820130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pemeriksaan  under carriage alat berat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "1e4ee817-7fc6-4e1d-a888-9865ed694045", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "1e508952-d6e4-4aaf-90b5-2e9807097b2a", "id_kompetensi": "3.37", "kompetensi_id": 1, "mata_pelajaran_id": 824060100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi ide kreatif untuk memproduksi sandiwara radio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "1e50cd71-bffc-408e-bd75-109d2944b339", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "1e50de3b-b161-4944-8941-ed44dda5e121", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 827390800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply appropriate specialized tools and measuring instrument", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "1e518082-9b95-40bb-aaf6-adc08fb12d95", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 831102000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip memusatkan dalam penyusunan unsur-unsur rupa dua dimensional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "1e51a418-10cd-4d98-8369-b4caacf0b5d1", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 801031100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> solusi layanan pendampingan pada anak berkeb<PERSON><PERSON>an khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-11-27 00:30:26", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "1e52e929-b8eb-48a2-8176-2685bfd35e4b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825060300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON> jeni<PERSON> pen<PERSON>t infectius pada ternak (ruminansia, unggas dan aneka ternak)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "1e542404-c9e0-4647-af68-f5915b3c0488", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "1e545561-88cf-4b7d-83d9-edc8b05391f1", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan faktor-faktor kemajuan dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:37", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:05:37"}, {"kompetensi_dasar_id": "1e54cbe8-9d0e-4d72-8f88-9c3870c6a8b7", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:59"}, {"kompetensi_dasar_id": "1e564bf4-284a-4a33-831e-77b3b5eb9c01", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 827290400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaplikasikan teknik penanganan rumput laut kering tawar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "1e568319-3a94-4855-a45e-a702c5e35072", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804100500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan teknik-tenik pemasangan jaringan transmisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "1e57a3cd-17c5-44c1-981c-70c66026fb62", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 825230100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prinsip dasar pengolahan hasil serealia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "1e5818d5-5e4a-4c6a-86e8-ad2015c8de5c", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemijahan ikan hias secara semi buatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "1e5992e6-9008-42c3-9e3a-ce5582bda27d", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 803071500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengatasi masalah pesawat telepon", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "1e59f240-7014-4daa-9fd9-0b6daa683b5b", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan diversifikasi produk hasil panen komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "1e5a134a-8bf6-4cc2-b7cb-47f8571fba19", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterpretasi makna teks cerita sejarah, berita, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:17"}, {"kompetensi_dasar_id": "1e5ab540-53a3-452f-8f36-818d15733ed7", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 803081000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melacak gangguan pada Sistem Kontrol Elektropnumatik berbasis komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "1e5bec8e-0f8c-45c8-b9a2-f3aac0706560", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung estimasi hasil produksi ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "1e5de55f-8f61-4a39-940d-3f84a66ae5bb", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825190100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemberian obat hewan secara intramamae.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "1e5e10c3-ea63-4ca2-a4fd-f2e139e62e26", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 824060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rundown Talkshow", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}]