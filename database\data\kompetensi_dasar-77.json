[{"kompetensi_dasar_id": "730e2e6e-395d-47ce-ba08-cf3b712057b0", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang instalasi pemipaan sistem tata udara komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "730ed47c-47a5-4336-81f5-b77d29e91fa7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahan mesin <PERSON>in kapal <PERSON>nan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "730f0775-c17b-4d69-a85c-5bdacc2be6d7", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827351000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi oily water separation", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "730fb2f8-95b8-47f8-bd6e-1056e842b40c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 805010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik pengecekan alat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2019-06-15 14:49:58", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "7310adb6-ac15-479d-ac6e-228b6225b83a", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih dan menerapkan strategi menyelesaikan masalah dunia nyatadan matematika yang melibatkan turunan dan integral tak tentu dan memeriksa kebenaran langkah-langkahnya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:23", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:23"}, {"kompetensi_dasar_id": "7311595d-f5ff-4c6c-91cb-7a9810d4706c", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:17", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:17"}, {"kompetensi_dasar_id": "7313ddae-58c7-4f05-87ce-ec2e602e1be4", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis plot sandiwara radio yang \"mengalir\"", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "7313df03-7b6e-42ae-aa73-61fea9fae081", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penyebab gangguan dan kerusakan alat dan mesin produksi pertanian, laboratorium, klimatologi, pemetaan lahan, penyimpanan dan prosesing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "73141a9f-fb12-4fd1-b089-5c1cf9ac4512", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 822080110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki Sistem Kontrol EMISI", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "73148da5-95f9-48e5-988c-a9f8a502028b", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan cara pengoperasian sistem SCADA pada unit pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "7315124a-7083-4fcb-a820-1f226911673b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 820140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis awal kerusakan pada sistem kendaraan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "731650b1-efe8-4afe-98a6-22fcc065fbed", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 825063500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON> pen<PERSON>nan hasil produksi susu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "7317136e-a70f-47e8-8452-6c26ad31d865", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> gambar kerja yangb \r\nakan dikerjakan pada mesin \r\n<PERSON>ais", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "7317913f-0a21-4769-a29b-840b15623bcd", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 829050500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>t garnish makanan dan minuman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-12-11 11:03:44", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "7318f013-20e6-4c71-9b7b-5bd1acb482d9", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin bubut, dengan su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:31", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:31"}, {"kompetensi_dasar_id": "7319b9cf-1699-4235-a0c0-aefca0e23d5c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "731bdd79-c42a-460c-b8f3-22c5a5901300", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401131900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pencapan dengan metode resis printing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "731c9586-e1be-4b5d-aa45-5918a74d2577", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:55", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:02:55"}, {"kompetensi_dasar_id": "731cb258-282a-49fa-96a5-8d6767b479a1", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:07", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:07"}, {"kompetensi_dasar_id": "731dfc05-8088-4b64-93b9-3ce210e4cda5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan  putaran mesin  \r\nberdasarkan kecepatan potong  \r\nbahan benda kerja sesuai \r\ntable yang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:12", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:12"}, {"kompetensi_dasar_id": "731eabba-abed-4748-a7eb-a7aa74358a05", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827351000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan pesawat pengubah panas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "731fc2d7-bb0e-4af5-97e0-ae73949ee5a8", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "731fe306-1ad2-4c41-ab55-ac2b9847d000", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:50"}, {"kompetensi_dasar_id": "73200c9d-443a-470d-b7cf-aad6ae523980", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 802031210, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengatur pencitraan cahaya digital pada animasi 3D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "73205c25-16ec-4e70-a755-1589668a9f7d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 843090800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah   unsur dinamika gerak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2019-06-15 14:58:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "7320ac51-c6ef-4719-93c0-d0ab1dc6b829", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan langkah-langkah pembuatan sinopsis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "732177ee-a5ef-45ac-84fe-cc42e4588107", "id_kompetensi": "2.4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan membaca partitur lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:21", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:30:21"}, {"kompetensi_dasar_id": "7323ed5c-1972-43b9-98ef-983dce642f6b", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 800030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>an sampel produk antara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "73249611-f587-42d2-bc76-d265e0bb427d", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem penerangan dan panel instrument", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "73250e3c-937f-4a5a-8774-e4a1d21a49b1", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 843062100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkreasikan teknik pengembangan melodi instrumen dan vokal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "73253191-3a72-4513-9823-8c86f156a532", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengujian sistem instalasi air bersih, air panas, udara tekan dan gas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "73260c33-9e5f-41e7-be15-7df724b972ed", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401251021, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>laskan entri jurnal ke dalam buku harian perusa<PERSON>an jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2019-06-15 15:07:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "7326ed93-2219-4a1f-8bec-21de27805c2f", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis komunikasi mikrokontroler dengan komputer (interfacing).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "732881b2-e49f-4fcc-95e0-1e42d50ba22b", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 840040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan  proses dekorasi glasir  benda keramik (under glaze,  over glaze, dan in glaze)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "7329351e-56d1-4969-a654-bb2bbeca2a1c", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON> sika<PERSON> k<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2019-11-27 00:27:49", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "7329e1ab-9714-4f11-9be7-b1bbd69acf5d", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 820040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pendinginan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "732a05d8-e67c-4216-9bb2-f55a261fe0fe", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:34", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:34"}, {"kompetensi_dasar_id": "732a4302-397e-419e-adb3-37ec28432dde", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 827210200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan rekayasa pertumbuhan komoditas budidaya perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "732ac5a3-07f4-407c-9abc-55b4c862f0e2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 800060920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengoperasian alat water tank dan pendukung lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "732aed20-bc40-44ce-ac56-0fae3529acbd", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "732c6e08-2200-4aa6-b086-22d08e9eea80", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyajikan ruang lingkup laboratorium kesehatan hewan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "732d0510-8cc5-4c5b-9fbb-08b42266ddb8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 811010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara pemotretan berbagai model (garis, nada leng<PERSON>, dan warna) dengan kamera vertikal/horisontal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "732e11b1-1a17-46cc-b2c3-3bedccea8d12", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 807022210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Oil Pressure Indicator", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "732e6a0f-e9ed-4484-8f6e-dfb1431d7e72", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804101400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perawatan peralatan proteksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "732f079b-7b6b-45d3-a3fd-b8e22f149266", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "732fa95d-731b-4782-9184-42242f3ac3db", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 821130400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara kerja katup pengatur kecepat-an silinder.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "7331b4ec-9a54-4689-873c-227b72468812", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 827090110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil perawatan alat tangkap ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "73320e3f-6714-4645-88e9-1d28d5510cbc", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep lingkungan hidup sehat dalam pengelolaan limbah ternak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "7332de1d-0274-47aa-8b3d-20ebef19bd0f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan garis-garis gambar teknik berdasarkan bentuk dan fungsi garis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "73331865-20b7-4cae-b604-d2b4d8f18c53", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 843080500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan monolog pada pedalangan utuh dalam cerita Mahabarata at<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "7334331e-977b-4f2e-90de-05f61e103e62", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 801031600, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami layanan kesejahteraan sosial (LKS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "73343d4e-95d9-42a9-a900-20eedf3fdc53", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>pta karya pengolahan dari bahan nabati dan hewani menjadi produk kesehatan yang berkembang di wilayah setempat dan lainnya sesuai  teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:16", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:16"}, {"kompetensi_dasar_id": "7334ec71-baeb-4bb0-9b3a-e4f1903ba3d1", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik instrumen (technique of the instrumen) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "7335bb84-c5fe-41f3-b2e3-aa89a667f79f", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melakukan penataan bahan kimia sesuai sifat dan data keselamatan  bahan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:58"}, {"kompetensi_dasar_id": "73368a98-b098-4f3c-8bc2-9cd150812797", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON>ak<PERSON><PERSON> n<PERSON>i", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "73368e1c-12e9-48ba-81c4-c3e0d26bc449", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola kecil dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:55", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:02:55"}, {"kompetensi_dasar_id": "7336df5b-823a-4c81-88c0-a5c8f2b37266", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803080910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi performansi rangkaian sistem kontrol", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "733813a6-44fe-4251-860d-a31235ec0ee7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "73385f56-efe1-430a-a9c6-ca0db6651894", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825250500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rekayasa wadah pembesaran untuk daerah perkotaan yang ramah lingkungan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "73388c9c-5b7f-4b37-81a5-9c3b8fc739ca", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan endoparasit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "7338c24c-c583-41b5-9062-52c37a866831", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki  kes<PERSON>han rang<PERSON>an Op-Amp.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "7338da8d-65ce-4ff6-920b-f397f1a9efb6", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan berbagai konsep dan prinsip permutasi dan kombinasi dalam pemecahan masalah nyata.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:53"}, {"kompetensi_dasar_id": "733928b5-6a97-4004-a05a-1ea3504c4117", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827350800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami cara perawatan dan perbaikan alat-alat penolong di atas kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "7339b9ee-1056-4003-829a-0b979bf6b090", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "menerapkan cara kerja proses roda", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:45", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 16:08:45"}, {"kompetensi_dasar_id": "733a3265-965f-41e3-a1f3-3cab73f76546", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami penggunaan menu program CAD 3D untuk menggambar ruang dapur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "733aea21-d309-4e86-acdf-9ad5e8f379bc", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyiapkan laboratorium untuk analisa rutin", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "733c1118-42d7-4685-8913-8411ebd200ef", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih dan menerapkan strategi menyelesaikan masalah dunia nyatadan matematika yang melibatkan turunan dan integral tak tentu dan memeriksa kebenaran langkah-langkahnya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:02"}, {"kompetensi_dasar_id": "733c9ef2-4e7e-4871-84ff-b2fd286d4845", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 821200300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan prosedur pengecatan dengan spray gun", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "733cf689-c83e-43e7-86dc-515298fec1eb", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:16"}, {"kompetensi_dasar_id": "733e1f16-2fa0-4597-8ad7-70aaa04f34ab", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis tentang faktor-faktor pembentuk integrasi nasional dalam bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:05", "updated_at": "2019-11-27 00:30:05", "deleted_at": null, "last_sync": "2019-11-27 00:30:05"}, {"kompetensi_dasar_id": "733f3b0b-9d2d-4e62-af95-6ee4af1e9e46", "id_kompetensi": "2.4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan membaca partitur lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:26", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:26"}, {"kompetensi_dasar_id": "733fbfcf-f910-478e-8530-90e5565145ae", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:30:35"}, {"kompetensi_dasar_id": "733fe339-ce49-485e-809a-e20bfc13d98c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 803081000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan system kontrol elektropnumatik berbasis rele", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "73413c48-d8f2-416e-acf7-ddaa84d87939", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 830090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan sanggul ukel konde", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "73424df4-7e20-495b-b5b8-6b5ae9f486ee", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 817050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Peralatan operasi wireline di permukaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2019-11-27 00:27:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "73425f0f-2ab3-49c9-9232-a4326d4f9449", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:40", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:40"}, {"kompetensi_dasar_id": "7344507c-2a9a-4bc0-99e5-5b583778da3f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 817010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, metode dan peralatan geologi lapangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "73449492-3511-427b-8281-f01862334094", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 875150020, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami jenis lubricants and fuels", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2019-11-27 00:29:53", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "73464a79-f3ae-422d-a296-8f8dfcfa73ef", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "7346b0df-6ff4-447f-9314-bdd04bfaa27f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknik pemesinan  bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan  teknik pemesinan  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "7347892e-b17d-4b89-bc88-c72a2461f45c", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 802032400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melak<PERSON><PERSON> rendering dan menyi<PERSON>an video hasil editing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:38", "updated_at": "2019-11-27 00:28:38", "deleted_at": null, "last_sync": "2019-11-27 00:28:38"}, {"kompetensi_dasar_id": "73478e1b-c1aa-4740-86b7-a18a24659b90", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:38", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:38"}, {"kompetensi_dasar_id": "7347b2b9-742d-485e-be01-936af6483e91", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 832030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> langkah-langkah proses/prosedur meluk<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2022-11-10 19:57:42", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "7348d2aa-07c8-4ca5-ad81-05533b83ebf9", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 801031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pen<PERSON><PERSON> pelayanan lanjut usia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "734abd22-cfde-4db9-bf73-691ef48ac1c2", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 827200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan treatment pengelolaan kualitas air pada pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "734b7fff-5263-4dd8-8c2a-8cf13043c885", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 843040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik mix media pada seni lukis ekspresi objek langsung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "734ba31d-0732-464f-80e2-d29c00271197", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisa hubungan prosedur pen<PERSON> sampel dengan karakter sampel", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:03", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:03"}, {"kompetensi_dasar_id": "734baac5-049b-43bb-9832-540fe8c0955b", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 820140200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil perbaikan kelistrikan kendaraan ringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "734c56fc-0e5b-4c2e-8dc9-808419456cca", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807020600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan  perlengkapan listrik pesawat udara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "734f6594-b3e9-486e-a171-9e7eacdcb46f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses fisika dan proses kimia pada industri   makanan, lemak dan minyak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:59"}, {"kompetensi_dasar_id": "734f774b-7c9b-4a4e-adcc-c0a686fdae71", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 800081300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pembuatan media padat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "735111ab-fc9f-4ae7-85a2-05a518a17d3f", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:08"}, {"kompetensi_dasar_id": "73516796-47a8-4cd5-a08d-61bc6c23aee1", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprogram sistem mikrokontroler untuk aplikasi sistem kontrol", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "73522944-b5fa-41cc-b340-6bc8566c55ac", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung sesuai hukum termodinamika dalam suatu proses.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "73526f83-0a27-4c3f-a280-8181712a3c4e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:24"}, {"kompetensi_dasar_id": "735296cd-a6f1-455c-9a28-adb6e8f4584e", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401251103, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pengertian dan konsep syirkah mudharabah muqayyadah off balance sheet", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2019-06-15 15:07:16", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "7352eca9-4e56-4471-b133-c353ef212565", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 804060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkanteknik pemantauan perbaikan jembatan sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "7355602e-0e81-4a9b-b382-4fbaf4627519", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati karya pengembangan tenun  dengan berbagai keteknikan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2022-11-10 19:57:42", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "73567ba3-d870-4eaf-b4dd-9d65c7093bd2", "id_kompetensi": "4.47", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan monitoring te<PERSON><PERSON><PERSON> komponen mesin, sa<PERSON><PERSON><PERSON> ka<PERSON>, <PERSON><PERSON><PERSON> <PERSON>/O kendali elektronik dan pasangan mekanik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "7356e514-5eb9-447a-bb4e-19256d23d59a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan teknik perencanaan pembibitan tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "7357b717-ac16-42de-9a1a-b0acd669bcfe", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "73598ba2-a53d-4cda-9375-fe15c03a5613", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 842040300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat produk kriya kayu dengan konstruksi tingkat lanjut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "735a4f1e-8bf1-4913-ae01-d54f5fc65d28", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan bahan baku dan bahan penunjang dalam suatu industri kimia berdasarkan azaz sto<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:24"}, {"kompetensi_dasar_id": "735de827-9215-456d-adfe-dbd33e310e02", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks berita sederhana dari koran/radio/TV.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:33:43"}, {"kompetensi_dasar_id": "735ec130-537a-4829-9594-c3a986f1bc86", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 809010500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> harga bahan baku dan pendukung sesuai yang berlaku di pasaran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2019-11-27 00:27:35", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "735ef637-d779-4730-b05b-ad99b512c85b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:10"}, {"kompetensi_dasar_id": "736240e8-3382-4c73-9f6e-30183eeb2f4d", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji terjadinya raster gambar pada system televisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2019-11-27 00:29:11", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "73628cc4-7bc0-49a5-833f-d0a8a407eb60", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:38"}, {"kompetensi_dasar_id": "7363c16e-df51-4860-ba9a-48ae1b4e5e11", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843061110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasi perangkat instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "7363dce5-4062-4d62-98e1-38d9b44ce23d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 843070110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan motif lagu vokal ritmis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "73669f9a-5f51-4118-a14c-0453e64b7bd8", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pencatatan transaksi penerimaan kas dari pelunasan piutang dagang, pen<PERSON><PERSON> tunai, dan penerimaan lainnya ke dalam buku jurnal khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "7368fe09-d4c3-4597-b558-4946e2de2444", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 824050900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> pen<PERSON>han kepada kru, pemain dan pengisi acara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "73695c5d-ac18-4fb4-bf02-755115fd4142", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401251104, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentif<PERSON><PERSON> jenis-jenis zakat yang berlaku sesuai syariah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "73699901-052c-4e94-ba4c-a37a99567cf5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan jenis Integrated circuit (IC)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "7369dbb5-3623-4d1b-bee2-55420645eeff", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843062200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan eksplorasi dalam berkarya seni karawitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "736a0686-c669-418d-af69-015d37f11231", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 829080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "        Membuat makanan pembuka (hot and cold appetizer)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "736ac500-1f40-47cf-9195-9b6995081f5e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pengoperasian centrifuge", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "736bb68a-35fd-4f55-8df0-d9b7efcccfac", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kinerja Differential System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "736bfaa1-0304-476e-ae91-2dc77795a3fb", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar proyeksi sesuai dengan standar gambar teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "736cb584-8c23-4f75-a3bc-07fb9af4af9b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 807020810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan komponen resistor, induktor dan kapasitor sesuai dengan karakteristiknya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:38", "updated_at": "2019-11-27 00:29:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:38"}, {"kompetensi_dasar_id": "736d0f91-1907-4dfb-8282-ee7970bb4e09", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 816010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> anyaman keper pecah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:50", "updated_at": "2019-11-27 00:28:50", "deleted_at": null, "last_sync": "2019-11-27 00:28:50"}, {"kompetensi_dasar_id": "736d84b1-1080-44d9-8295-2d2967804da2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 801031500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengumpulan data", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "73702fe2-960d-4a18-8384-4b566e639e7b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821170400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami dokumen Welding Procedure Spesifications (WPS) las SAW", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "73706b8f-341e-4580-8f60-174d5cdc2fa0", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komponen dan tata letak sistem kendali instalasi penerangan (Smart Building)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "7370a076-b592-49f6-a5a4-112ab4ff3bef", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis prosedur perawatan dan perbaikan konstruksi bangunan gedung yang tergolong renovasi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:56", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:33:56"}, {"kompetensi_dasar_id": "73710024-c999-4bca-881c-6dab5c87247d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:20", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:20"}, {"kompetensi_dasar_id": "737191a3-c9dd-49c5-9827-a8ab56d29731", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401131600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan  penyiapan sampel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "73719c68-54e8-4c72-b744-215b187a6efa", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825270100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksana<PERSON> pengujian mutu produk olahan dari daging.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "73724fa1-b3cf-4635-8c7b-6f2736e54239", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 814100100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses crimping, cutting dan balling", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "73727990-8da9-4470-9547-919817e36447", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 821190300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kesalahan instalasi penerangan dan tenaga di industri dan kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "7372a626-efaf-440c-9e70-b0288089215b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:25", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:25"}, {"kompetensi_dasar_id": "73737814-da9a-444e-81ae-f8238cbe4c0a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803070800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar arsitektur (rancang bangun) mikroprosesor .", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "73737b62-a818-4359-9989-5d3ae10967c4", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan jenis-jenis gangguan mekanik sistem tata udara komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "7373a5e8-4934-45c0-b5dc-820e4b484fac", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Men<PERSON><PERSON>jjkan teknik pembibitkan tanaman dalam produksi benih tanaman pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "7374a5a1-1369-43f0-959e-afc79afeb175", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan badan denga nsistem body scrub/peeling", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "7374fdf3-83d9-44ca-91e9-493ac16c56b0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 800090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON> hierarki perundang-und<PERSON>n kesehatan dan k<PERSON><PERSON>, serta bagan organisasi institusi kesehatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "73754c29-d870-4d86-ba3b-27a0b72fc4b4", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda silinder (cylindrical grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:07", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:01:07"}, {"kompetensi_dasar_id": "73755fda-760b-491a-bdb9-4d82c22cdbdb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 875150020, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan perawatan Carburetors", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "737589b7-b294-4812-b1f6-c61fc98a7e78", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:00", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:33:00"}, {"kompetensi_dasar_id": "7376c0d6-62ee-41b9-8aa2-db41de5a8dce", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 843060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dasar komposisi tari berkelompok", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "7377412c-9e06-4f4d-80ac-228319ec9898", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825050300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik penyiapan sarana Laboratorium Pengujian Mutu Benih", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:06", "updated_at": "2022-10-19 23:19:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:06"}, {"kompetensi_dasar_id": "7377e7ca-3be6-49e3-9b74-519d05133f34", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 809020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses cetak satu warna/dua warna sesuai pesanan cetak dengan teknik cetak sablon/saring", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "7377fb43-4488-4dc6-9d1d-b96f99c6aa6c", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 820030400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Sistem Bahan Bakar dengan system common rail", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:16"}, {"kompetensi_dasar_id": "737a3a82-d3b6-4d79-8dc7-c46047150327", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Si<PERSON> sebagai pelindung dalam kehidupan sehari-hari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "737ad0af-4f8e-4c69-8b67-6ac32fb215b3", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami berbagai peraturan perundangan serta konsekuensi hukum bagi para pengguna dan pengedar NARKOBA dan psikotropika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:46", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:46"}, {"kompetensi_dasar_id": "737b8798-5e1e-431c-ad4c-729801ecbced", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:35", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:35"}, {"kompetensi_dasar_id": "737c2ef6-4837-471d-967f-87dbe2b0ee81", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar konstruksi bangunan air, bangunan pertanian dan komponen alat mesin pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:31", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:08:31"}, {"kompetensi_dasar_id": "737ceb62-cbc1-4f0b-8d38-74e884f79f28", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 802040700, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah efek pesan dan efek media", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "737d1f24-f55b-4f69-b5f4-91a397d26f98", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:10"}, {"kompetensi_dasar_id": "737f1870-9599-4cd5-a252-6f2e82fd7c65", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817040110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan metode dan peralatan penanggulangan limbah produksi minyak bumi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "737fe8df-f585-4395-abaf-47c486b2ce4b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar proyeksi orthogonal dan isometrik dari objek gambar atau benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:04", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:02:04"}, {"kompetensi_dasar_id": "737ffe39-0b1d-4f40-803f-6a4bf5b586e7", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 804100810, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jumlah bahan, tata letak dan biaya pada instalasi Perlengkapan Hubung Bagi (PHB) Penerangan 3fasa untuk lapangan olah raga sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "7380419f-58b4-4ef6-bd6c-b92bc4377f70", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis gambar 3D kompleks", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:33:58"}, {"kompetensi_dasar_id": "73805452-67ac-44d8-894b-c968e3beace7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825020600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik persiapan lahan  tanaman perkebunan semusim", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "73808834-80a7-4a09-9ce0-4510b372be14", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820120200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  penyusaian warna/ color matching", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "7380ec06-ecae-4e9f-9e3f-3bffb0572e60", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:59"}, {"kompetensi_dasar_id": "7383ef68-6a27-4aa3-b9d8-e2ef8c38bc20", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827310100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply SOLAS, subdivision and stability, machinery and electrical installation", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "738613bf-b5a5-4dde-aa66-5a4545574229", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827350600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain  keeping a safe navigational watch", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "7386b204-4438-49c6-a645-712f5cfae76f", "id_kompetensi": "4.31", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang sistem kontrol dan proteksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "7386dff4-eea4-4c80-9d2e-4b22047d591f", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 840020130, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat resep glasir keramik bakaran menengah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "738836ed-1ae0-4533-9877-422edd7d053a", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik  pengendalian gulma tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "73892ce5-36b9-4d16-8034-8d7d213af155", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 811010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami fungsi lapisan struktur film reproduksi dan pencampuran bahan-bahan kimia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "73894f71-563f-4ca6-91b6-f32e50e9c324", "id_kompetensi": "4.81", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>ji sebuah tranformator untuk menentukan nilai arus dan memberikan tanda polaritas arah arus transformator.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "7389b430-7ec3-40c8-8c35-a1b177e1cb91", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 805010500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa kebutuhan data spasial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "7389d933-6f82-45c3-b7bc-0218122c6f66", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:06", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:06"}, {"kompetensi_dasar_id": "738a0681-1355-4bf9-92ae-c43fa02f7fed", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik analisis secara potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "738a38a7-f15d-444c-9790-12438df21d8f", "id_kompetensi": "3.3", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur tit<PERSON>i <PERSON> (argentometri)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "738bcd51-5cd8-4227-9e80-961782b8e19a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401251310, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, karakteristik dan bentuk penjualan langsung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "738efc19-7c44-42ab-b37b-ec473d4ac104", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "73902795-1981-4d43-8523-df90279f5754", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistim kestim<PERSON> phase, sistim k<PERSON>, dan si<PERSON>t kimia fisika bahan dalam proses industri kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:08"}, {"kompetensi_dasar_id": "7393758c-2f9c-42c6-bcfb-ef75d72861bf", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804101100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pekerjaan elektromekanik dari bahan non logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "7393dd07-615a-40e4-96b1-626bf3ec5462", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menya<PERSON><PERSON>g Air\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:30", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:30"}, {"kompetensi_dasar_id": "73959276-d215-4af1-9520-394255a01fc7", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804100500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi perbaikan gangguan operasi jaringan transmisi tenaga listrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2019-06-15 15:12:17", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "73967efc-7e53-49a0-b10d-45278689fd20", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:11"}, {"kompetensi_dasar_id": "7396bccc-2128-4b65-89fd-c31f054f2d95", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 821090710, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami macam-macam material pipa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "7396f541-24da-4002-b0e3-3e66e609d359", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan tata cara pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:10", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:10"}, {"kompetensi_dasar_id": "739b4c7f-d737-4fd9-a092-b02cbda1874a", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks prosedur lisan dan tulis berbentuk resep", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:44", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:44"}, {"kompetensi_dasar_id": "739bbd3d-c8c0-4334-8e05-6e0e7bbb1ace", "id_kompetensi": "2.4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan  partitur lagu karya sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "739d07e9-2f09-4879-9792-cbc07f1823ce", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 828150100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan hubungan kerja antar bagian di hotel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "739d52d2-e4c0-4ea3-b9ed-1e3a15b13581", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825270300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> rencana pengendalian mutu hasil pertanian dan perikanan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "739d720a-8a45-4f2f-8425-cd41df2603f7", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:50", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:50"}, {"kompetensi_dasar_id": "739da77c-c263-4808-95de-a28159d91361", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola kecil dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "739ddd84-7e42-4d6f-b7a7-2fccc5efdb00", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 820030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menelaah persiapan pengoperasian turbin gas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "739e03dc-7c61-4b2d-a45b-1ed4344db65c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825280300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan penggunaan media penghantar panas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "739ec3ab-f5e3-442b-a2ca-01fd56234695", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:54"}, {"kompetensi_dasar_id": "739f4597-d7a5-403c-ae47-c95ff3892df0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pengoperasian hemocytometer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "739fe117-77f6-408f-b534-98a3888f1137", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825060800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pengetahuan tentang pemasaran hasil pembibitan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:07", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:07"}, {"kompetensi_dasar_id": "73a0aa2a-8750-4981-be96-e5d1bc0b42d9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:01"}, {"kompetensi_dasar_id": "73a179e4-164b-410e-bb72-f0e68311e599", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 807022210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>n su<PERSON>, <PERSON><PERSON><PERSON> dan volume pada pesawat udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "73a2e869-289a-4f5d-97f1-adbefc7346b7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843060810, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses produksi karya tari <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2019-06-15 15:07:31", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "73a30d02-9621-4034-b2bc-68173110b46d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 842020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengawetkan kayu dengan bahan alami dan kimiawi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "73a34bd3-61e3-45bc-906a-aaa6f3bcbcc0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:53", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:53"}, {"kompetensi_dasar_id": "73a4a3fd-45cc-4d11-a9e6-09ec5576c5c3", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843120500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memverifikasi jeni<PERSON>ung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "73a5d5b7-197d-4280-9bc0-ac618daf3503", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merangkai dan menja<PERSON>an silinder dengan rang<PERSON>an pengunci", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "73a65426-83e5-4d03-9a1f-f1df1e781239", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukanperhitunganberbagai proses berda<PERSON><PERSON><PERSON><PERSON>ter<PERSON>dinami<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:45"}, {"kompetensi_dasar_id": "73a744fc-dcd3-4c4b-8d5a-f356998eecce", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804050470, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat RAB Bangunan gedung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "73a75331-c72c-4e45-901b-74f6fac8af90", "id_kompetensi": "3.8.", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.8. <PERSON><PERSON><PERSON><PERSON> proses per<PERSON><PERSON><PERSON> rang<PERSON>an kombinasi pada sistem digital", "kompetensi_dasar_alias": "", "user_id": "3e4e480a-44cb-4765-aaef-4cb48f15a612", "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:31", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:31"}, {"kompetensi_dasar_id": "73a76b0e-fa07-4732-af6a-2737677264e5", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "73a8c249-8a17-4fd3-b82e-e9b606601153", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825250300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih filter yang sesuai untuk Dekorasi Akuarium/Aquascape/ Paludarium", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "73aa4576-3158-4944-8064-4520076f5d09", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 817090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahamitentang tekanan formasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "73abb3ec-0261-4344-91d3-e4e88de99d3c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengetaman permukaan kayu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "73adaec4-85c5-4f5d-b07e-ef7613667566", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:40", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:40"}, {"kompetensi_dasar_id": "73ae2223-32df-482f-8bce-23f7dff76a2c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804051200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan konsep furnitur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "73af687e-a818-4da6-a025-67bd30afd725", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 827350800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan perawatan alat kebakaran di atas Kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "73b03269-2075-4dda-8852-f0d22ff81e8f", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 804050480, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur keselamatan dan kesehatan kerja serta lingkungan hidup K3LH", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2019-11-27 00:27:39", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "73b03b9b-6670-44f3-b061-4dd72b46e116", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803060300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan penerima TV High Definition Television", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "73b1c993-f294-4fdc-9682-da7af060c647", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pemesinan frais kompleks", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:34", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:34"}, {"kompetensi_dasar_id": "73b25c71-0b7d-4c05-bbf9-9c9d934e311f", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827110110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> tipe, <PERSON><PERSON><PERSON><PERSON><PERSON>, prin<PERSON><PERSON> kerja dan fungsi mesin kemudi di kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:06", "updated_at": "2019-11-27 00:29:06", "deleted_at": null, "last_sync": "2019-11-27 00:29:06"}, {"kompetensi_dasar_id": "73b283e9-379f-4aa1-9806-3a64e94ec2d4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401141200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis simplisia getah, damar dan malam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "73b2be2b-1105-4a31-8db4-357410e22007", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur pengujian k<PERSON>aian fungsi produk barang/jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:05:36"}, {"kompetensi_dasar_id": "73b41be9-da2c-4f24-9bc6-c21806263f11", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 825050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penyiapan alat dan bahan pengambilan contoh dan pengujian mutu benih tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:47", "updated_at": "2019-11-27 00:27:47", "deleted_at": null, "last_sync": "2019-11-27 00:27:47"}, {"kompetensi_dasar_id": "73b58abe-cec0-4b0b-92fa-60008e4738f4", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan print to tape dan burning", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "73b77dfd-b1a8-4932-809f-5a32993b02a0", "id_kompetensi": "2.4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan membaca partitur lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:52", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:58:52"}, {"kompetensi_dasar_id": "73b7f23e-a8cd-4576-9c95-022b8f02c1f2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:05"}, {"kompetensi_dasar_id": "73b926b3-a37e-44da-94e4-5f6aabd495b9", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:03"}, {"kompetensi_dasar_id": "73b92fb2-67ee-4e64-b84f-e566009ac741", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 803071200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa proses kerja blok RF Power amplifier  digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "73b9331f-4bab-4698-ba27-f992c0015e81", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "73b982ad-5be1-4fff-b3d7-9e16373aad82", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 817120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami spesif<PERSON><PERSON> drainase", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "73b986a2-1092-4375-97d5-5b97f66507cc", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401251230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  pemasaran On-line dengan menggunakan /memanfaatkan media sosial.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:17", "updated_at": "2019-06-15 15:07:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "73b99e29-dd38-49de-9879-a80363c78479", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 829030300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Me<PERSON>ami perabot di restoran.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-11-27 00:30:31", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "73bb4352-71c1-49cb-910a-e06084b7dc52", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:17", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:07:17"}, {"kompetensi_dasar_id": "73bbbd6e-0830-4e03-93fd-73f238240c52", "id_kompetensi": "3.49", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan desain bagian-bagian sistem aplikasi PLTS.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "73bc105b-2248-4e31-b039-1a61fbbc4449", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 843120500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat set gantung dan piranti tangan berdasarkan teks lakon", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "73bc672c-f5fd-4ece-9d5f-d2b23f3297b6", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan objek k<PERSON>, menganalisis informasi terkait sifat-sifat objek dan menerapkan aturan transformasi geometri (refleksi, translasi, dilatasi, dan rotasi) dalam memecahkan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:57"}, {"kompetensi_dasar_id": "73be3d61-8883-4231-b8c9-41e1a3ea7df7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 826160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penyebab dan pengaruh cacat kayu bulat jati", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:34", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:34"}, {"kompetensi_dasar_id": "73bf8c95-28d0-4db6-afdb-416f746487af", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dan menggunakannya untuk menganalisis grafik fungsi dan menguji sifat-sifat yang dimiliki untuk mengetahui fungsi naik dan fungsi turun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:46", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:46"}, {"kompetensi_dasar_id": "73bfb16c-25c6-48ff-99f8-b330f39dc43f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 840010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan peralatan untuk mengolah clay body massa tuang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "73c0b45b-4e05-45ea-ae89-84234fc540e1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:33"}, {"kompetensi_dasar_id": "73c2a495-d000-455a-99cc-d2a6b8541ab9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 817020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "me<PERSON>ami Konsep Aliran Fluida dalam <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "73c3406d-aaf2-4470-8297-75e712e5334b", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 843110200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami dimensi karakter peran", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "73c36bc3-e3e4-4805-b3df-124cfa4cbe6a", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> peranan Agama Buddha dalam ilmu pengetahuan dan teknologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "73c36fee-cc6b-4dd0-a80e-3eb21f4a5160", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 802040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> tata letak (lay out), <PERSON><PERSON><PERSON><PERSON> huruf dan ilust<PERSON>i majalah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "73c3cbcb-32c5-4700-b67e-5a59e2a976f0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 831070700, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON> pola busana anak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "73c590b9-d7cc-4ed1-b9d9-d6309ec2265f", "id_kompetensi": "4.73", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pen<PERSON> (pengukuran) untuk mendefinisikan gaya gerak listrik (ggl) E akibat pengaruh nilai resistansi internal (r) dari sel baterei.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "73c5c250-c6f8-4aca-bda3-04ec68c3c21f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:21", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:21"}, {"kompetensi_dasar_id": "73c7ee55-304b-4eaa-a167-d1944a887a4d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 818010200, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan konsep <PERSON>a bahan galian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:31", "updated_at": "2019-11-27 00:27:31", "deleted_at": null, "last_sync": "2019-11-27 00:27:31"}, {"kompetensi_dasar_id": "73c80942-f006-41fa-89b5-f4c63aa8b70a", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 801030700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan bimbingan konseling pada lanjut usia penderita penyakit kardiovasculer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "73c9673e-9309-434f-8384-805386e0aa85", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:25", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:25"}, {"kompetensi_dasar_id": "73c9e299-4c11-4ea7-a524-d0e782aebac4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "73cb3292-116d-4ab6-be4c-34afd8ee76e5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  elemen utama eksterior berdasarkan konsep dan gaya eksterior yang ditentukan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "73cb8297-2edf-4484-8562-abf4b9295dd9", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 820090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki Steering System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "73cc662f-c7fb-475c-bec0-baa594c2ac25", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:24"}, {"kompetensi_dasar_id": "73cd7d75-10e6-463c-a7f3-4fbf232ed572", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825280300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan teknik pengendalian bahan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "73ce44ed-0c2a-4d3c-8ea8-3395bb3cc5a5", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804050700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON>  prosedur finishing kayu dengan  bahan cat enamel dalam  berbagai jenis dan warna de<PERSON>asi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "73cf2141-a8ea-49d2-8bce-2698343cec77", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:49", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:49"}, {"kompetensi_dasar_id": "73cf3200-fdeb-43ad-a15c-5b0791aab898", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 804060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pela<PERSON>an peker<PERSON>an per<PERSON>asan kaku(jalan beton)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "73cf73cd-c135-4f38-a05d-312819b7fb9f", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 401141100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi obat yang berhubungan dengan penyakit pada sistem pernafasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "73d06ac7-ea4f-4636-bdab-537b4de5b36d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis geometri gambar teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "73d1ba33-774e-442b-85d5-f2bb68f20433", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:50"}, {"kompetensi_dasar_id": "73d22909-803b-4dea-89d1-5abca80458e5", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 821120300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> per<PERSON>aan komponen mesin di atas dek", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "73d28dc5-2185-4f3c-a3b1-37f9d4cc3ecf", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:18"}, {"kompetensi_dasar_id": "73d3039e-1bd8-4b06-9d6b-2f6ccc3d366e", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON> rencana kegiatan industri pakan ternak unggas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "73d34257-0fcb-4597-ae13-9440ae8d8e89", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 814110200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami prinsip penomoran benang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "73d3e0e8-a8e0-4ca3-814c-a4a4c8ddcaa0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807020900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan konsep/deskripsi material composite sesuai dengan SOP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "73d44874-e7ff-419e-ad21-2709c7f71a32", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 827210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik dan tipe filleting, tipe dan bentuk potongan daging ikan (<PERSON><PERSON>, Saku, Steak dll)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:35", "updated_at": "2019-11-27 00:29:35", "deleted_at": null, "last_sync": "2019-11-27 00:29:35"}, {"kompetensi_dasar_id": "73d4ddaf-7b34-40bd-85c5-84348d0e094b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825060900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan tentang pemanenan ternak unggas pedaging.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:07", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:07"}, {"kompetensi_dasar_id": "73d59338-95bd-43b3-9a71-7a7eb4a8d333", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> kalibrasi positioner", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "73d62c9d-3c09-4abb-9587-e828a0334c8d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:05"}, {"kompetensi_dasar_id": "73d63a77-f67b-463c-8c3a-b739fee68ec2", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 829101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksana<PERSON> proses produksi dalam jumlah banyak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "73d693dd-da62-403b-af79-5c05e53712f0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi <PERSON>i rele pengaman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "73d7e44c-9a30-4655-8c17-8c848b812b0e", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, dan merancang koreografi aktivitas gerak ritmik, serta mengevaluasi kualitas gerakan (execution).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:08", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:08"}, {"kompetensi_dasar_id": "73d88608-9425-4587-bf52-51bd6147c44f", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 804050470, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat dokumen perjan<PERSON>an jual beli pada bisnis konstruksi dan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "73d8da5b-4aae-41ef-a044-e0df1678198c", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 830030100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Membuat laporan hasil evaluasi berat badan ideal untuk kesehatan dan kecantikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "73d94fcf-e81d-400a-9ba1-2d8759069265", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 826050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dokumen lingkungan (kegiatan wajib dokumen lingkungan, pengelolaan, pemantauan)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "73da914a-adcf-4993-8ce2-ee8ba2f48ea6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802031000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami klasifikasi huruf font pada tipografi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "73db4aec-3f0f-48e0-8c8e-2484aa5d7b5d", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825062400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menyajikan fisiologi reproduksi hewan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "73dd2859-2192-45e3-a14e-a70dd5bf9183", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 824050700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan prosedur <PERSON> proses produksi program televisi drama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "73dd9f61-f155-44d7-b924-398dda827f47", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:28"}, {"kompetensi_dasar_id": "73ddafe8-14bd-4d38-98d2-c3b74ea75bf4", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pengaliran bahan bakar bensin kon<PERSON>sional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "73de8798-8317-46af-a3c4-42dc65ed9907", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:54", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:54"}, {"kompetensi_dasar_id": "73dee596-1b16-4baa-a88d-f1597bd81619", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON>", "kompetensi_id": 3, "mata_pelajaran_id": 700109000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Peserta didik mampu menjalani kebiasaan baik dan rutin\ndalam berpraktik musik dan aktif dalam kegiatan-kegiatan bermusik lewat\nbernyanyi, memainkan media bunyi-musik dan memperluas wilayah praktik\nmusiknya dengan praktik-praktik lain di luar musik serta terus\nmengusahakan mendapatkan pengalaman dan kesan baik dan berharga\nbagi perbaikan dan kemajuan diri sendiri secara utuh dan berdampak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:47", "updated_at": "2022-11-10 19:57:02", "deleted_at": null, "last_sync": "2022-11-10 19:57:02"}, {"kompetensi_dasar_id": "73df1d38-8c67-43bd-8421-eaca2aaa6cc6", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal dan menya<PERSON>kan<PERSON> dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:13", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:13"}, {"kompetensi_dasar_id": "73df5755-c554-4f9c-aff6-7ad88bb28f86", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 822180100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang model instalasi kelisitrikan rumah PLTMH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "73dfa60f-4a47-43d3-bf28-0d8c262a204e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 830050200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan persyaratan dan kontrak kerja karyawan/SDM salon SPA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "73e18963-6a9f-43c0-99b0-a2a7514ca71b", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 401251104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan identifika<PERSON> akad transaksi dalam fiqh muamalah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "73e1a64b-2591-4b54-800c-d5c16232ab70", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan capture dan copy ke dalam computer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "73e1b7dc-270f-4cd2-b14c-ccdaa9416ea8", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pemesinan bubut  kompleks", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:59"}, {"kompetensi_dasar_id": "73e296a1-51e5-4a7d-9070-708d47353cbd", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis prosedur perawatan dan perbaikan konstruksi bangunan gedung yang tergolong rehabilitasi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "73e30a3d-a132-414d-bd7f-49080be48f9f", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:26"}, {"kompetensi_dasar_id": "73e3a711-831e-43f4-bc96-da896f24e966", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>  urutan dan paktor pengulangan yang teratur dari bentuk, teknik dan isi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "73e4f078-320a-4c69-962e-4c0dbdc60071", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251021, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> entri jurnal ke buku besar (posting) pada per<PERSON>an jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2019-06-15 15:07:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "73e5ac91-f113-4c0a-adfa-36d079cec483", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan bentukan topografis lahan, fungsi & aktifitas potensial untuk diakomodasikan di atasnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "73e676f1-1162-4d8a-8ebf-27bfeb800453", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan pen<PERSON>apan bahan tanam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "73e7b302-952b-49ce-8a79-248809262a69", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kondisi operasi dan aplikasi sensor flow", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "73e8106e-5dc4-4259-9afc-7c37025920d6", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401251130, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Letter of Credit (L/C)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "73e82b49-80eb-42a8-b8b3-3a3db9372110", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:06"}, {"kompetensi_dasar_id": "73e9110a-c577-4a4b-b301-15f3776b5083", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827210400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, sele<PERSON>i dan penetasan telur komoditas perikanan hasil pem<PERSON>han alami", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "73e9754d-b946-4a76-9d37-ed6072728d4a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 817150110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode keselamatan kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "73ea236d-b4c0-4987-941f-eee2a2ebb26a", "id_kompetensi": "3.31", "kompetensi_id": 1, "mata_pelajaran_id": 803071400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa prosedur pen<PERSON>han antena parabola", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "73ea8f4e-e663-4304-b2e4-11a31726bf05", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 801031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara kesehatan lanjut usia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-11-27 00:30:26", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "73eabe4e-5efb-47cd-a622-a8334ebfe2f6", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 804040200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemasangan langit-langit, ornament dan profilnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2019-11-27 00:28:15", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "73ebc643-55dd-4ae3-bada-092a6ff05e98", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 843062100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan garap pengembangan bentuk gending/lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "73ebfe85-9127-4bde-88ba-486e8074e0ee", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 820030400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja <PERSON>sin Diesel PLTD Besar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "73ed86ef-f4ca-4bc1-b848-3dfcafaf1fc0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan Ke<PERSON>ama<PERSON>\r\ndan <PERSON>ja dan\r\n<PERSON>du<PERSON> dalam\r\npelaksanaan peker<PERSON><PERSON> Gedung\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "73ee08f6-df77-4998-97ba-48fa95b1675b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> jenis-jenis se<PERSON>bon", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "73eef2f7-c663-4a2a-a0c3-b320f4a365d5", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:58", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:26:58"}, {"kompetensi_dasar_id": "73ef3161-0a4e-4468-b29d-727b860c9506", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi kelistrikan PLTS tipe terpusat (komunal) off-grid", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "73ef4627-9502-48e8-9d7b-622a89c69964", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "73f0eeaa-a7d6-435b-aac3-13c90c827777", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sabun dan detergen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "73f13e2e-2abb-4e81-8138-0b5319c7364b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menghitung kemampuan sumur untuk memproduksi minyak bumi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "73f14554-c89a-420a-9385-beaf2854734d", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804040110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep time schedule dan kurva S", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "73f1a9c4-97ef-46ba-9be8-b3b4fb679a66", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804111100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Prosedur Perawatan peralatan Kelistrikan system mekatronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "73f2b54a-5c30-4119-bf21-99b184bae36d", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses grinding dan sizing Sieving.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:41"}, {"kompetensi_dasar_id": "73f45f95-c074-49f6-a155-90c7209af64f", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep-konsep reaksi kimia dalam  perhitungan stoiki<PERSON>tri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "73f4ffed-4c84-4091-99f7-d7eac23dfc99", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemotongan panas secara manual dan perlengkapan pembentuk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "73f54d24-022a-4817-9de3-e4dc25b74933", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821090400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> dan menerapkan penyiapan pengecatan lambung kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "73f5c5b1-e97c-4981-b980-12f2288ed866", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan  sistem pengapian konvensional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "73f79812-375c-4ad2-95ff-841800041269", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:04"}, {"kompetensi_dasar_id": "73f7b16e-03c9-4959-bd5f-3e8a18fe40b9", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 827060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik penentuan posisi dengan membaring benda darat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "73f7b29a-f3ae-458b-9c4d-7ea7f598b6f6", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perawatan dan perbaikan instalasi listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "73f9378a-27eb-4c2e-82b3-0ffd10c75dc5", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaut gambar 2D dan 3D CAD CAM dalam gambar produk/elemen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "73f974be-e26c-4338-83f1-3b320858a18e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:41", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:29:41"}, {"kompetensi_dasar_id": "73f9d011-2933-47c2-ba4e-442a444fffaf", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 802020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami cara mengkonfigurasi Proxy Server.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "73fa6fd5-daea-4499-9ec5-cafc5ffbb84a", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 825050200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis dokumentasi, pembuku<PERSON> dan pelaporan produk pupuk, dan pestisida organik, serta pupuk, dan pestisida hayati sesuai standar operasional prosedur (SOP) sertifikasi organic untuk produk pupuk, dan pestisida organik, serta pupuk dan pestisida hayati", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "73fcb363-c326-48d1-92f5-07b1628eafc0", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan  teknik pengoperasian alat sipat datar (leveling) dan alat sipat ruang (theodolit). ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:46", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:03:46"}, {"kompetensi_dasar_id": "73fd0571-cdd5-4580-8f78-ed22725ca256", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, mengan<PERSON>is dan menge<PERSON><PERSON><PERSON> pengetahuan faktual, konseptual, prosedural, dan metakognitif berdasarkan rasa ingin tahunya  tentang ilmu pengetahuan, teknologi, seni, budaya, dan humaniora dengan wawasan keman<PERSON>, keb<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, dan peradaban terkait penyebab fenomena dan kejadian, serta menerapkan pengetahuan prosedural pada bidang kajian yang spesifik sesuai dengan bakat dan minatnya untuk memecahkan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2022-11-10 19:57:42", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "73fd6152-5ee1-4964-8c38-d112badd1c85", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 821170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami dokumen Welding Procedure Spesifications (WPS) las GTAW/SAW", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:22", "updated_at": "2019-11-27 00:29:22", "deleted_at": null, "last_sync": "2019-11-27 00:29:22"}, {"kompetensi_dasar_id": "73fe5309-a7aa-46b3-a138-aedbeb962271", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401251310, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi <PERSON> langsung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "73fe6833-36ac-4a1d-8ce5-633ab5f33223", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan treaded, fastener, sealant dan adhesive", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:26"}, {"kompetensi_dasar_id": "73fed775-5472-4b4d-a52a-e299afd64df3", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "73ff735a-da2c-427e-87f2-a4cdc2b2b18a", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 821080300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami perawatan mesin-mesin perkayuan Stationer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "74008cb6-dc86-45ee-9525-18399fe04945", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 825063100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> penanganan limbah dalam usaha aneka ternak (hewan k<PERSON><PERSON>ngan)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:18", "updated_at": "2019-11-27 00:28:18", "deleted_at": null, "last_sync": "2019-11-27 00:28:18"}, {"kompetensi_dasar_id": "74010473-7290-4794-bac1-b56f182e9e3a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perencanaan laboraorium untuk kegiatan praktek, uji coba dan penelitian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "740120fa-af06-4907-a549-b210609b65df", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar penggu-naan kontrol suhu dengan sistem mekanik, elektrik serta  hubungannya dengan interface dalam pembuatan sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:43", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:43"}, {"kompetensi_dasar_id": "74019b93-54a1-4504-8da9-23d4433576ea", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:29"}, {"kompetensi_dasar_id": "7401dbe5-6cbf-42d7-8c92-9d915874cba3", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "7401dfb0-18f4-4847-b98d-3b59c50f961a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 805010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> survei dan pemetaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "7401f31a-92c2-41df-99f6-8779fa313066", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan K3LH pada peker<PERSON>n", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "74027074-8dde-445a-a078-feabdd74af0b", "id_kompetensi": "4.56", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan troubleshoot pada modul dan komponen robot mobile", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "740416bd-6ad2-41b9-a29e-898f756bee0b", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik instrumen spesialisasi pada genre musik progressive rock dan progressive metal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "7404a03c-ef91-4bba-81bf-99185cc96fd6", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data dari situasi nyata, memi<PERSON>h variabel dan mengkomunikasikannya dalam bentuk model matematika berupa persa<PERSON> fungsi, serta menerapkan konsep dan sifat turunan fungsi dalam memecahkan masalah maximum dan minimum.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:12"}, {"kompetensi_dasar_id": "7404f670-9f15-4c1e-968a-b2af954c1692", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843061110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik pengembangan pola tabuhan instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "74070929-0a25-4d1c-89f3-075ab78b6720", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843080200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi tatah sungging wayang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "74075d7c-963a-4ca7-b9fa-c85dc7971caa", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 806010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit instalasi pemipaan refrigerasi domestik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 15:12:20"}, {"kompetensi_dasar_id": "740941d7-41e5-4d35-adde-6301a9083d53", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 827390900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate routine pumping operations", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "7409a960-a6dd-4170-a5f9-309df77b3ad4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 829020400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapka platting dan garnish", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "740f3f54-c718-4a1a-aca7-e71c5bba9c92", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi komponen sistem rem alat berat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "740fb7e0-7b50-44bb-85f0-b1caac8f7c7d", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat supercharging /turbo-charging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "74112285-3142-4777-8e10-5b99a519e6e9", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan posting jurnal-jurnal ke dalam buku besar untuk perusa<PERSON>an dagang.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "74118f6d-7c7f-499d-b9f0-f8bccd5ab1f9", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 875190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis karakteristik transformator 1 fasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:39", "updated_at": "2019-11-27 00:29:39", "deleted_at": null, "last_sync": "2019-11-27 00:29:39"}, {"kompetensi_dasar_id": "7412a137-22c0-47a3-8edc-a2726dc7bab8", "id_kompetensi": "4.29", "kompetensi_id": 2, "mata_pelajaran_id": 825063100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil produksi pada usaha aneka ternak (he<PERSON> k<PERSON>ayanga<PERSON>)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:18", "updated_at": "2019-11-27 00:28:18", "deleted_at": null, "last_sync": "2019-11-27 00:28:18"}, {"kompetensi_dasar_id": "741417c7-dca5-4d46-bbfa-46436f52f615", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perawatan mikroskop.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "7415fb11-ddd4-4bf7-a5f8-5596c000444e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengoperasian per<PERSON> filt<PERSON>i.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:38"}, {"kompetensi_dasar_id": "74160ba7-5427-40f7-ade4-356d63d9fa70", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 801031100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> tindakan advokasi pada anak berhadapan dengan hukum (ABH)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "7419925f-9bbe-4110-bdde-55a992cb18c3", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 816040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur gambar/desain tunggal dan raport dalam proses pencapan kasa datar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "741a5127-d1c8-40a2-b72a-a7818e854245", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 813010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalis kerja kontroler pada sistem instrumentasi kontrol proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "741a63ed-f41a-4f7d-af71-233b5f398b02", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 826050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> pemeli<PERSON>an tanaman hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "741a8bd4-dcd7-44ea-8ab2-b273dd915214", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 803071500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemasangan konektor pada saluran transmisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "741a958d-751d-46c5-8b06-2c927e5ecd71", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803070800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat program aplikasi sederhana dengan mikrokontroller.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "741bd5c7-8225-4712-9c33-257436ec426d", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuatan benda kerja se<PERSON> (segi-empat, balok bertingkat) pada proses pengefraisan sesuai spesifikasi dan prosedur standar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "741bfe3f-a411-42ac-a2e5-b557c32a5162", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis standard ISO mengenai tata letak gambar dan layout kertas gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "741c3029-e405-4393-bbdd-4d7a00e18a6b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan panggilan hidupnya sebagai umat Allah (Gereja) dengan menentukan langkah yang tepat dalam  menjawab panggilan hidup tersebut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:33", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:33"}, {"kompetensi_dasar_id": "741c89b6-de7c-4d31-b78e-82020101420a", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprogram sistem mikrokontroler untuk aplikasi sistem kontrol", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "741e3423-c876-47b6-983c-6a2a1fdbe575", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi penangkal petir (Lighting rod)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2019-11-27 00:27:59", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "741ea15f-4f74-46df-b2d8-939e0b3491e2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keterampilan 4  gaya renang untuk memperbaiki keterampilan gerak, dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:31"}, {"kompetensi_dasar_id": "741ff787-f08c-4b10-bb4d-2faa54746f38", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807021710, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang kabel pada instalasi kelistrikan pesawat udara sesuai dengan jenis, parameter kelistrikan, parameter mekanis dan prosedur/cara penyambungan yang tepat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "741ff8ac-3d57-4404-9dbe-17dcb95e0385", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 803090400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar system control dari aplikasi mikrokontroler pada peralatan instrumentasi medik secara blok diagram", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "74202d43-73d2-4dc1-9a5a-317326134460", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804101200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemeliharaan rutin unit mesin listrik pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "7422cda2-1c8a-4754-9f21-bbc793016162", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 825060200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pengawetan hijauan pakan ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "7422e1e7-71c1-4875-a01a-391fd24fef69", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 843011742, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis full score ansambel, orkestra menggunakan aplikasi Sibelius", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:06", "updated_at": "2019-11-27 00:28:06", "deleted_at": null, "last_sync": "2019-11-27 00:28:06"}, {"kompetensi_dasar_id": "74242279-60e7-4dd1-b836-1c2875b1f2d5", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih dan menerapkan strategi menyelesaikan masalah dunia nyatadan matematika yang melibatkan turunan dan integral tak tentu dan memeriksa kebenaran langkah-langkahnya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:03"}, {"kompetensi_dasar_id": "742437aa-ce35-4481-b48c-f334f5f7468b", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 806011100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami gambar rangkaian kompor lisrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "7424d71e-ebfa-4678-969d-6714ea22242d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 823060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam desain reaktor pirolisis dan destilator asap cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:37", "updated_at": "2019-11-27 00:27:37", "deleted_at": null, "last_sync": "2019-11-27 00:27:37"}, {"kompetensi_dasar_id": "74250db7-71c6-4a08-af99-8ccb16ba4da1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "74262af2-1f6d-4cfe-94db-555a610ad58f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:43"}, {"kompetensi_dasar_id": "74268959-e3ef-41e6-b6fa-c5c9519d0b4d", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401141500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis alur pelayanan resep dokter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "74276354-de21-403a-9800-3d78e15bde50", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola  menyerang dan bertahan) salah satu permainan bola besar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:47", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:47"}, {"kompetensi_dasar_id": "7427864c-0184-4421-bad4-0752c03e9c78", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:56:58", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:56:58"}, {"kompetensi_dasar_id": "742a96c9-df72-4b6b-833a-64f6d9eed694", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisa luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:48", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:08:48"}, {"kompetensi_dasar_id": "742bf269-a022-454e-b185-84c216710ffa", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 807020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan assembly main landing gear and wheel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "742cca45-daa6-4b3b-8f92-bf6c48133c43", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan energi  dan bahan penunjang dalam suatu industri kimia berdasarkan azas kekekalan energi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:13"}, {"kompetensi_dasar_id": "742d18ba-baa0-4906-8f50-e3d67d8507c8", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan jurnal penyesuaian dan jurnal koreksi serta koreksi akun bagi bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2019-06-15 15:07:16", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "742d81a7-e6ad-498f-b2b8-ca9f8d7b1e12", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pola kayu sesuai dengan gambar, instruksi dan spesifikasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "742e0e69-4d31-4fba-b17d-e652723d2084", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknik pemesinan  bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan  teknik pemesinan  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:10", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:10"}, {"kompetensi_dasar_id": "742e4946-c6f9-42a0-be3c-5bb3dc29720a", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "742ef2c5-ba01-43c3-8e88-a8ff810fc8ae", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 801040300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan relasi dengan lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "742f2706-55ce-49b3-bc2d-cb8e9829d8ae", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 840050100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapakan cara  membuat grafik pembakaran biskuit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "743048a3-d8f3-4c6e-a5de-eea290988939", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803070900, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik perawatan dan perbaikan peralatan elektronik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "7430515a-d5aa-4838-ba2d-30c3649613cf", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "7430d438-0c65-4267-a108-f1d740c727cd", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan praktik desain grafis pin.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "743160b8-fad5-45e8-8e54-39f2e9718c98", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis k<PERSON>si proteksi gardu induk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "7432a562-1fb0-4432-9ff2-63aaeb537d94", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "7432a9a5-8e0f-4b6f-90f1-b1005c80532b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kons<PERSON> budaya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:02:43", "updated_at": "2019-06-15 15:02:44", "deleted_at": null, "last_sync": "2019-06-15 15:02:44"}, {"kompetensi_dasar_id": "7432b41d-e87c-4153-86c5-d38d5b304402", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pribadi <PERSON>us <PERSON> sebagai sahabat sejati, to<PERSON><PERSON> idola, dan <PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:13"}, {"kompetensi_dasar_id": "74335ff0-d54a-4ed8-8e7a-2ec483435a81", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyiapkan laboratorium untuk analisa rutin", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "7433eaf6-1390-452a-a1a6-4a0ebe40e0ad", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 829111100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat roti \"danish\".", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "743598ce-c3df-4b8f-8d52-dafa06eca054", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820140300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem bahan bakar bensin konvensional/karburator", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "7435dad1-9ac5-4407-8202-3c582965fe66", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843070300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi ornamentasi vocal ritmis dengan menggunakan iringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:16"}, {"kompetensi_dasar_id": "7435e0b8-7279-4d76-94e2-ff47477ac6f5", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem kelistrikan dan kelengkapan tambahan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2019-11-27 00:27:26", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "74370ace-cbf8-4116-8110-0f2316187770", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825062000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON><PERSON> hasil pemasaran produksi aneka ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:02", "updated_at": "2019-11-27 00:28:02", "deleted_at": null, "last_sync": "2019-11-27 00:28:02"}, {"kompetensi_dasar_id": "7437af8d-fce1-469e-931b-71857511db9a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.1 Menerapkan kaidah gambar proyeksi dalam membuat gambar proyeksi bangunan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:16", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:07:16"}, {"kompetensi_dasar_id": "743871f6-f0c5-4f3c-b055-99280a1e4ff2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:22"}, {"kompetensi_dasar_id": "743972dd-ffbe-49fd-8556-ef3043f3e5a2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:16"}, {"kompetensi_dasar_id": "743af366-98ce-4082-8c8a-4bcaed8b31ab", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:07", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:07"}, {"kompetensi_dasar_id": "743b6859-b39e-415f-9a28-fa879a95521a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401141510, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur evaluasi sediaan cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2019-06-15 15:07:03", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "743d3761-99d3-4604-9c0f-1c651cad96ab", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827140200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pertum<PERSON>han ikan berdasarkan sistim peredaran darahnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "743d4ce8-d217-4414-bd05-672202416697", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan konsep analisis dampak lingkungan hidup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "743d5206-a3c5-4b0e-adcf-1d1a678bc016", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip penggunaan material dan bahan kimia sesuai SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "743eaa32-6256-43c5-9123-b28b82f28914", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja rang<PERSON>an konversi D/A & A/D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "743f1f6a-2e90-44c8-91d0-6ee2767a8387", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengan<PERSON>is bahan pakan dan pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "74415bfd-1d94-4422-8968-ba50b926730b", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "74416381-cdb8-482f-af7f-5ff68e900e6a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:21"}, {"kompetensi_dasar_id": "744165c7-b4ec-4df8-8382-058d453c0338", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804131530, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bahan dan alat untuk pengelasan oxy acetelyne", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2019-11-27 00:29:05", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "7442553a-ff39-4967-a95f-bbc1b343722e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 826070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengolahan data hasil pengukuran digital dengan komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "7443349c-480a-42ff-9156-3270524e4ff1", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 805010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa syarat utama penggunaan alat ukur waterpass dan <PERSON> (kalibrasi)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "7444783c-788f-41ee-bce8-52595dfd88b4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisi pengukuran parameter  aliran fluida.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "7445566c-215b-4cff-b36f-6b50e8d04918", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 800060920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> manipulasi bahan praktik dokter gigi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "744583aa-9292-44b9-adda-91215fe27019", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:46"}, {"kompetensi_dasar_id": "74460b66-eb1c-4cfc-b242-b3b492b2fada", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan alat timbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:58"}, {"kompetensi_dasar_id": "74484dc8-2b48-49ff-a92c-f78eff908152", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 801040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melatih Activity of Dayling Living pada lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "7448de5c-a135-4780-b5c1-025479955bba", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 843070600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bentuk organologi instrumen gesek", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2019-06-15 14:58:18", "deleted_at": null, "last_sync": "2019-06-15 14:58:18"}, {"kompetensi_dasar_id": "74491bd5-2597-4db7-95e7-c49103e75c6b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:29"}, {"kompetensi_dasar_id": "744925a3-4d36-4a6a-8079-04629a4abeed", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:40"}, {"kompetensi_dasar_id": "7449cfa7-d065-4f84-925b-4a1b691f57e8", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>n nilai besaran vektor pada dimensi dua", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:00:15"}, {"kompetensi_dasar_id": "744bc664-8f95-4183-81db-f60d8e94fb89", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks berita sederhana dari koran/radio/TV.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:15"}, {"kompetensi_dasar_id": "744be692-ee7d-4e2b-b4f5-dc51b835452d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:06"}, {"kompetensi_dasar_id": "744d2c5c-739f-420a-b2b1-3875d456a82c", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 818010300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan teknik sampling batuan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "744dcc23-8c3b-46fa-a4aa-be3c3c089b74", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan default tray pada sketchup", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "744ece3d-8243-4252-8afc-b071feec0d37", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "744f31ce-6d32-4dc9-91d6-01f289823e57", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  mesin gerinda silinder/cylindrical grinding machine  untuk berbagai jeni<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:20", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:08:20"}, {"kompetensi_dasar_id": "745153d6-f8f4-48c5-8e20-1fd1472e1820", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:26"}, {"kompetensi_dasar_id": "7451cb96-9b58-41de-9031-bc7626870eea", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemesinan bubut  kompleks untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "74533b38-8eaa-45b5-a094-9d08b0ff2d81", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820120200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode masking bagian body yang tidak di perbaiki", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "7453bff8-8e92-4c63-a0dd-d9c69d3190ed", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827110340, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bahan bakar cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "74547b35-4b7d-4ed3-b7c5-454fcbf060fa", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 843070110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan repertoar lagu non ritmis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "7454d9d6-bee1-47b5-8f59-3abf0bbbae30", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran tokoh Nasional dan Da<PERSON>h yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:20", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:20"}, {"kompetensi_dasar_id": "74552960-a5bc-4dd3-958a-c185b1a01357", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pembuatan benda kerja    pada mesin bubut, dengan su<PERSON>/toleransi khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:34", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:30:34"}, {"kompetensi_dasar_id": "7455723e-89da-48a7-8922-2f5b2990b274", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 822050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> beberapa macam  robot yang diam ditempat dan robot yang berjalan serta konstruksinya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "74563892-a045-4a44-b99c-01f5271a4009", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:04"}, {"kompetensi_dasar_id": "74577ede-2113-4950-ad12-2a30628c7679", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "7457b4b1-49ef-49b8-88ac-9c072215307f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401251104, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pasar modal syariah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "745824e9-d3dc-428f-bbcc-df1a5f121ec3", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 843040400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkomunikasikan hasil evaluasi tata busana panggung dengan peran dalam pementasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "7458ce2e-b165-415d-b741-09c24bfd2ad0", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate actions on stranding/grounding", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "7459ff2c-a60d-41e1-9c1d-0bec70362009", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401132100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis k<PERSON>uk<PERSON>ri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "745ac5b8-bdca-46da-a4a1-4bb37d691c1b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 839120200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pola produk kulit mentah tatah sungging dua dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "745c0868-2f07-4996-95f7-004ed2480139", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843050100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> tangga nada", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "745c5784-659c-480a-bd3f-2f14c7ab5022", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 843050100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>   tangga nada mayor dan minor yang dapat diterapkan dalam pembelajaran praktik instrumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "745ce5d3-eab5-42b0-ba1c-98a8e68f6554", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "745d877a-9e71-4cdc-9b8f-cf7fdd261eda", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825020610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengendalian gulma tanaman perkebunan semusim penghasil minyak atsiri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "745de57c-c228-4615-9614-4fb0db0a3d98", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola sistem manajemen perbenihan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "745e9d9c-4a9b-44da-9218-6e333f3765bc", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803080910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan sistem kontrol open loop", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "745f45e5-db39-44ab-9330-221e20cbc1aa", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis, untuk menyatakan fakta dan pendapat, dengan memperhatikan fungsi sosial, struktur teks, dan unsu<PERSON> k<PERSON>, yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:28"}, {"kompetensi_dasar_id": "745f49d9-8886-4af4-aad9-6bf02a0d2a09", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 830130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bahan masker se<PERSON><PERSON> fun<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "74601a5b-ea26-4349-b26b-3975bc8392a0", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting surat lamaran kerja, dengan memper<PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:27"}, {"kompetensi_dasar_id": "74604559-e397-4468-9844-7a8075eca26b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "74604b35-905d-4ead-849e-33b498455605", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:12"}, {"kompetensi_dasar_id": "7460b690-a489-4f41-be9d-f02d666abc0c", "id_kompetensi": "3.30", "kompetensi_id": 1, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gejala kekurangan cairan pada lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "74637806-cf48-4601-a9bc-df9a1a74a9d1", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 822190500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasangan saluran kabel udara tegangan rendah (SKUTR)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "7463a235-18c9-4a8a-862f-d758de5ae440", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprak<PERSON><PERSON><PERSON> tindakan Yesus Kristus yang rela menderita, se<PERSON><PERSON><PERSON>, wa<PERSON><PERSON>, dan bangkit demi kebahagiaan manusia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "74648dbe-c816-4acc-a4ab-f9174460f0af", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825230100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan produk olahan kacang-kacangan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "746741b5-2111-4950-8fd3-caa2d1c428e8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis penyusunan formulasi pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "746774be-5080-42c2-bdf5-3b06b915fe77", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 825060900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON> pen<PERSON>nan hasil produksi aneka ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "7468992b-6a8c-44b1-9c57-cd308bfab31b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 805010500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep Sistem Informasi Geografis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "7468cc58-8b42-412f-9cbd-759240eda314", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 807020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Flights Control (ATA 27)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "74694aeb-44cd-4737-979f-34e3a25a3ef0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825020610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan teknik persiapan lahan produksi tanaman perkebunan semusim pen<PERSON> serat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "746a18ea-0674-40d8-b2fa-aa654e8c1d80", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:37"}, {"kompetensi_dasar_id": "746b2e9a-2153-4fea-98db-96fb1e29d76d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825020800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengamatan gejala serangan kerusakan tanaman pangan akibat faktor biotik dan abiotik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2019-11-27 00:27:40", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "746c03f5-8117-4f1d-a784-57e053da097d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 842040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara mengasah pahat ukir kayu bentuk lurus dan lengkung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "746c0a19-42c7-431e-8d9c-94b957ce4baa", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:37"}, {"kompetensi_dasar_id": "746c4a0c-e1bc-40a2-ab4a-8ea27f5ce247", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:06"}, {"kompetensi_dasar_id": "746d5976-a05b-4153-b96a-25292858bdc2", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisa kapasitas kerja pengolahan tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "746dc8ee-3cd3-4ac6-bc34-0523295da780", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur kerja terstandar dalam perawatan sistim kontrol yang terhubung komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "746e0474-472e-4d76-b9f8-a0286a2935c4", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 802020700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil instalasi dan konfigurasi subscriber internet telepon.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "746ee5e2-dfe1-4e2c-86db-33ee09c00fce", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "746eeba6-04ce-4809-a292-ac65b7340e7d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprak<PERSON><PERSON><PERSON> pelaks<PERSON>an pembagian waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:17", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:17"}, {"kompetensi_dasar_id": "747037ce-1151-4c90-9d00-0156493f9841", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "747187cb-6c50-4026-bd84-cd62cd9f0882", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis teknik penyiapan laboratorium kultur jaringan tanaman perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "74728130-13d3-446c-88df-2b2359587b0a", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  K3 dalam  mengawasi boiler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "7475505a-8cdd-4689-aa76-122cc156076c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827310200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi keselamatan pelayaran dan pencemaran laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "7475681c-5a8b-4e5c-9be1-594526058ef5", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 825021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan media kultur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "74757e54-21e5-4689-a47d-759057b2eab3", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 803080800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kondisi operasi rangkaian Run-Jogging Forward-Reverse berbasis kontaktor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "74763576-caaf-41e7-b980-cf46ae84dbb7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 839100200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prosedur pembuatan desain sandal sebagai produk alas kaki", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "74767256-f9a7-4a10-96fe-da762188d440", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi dan aplikasi aktuator pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "74768822-69be-4de1-8d39-5ff0c3586b36", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 802030310, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON>t laporan hasil pasca-produksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:20", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-06-15 14:50:20"}, {"kompetensi_dasar_id": "747781fc-0892-413a-b29e-5671bb5eebad", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> Housekeeping", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "74787238-7964-43c5-8348-5d0058f91f3d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "74789d81-f948-4315-89c8-bc1eff950984", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah hasil penyelidikan yang berkaitan dengan suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "7479df7d-13d3-44e6-b995-b2878ae4bed4", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 807022700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>ren<PERSON><PERSON><PERSON>t pada poros", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "747ac0f1-4b34-47d5-b139-d364fe619314", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON> penanganan limbah pabrik pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "747b35c6-d607-4a37-9e91-6cbe8c794ebf", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 843050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> pembalikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "747b9419-e168-4242-88db-e404d4e1636f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memresentasikan hak atas\r\nkekayaan intelektual", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:43", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:28:43"}, {"kompetensi_dasar_id": "747d1c43-8b32-48bb-be33-25b21e067f9d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 829080800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan hidangan sayur Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "747d556b-83c2-44cf-ae97-f0761dcfbb76", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pen<PERSON>an berbasis mikroprosessor.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "747dc436-b2ed-4edb-99ce-c77a8323088f", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 828160100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> keluhan pela<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:19", "updated_at": "2019-06-15 15:07:19", "deleted_at": null, "last_sync": "2019-06-15 15:07:19"}, {"kompetensi_dasar_id": "747e07d4-7cda-4bf4-a95e-31af37337fa4", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pengoperasian sistem tata udara industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "747f1252-aac2-4339-a37f-2625d3068d35", "id_kompetensi": "4.2.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (31): 13-14 dan Q.S. <PERSON>Ba<PERSON> (2): 83 sesuai dengan kaidah tajwid dan makhrajul huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:34", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:34"}, {"kompetensi_dasar_id": "747fd7f6-d9a3-404a-aca7-5d4bd38bdc84", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi macam-macam penyakit jaringan lunak mulut.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "74804818-0823-40bb-96b1-bccdbd7ff1a0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 839120200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menatah produk kulit mentah tatah sungging tiga dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "748099b5-85d4-46df-97c3-266c2b9b7173", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 802031110, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencitraan gambar digital (draft rendering) pada animasi 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "7481ec72-9fa5-4b37-8747-b5fb41adeda8", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 821080300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengoperasian mesin bor pahat (cheesel)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "7482674b-44ec-49a2-b1dd-e969e637c815", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil analisis kromatografi lapis tipis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "7483faf9-1e74-4ee0-b1a2-6a8b37ec8531", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cara men<PERSON><PERSON>, udara dan angin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "7484dfe2-e4c8-4107-8ff4-aaf1b4c596fb", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807021720, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kesalahan pada rangkaian listrik yang kompleks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "7485055b-205e-43c7-96e2-fec0b973614b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800020300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>, per<PERSON><PERSON><PERSON><PERSON>, dan gangguan pada usia lan<PERSON>t", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "74852f59-cb14-44e9-9cb2-07100490d7ce", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 806010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pekerjaan pengisian refrigeran ke dalam unit refrigerasi domestik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:20"}, {"kompetensi_dasar_id": "74853664-dd6e-473c-9410-b289dbc50d13", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usahadengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:53", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:53"}, {"kompetensi_dasar_id": "748586c8-9be4-44b4-960c-487079b75ff6", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 820120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan Pengecatan Panel Plastik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:21", "updated_at": "2019-11-27 00:27:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:21"}, {"kompetensi_dasar_id": "7488f256-13c8-43ca-bd00-d58b17c9e145", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> unsur-unsur musikal dalam repertoar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "74896233-3949-461a-a7e9-de2ee42cc2bd", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401251600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melakukan pengelompokkan produk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "748a1a78-ecf5-419e-bd9e-74a21b4070de", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825220500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Me<PERSON><PERSON><PERSON><PERSON> makanan herbal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}]