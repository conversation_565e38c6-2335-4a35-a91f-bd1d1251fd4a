[{"kompetensi_dasar_id": "38791054-9169-4cff-950f-4dfe000b2077", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 808080800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perintah-perintah CAD3 dimensi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "387aaad3-e79e-4630-b3f5-de55dd67d17b", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 824060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengulas Format radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2019-06-15 14:58:10", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "387ab33a-2600-4e95-92b0-529e5c60aed4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi dan aplikasi piranti sensor proximity", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "387abf05-ed91-4559-80fd-6a3103ad2526", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 817020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hubungan antara tekanan-volume-temperatur pada Si<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2019-11-27 00:27:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "387bd928-929c-4f02-8c39-4d9a8c570c02", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan alat khromatografi kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:47", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:47"}, {"kompetensi_dasar_id": "387be522-e819-4cc6-bb40-995f9641d1d8", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 843062000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hubungan antara karawitan sebagai iringan dengan cabang seni lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "387ce178-1ac8-4213-9b03-f4c7600da95f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 828190130, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menganalisis informasi mengenai perincian pemesanan tiket penerbangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "387d3d42-5e60-42c7-9183-84c89cd38869", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "387d57b0-3774-4685-9a1c-eadc406533c6", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 803010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan aljabar <PERSON>an pada gerbang logika digital.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "387d5973-6826-4cdc-8ab5-ca03b0fa2ee1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825210400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> t<PERSON>, prinsip dan teknik-teknik mengemas dan menyi<PERSON>an bahan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "387efdd4-6353-4587-921a-085747df58a8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:59"}, {"kompetensi_dasar_id": "387f108a-7f0c-4e8b-ad1a-3dcd67584d28", "id_kompetensi": "3.4", "kompetensi_id": 2, "mata_pelajaran_id": 401131500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemeriksaan coliform dan identifikasi bakteri E. Coli", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "387f6818-0001-4acb-8db1-6284baf0dbe4", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur teknik pembuatan ulir cacing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "387fa10c-5db0-435e-9b83-0b3c7dd0a2de", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna surat lamaran kerja.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:27"}, {"kompetensi_dasar_id": "387ff22a-cce5-494e-b474-6a819a67d67b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> gaya dalam struktur bangunan dengan cara analitis maupun grafis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "388016d7-3c99-44f9-be79-72ec374482de", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:30"}, {"kompetensi_dasar_id": "3880a6dc-72a2-4ba9-a2e9-68acf7fb841c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:22"}, {"kompetensi_dasar_id": "3884ad4d-b873-4dff-b6e4-be92f46883ea", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola menyerang dan bertahan) salah satu permainan bola kecil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:35"}, {"kompetensi_dasar_id": "3884f946-3cc9-4c6c-a801-7adb90b127d4", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401251221, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Mengolah data berdasarkan rumus matematik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "38850b9c-9705-4002-98c3-3785539df23a", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:19"}, {"kompetensi_dasar_id": "388510e1-cc52-40fd-99a2-8d793bf72cbf", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:12", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:12"}, {"kompetensi_dasar_id": "3886b9ed-f95b-45b4-bc6a-8dde5f5a1cab", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827210600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan teknik dan tipe o<PERSON> (HO, PDTO, PD dll )", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:51", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:51"}, {"kompetensi_dasar_id": "3886d03f-1156-483f-8e64-41d2457d8cc1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "3886e6d2-95d9-4a26-93eb-cc001e53c758", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 843040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembang kan karya seni lukis ekspresi teknik aquarel dan plakat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "3887dc5e-185f-45f3-8cbb-70794eb3387d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 824060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah acara radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2019-06-15 14:58:10", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "38881a21-f7d3-4fba-99ee-b3c5b350bec5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Be<PERSON><PERSON> aktif dalam menjunjung kehidupan yang multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "388a87d4-c7a9-43cd-a8f1-6a3094eda10a", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 820140200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem audio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "388cd532-c76c-4001-a78e-c5c8364cd3ac", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkangambar kerja pemasangan komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:41", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:29:41"}, {"kompetensi_dasar_id": "388dd314-fdfa-4460-b26b-a69962ddcf2e", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> berb<PERSON>i jenis jacking, blocking dan lifting", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:37", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:37"}, {"kompetensi_dasar_id": "388ee55c-c19c-49f7-adef-e1361b9dfb68", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:41", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:41"}, {"kompetensi_dasar_id": "3890ab78-a72d-4da2-b29d-423260121515", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821130400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami prinsip dasar kerja dan cara kerja sistem hidrolik serta kelebihan dan kelemahan dalam sistem hidrolik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "38914c5a-5c0f-4980-989b-90e26d1c76ee", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401131400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> titrasi penetralan (asam basa)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "3891c543-265b-45ae-bcfd-d278879038e0", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 803040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "memahami instalasi kabel <PERSON>,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "38920f64-2a6e-46a6-8513-b68eeeb0254e", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:33"}, {"kompetensi_dasar_id": "3893309d-99c2-44bf-baee-f146858ae643", "id_kompetensi": "3.37", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is screen direction pada <PERSON>yu<PERSON>dar<PERSON> program televisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "389345bf-2d38-407e-876b-9e46c735aa1b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola kecil dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:56", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:56"}, {"kompetensi_dasar_id": "3893aa81-5614-4aa7-8b2d-dcb015a8dcff", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses grading (color correction)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "38941e82-8d7a-4758-a13b-e69e3cb5079e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan Differential System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "389433b7-eb88-442c-b244-f7300f3eec82", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843050600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan peralatan perekaman audio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "38945423-71f5-40f2-9a37-a614969a6995", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825250300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis jenis ikan hias yang sesuai untuk paludarium", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "3895a418-263c-4361-98b1-caada546216b", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 828060100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakuka<PERSON> pengel<PERSON>an ars<PERSON> elektronik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "38973b42-96bd-42be-8d83-520ebc723a36", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825061000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan tentang rekording  dalam agribisnis unggas petelur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:07", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:07"}, {"kompetensi_dasar_id": "3899bd2b-4618-43b6-938b-64fc9575e68b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821130400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan macam-macam jenis fluida yang dipakai dalam sistem hidrolik beserta kerusakan fluida hidrolik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "389a6489-f942-412d-b131-e4bfa49710d8", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843090800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeragakan  teknik garap gerak berdasarkan cerita", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2019-06-15 14:58:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "389b2e8c-de13-4d49-bb87-8bcff9c5a676", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 809020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses cetak satu warna/dua warna sesuai pesanan cetak dengan teknik cetak rotogravure/intaglio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "389b34a1-c435-40bb-9c96-48f74865d05b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan objek k<PERSON>, menganalisis informasi terkait sifat-sifat objek dan menerapkan aturan transformasi geometri (refleksi, translasi, dilatasi, dan rotasi) dalam memecahkan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:59"}, {"kompetensi_dasar_id": "389ba00c-cd35-4ec3-a0d1-6ed436cadfc1", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 300310400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis tentang kegiatan di waktu lampau (Shitakoto) pada teks transaksional lisan dan tulis dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan sesuai dengan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "389c3493-3e0f-4c62-8528-1aed2d234170", "id_kompetensi": "4.35", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> penyatuan suara dan gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "389d26ac-12b7-484f-8f5c-d9e89c0730c3", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 805010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Metode dan <PERSON>-Pengembalian <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2019-11-27 00:27:46", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "389d3757-0438-4607-92c3-2b3f7adbb060", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kondisi komponen-komponen dan sistem kelistrikan PLTS On-Grid", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "389de065-ba7f-48d5-8fa5-6d28005e4c6e", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan komponen instalasi penerangan pada bangunan bertingkat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "389fb0c0-1c4e-40e0-9b52-416254147288", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 828020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  Sistem Informasi Manajemen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "389ff7c8-3595-4c5f-8a5d-b945a5f8eafd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827390900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain refrigeration", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "38a0574c-59ad-4a78-bb1c-48a9d261f793", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membongkar benda keramik di dalam tungku hasil pembakaran biskuit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "38a07036-5e0c-4097-a86a-5a2ed746fa7d", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil editing dari syuting program televisi non drama multy camera system", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "38a2609f-fdd2-42c6-bdc6-e0ce6f484a55", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 821090320, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar konstr<PERSON>si depan kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "38a2f97a-bd9b-40a5-8265-508bcf3a525f", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kontrol yang terdiri dari PLC, pnuematic dan hydraulic", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "38a3f6cc-558a-4b85-9831-bae2dfc639d3", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 821090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik memfrais dengan mesin frais CNC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "38a404f7-9b33-4b76-8e2b-5d608fdbf11a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800080230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan komunikasi di tempat kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "38a4b1c0-f32d-4155-b2fd-abf4691c3c5b", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 821120300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis berdasarkan pengamatan terkait perbedaan komponen mesin di atas dek", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "38a50810-e955-49da-933c-53b75973072c", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar kerja (rancangan) pemasangan sistema pembumian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "38a5676a-1b28-4df9-91c5-11475a905101", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pekerjaan pemindahan tanah mekanispada pekerjaan jalan, i<PERSON><PERSON> dan jembatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "38a5c602-340c-4a29-99f8-1deb44cef4a6", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is rangkaian catu daya tinggi pada rangkaian penerima televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "38a5fa59-0b3b-49f2-8d3b-67001cc62de6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 814130100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengu<PERSON>an bahan baku chips", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "38a7b85c-5186-4a97-85d6-b76b3a07c903", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300310800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mendemonstrasikan tindak tutur untuk menyatakan dan menanyakan tentang kehidupan di sekolah (la vie scholaire, les matières, les horaires et les gouts) dengan memperhatikan fungsi sosial, struktur teks, serta unsur kebahasaan pada teks interpersonal dan teks transaksional tulis dan lisan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "38a89558-11af-486d-8dd7-168d4ecc507e", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:15"}, {"kompetensi_dasar_id": "38a901c3-358e-45f8-89c2-31cebec1f85c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "38a91ca8-6416-4dfb-9a30-c2d267fafbf4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:01:08"}, {"kompetensi_dasar_id": "38a982b7-8074-44ea-8b7f-ed9605ca02ec", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 826060520, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis data hasil pada hutan alam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:34", "updated_at": "2019-11-27 00:29:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:34"}, {"kompetensi_dasar_id": "38aa9e22-7a43-40aa-b9f8-7cdcd35d511d", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:28"}, {"kompetensi_dasar_id": "38aacdcd-5a49-4322-8e18-7f59b2cd7c99", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401131900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prose<PERSON><PERSON> warna pada bahan tekstil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "38aaf781-306c-470a-a383-e92ee54e3391", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843062300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pola garap sajian karawitan mandiri etnis nusantara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "38ab5f67-19f6-4479-97f7-7baae6ddd915", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterpreta<PERSON><PERSON> jeni<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, karakteristik, dan media dari berbagai teknik sistem kendali dengan simulasi software pada instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "38ad15c0-ea72-4225-9ec8-8d8749cac7f3", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "38adc2e7-1e50-42df-8474-e4cd6dfbf179", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 821090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan penyekrapan benda dengan mesin sekrap sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "38af5fad-7750-4f45-b217-13f14d7b5eb8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:45"}, {"kompetensi_dasar_id": "38afeea8-00eb-4e70-b883-eeab2bc9d5e9", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827370220, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan aturan 27-31 tentang sosok benda", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "38b06f64-ed92-4be9-b872-25f270dccfd1", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan macam-macam tranducer pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "38b0704b-fa45-4d87-b7fa-d10bf0cb99b2", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  elemen-elemen penunjang eksterior sesuai kebutuhan maupun konsep dan gaya pada eksterior.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:34:02", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:34:02"}, {"kompetensi_dasar_id": "38b200d5-fd52-4fb9-8a81-b2dbe202d4ae", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:50"}, {"kompetensi_dasar_id": "38b23ffc-6520-4e77-946d-558343bd004c", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "38b2cb9f-5a8a-41c8-b0de-743cbd8ce9f0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pengoperasian mi<PERSON>p", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "38b33639-d73d-48ac-9380-32ce76af52e5", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 401130620, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi data analisis kadar lemak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "38b40df9-e956-4c46-b836-f63fe303c031", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perhitungan sucker rod pump", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "38b42d5f-f0ef-4715-a534-0dbdf9019bc9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843060700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeksplorasi pengembangan ragam gerak  tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:16"}, {"kompetensi_dasar_id": "38b547aa-1f3d-4cea-beff-e8e69db5e861", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan alat-alat ukur hidrolik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:39", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:06:39"}, {"kompetensi_dasar_id": "38b6694c-1134-4151-b06a-37d34c8a7d8a", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> hasil evaluasi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:35"}, {"kompetensi_dasar_id": "38b67bee-903d-458f-8f1d-6d76afa23096", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "38b770ef-89d8-4ec5-9600-2f7697a43588", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   3.4 Melaksanakan penyiapan benih/bibit<PERSON>man pangan, tanaman pangan sumber karbohidrat alternative, tanaman pewarna alami (pangan dan non pangan), dan tanaman pengawet alami (pangan dan non pangan), serta tanaman multi manfaat secara organik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "38b8c6ea-09a8-4173-9e42-c2bca8555b9b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800030100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengidentifikasi jenis obat secara umum", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2019-11-27 00:30:20", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "38b8cf4c-1dd1-40fa-b277-82a863444481", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyiapkan laboratorium untuk analisa rutin", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "38b9cffd-0fef-4e2e-896f-71d128b19eb2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:11", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:11"}, {"kompetensi_dasar_id": "38ba9e94-034d-4468-94b1-19037ffd137b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "38bc43ff-2501-4fc3-8771-f95745c11b14", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kemajemukan bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:58", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:58"}, {"kompetensi_dasar_id": "38bd5eea-a5a0-4bbb-932f-c23fa7626269", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 828060100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan penanganan surat masuk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "38bdb3a0-8fdf-44cb-8793-54e16060885e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor yang mempengaruhi struktur bangunan berdasarkan kriteria desain dan pembebanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:05", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:03:05"}, {"kompetensi_dasar_id": "38bf2ac9-ee68-44e4-971c-4b2e54fce9d4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:16"}, {"kompetensi_dasar_id": "38bf4422-14a1-4fa0-8201-015cfd96a435", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 843050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan berb<PERSON>i macam modes", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "38bfcf50-03ff-4bf8-9160-4c17f092d641", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonst<PERSON><PERSON> per<PERSON>/isu, sudut pandang dan argumen beberapa pihak, dan simpulan dari debat berkaitan dengan bidang pekerjaan secara lisan untuk menunjukkan esensi dari debat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:11"}, {"kompetensi_dasar_id": "38c00cf3-63cf-4f0a-86f6-15b9dc7b2e1a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "38c0726f-70ba-4af4-a3f6-951e6262a272", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843050100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menuliskan tanda sukat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "38c1127d-f5fb-4f99-8eb1-1db63127d8bd", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis gambar 3D kompleks", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:48", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:08:48"}, {"kompetensi_dasar_id": "38c11a18-5753-4e06-801a-4132dade7cc6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "38c1da5b-46e0-4572-85a5-3c4c2a1698b2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.1 Menerapkan kaidah gambar proyeksi dalam membuat gambar proyeksi bangunan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:17", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:07:17"}, {"kompetensi_dasar_id": "38c249ab-1b4d-4f81-a54a-e64e404fc14d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "38c27450-d112-411c-be2b-ce1035f6752a", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825110110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan teknik pengoperasikan alat mesin bubut dan alat-alat bantunya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:46", "updated_at": "2019-11-27 00:28:46", "deleted_at": null, "last_sync": "2019-11-27 00:28:46"}, {"kompetensi_dasar_id": "38c47204-8ecf-4324-b644-450629e58356", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> penanganan nyeri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "38c628d8-3865-41b6-a3f9-3274592d5160", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> perintah dalam gambar komponen pesawat udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "38c63fcf-4e69-4dbc-b86a-0825388ac7bc", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 803071300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membangun rangkaian elektronik untuk mengelola penggunaan daya sistem pembangkit listrik tenaga surya (PLTS) rumah mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "38c64825-facb-4071-9199-64a9d4866ad5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "38c6f959-9b7c-4038-8792-3688baa11d8d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825150100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis survey dan klasif<PERSON><PERSON> lahan pertanian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "38c71fdd-aeeb-4219-aabe-21ae9b76fb6a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 843010720, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> a<PERSON> mayor, minor, diminished dan augmented", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "38c803a1-0222-4853-8360-6ed32a67df09", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 814030900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peta proses operasi untuk mengetahui stasiun kerja yang terlibat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:26", "updated_at": "2019-11-27 00:29:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:26"}, {"kompetensi_dasar_id": "38c9d1c3-d662-438c-93ef-8773364488b8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827080200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> tindakan setelah terjadi kondisi da<PERSON>rat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "38cbcedd-e2d5-4321-baea-4cc7b7f9ec77", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825240210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memodifi<PERSON><PERSON> pengemasan produksi olahan diversif<PERSON> hasil perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "38cc3032-36a4-46c8-9012-a30ea0f25a0f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 814170100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pemeliharaan komponen mesin dan peralatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "38cd5390-9d70-425c-86f4-2b99c0b33ef6", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung RAB pada pekerjaan perawatan bangunan gedung yang tergolong restorasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:34:01", "updated_at": "2019-06-15 15:34:01", "deleted_at": null, "last_sync": "2019-06-15 15:34:01"}, {"kompetensi_dasar_id": "38cd6b5d-3c04-4d26-939e-79d840cb5a9c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:41", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:41"}, {"kompetensi_dasar_id": "38ce3f60-3320-4d79-962a-a6f6ef5d1fa5", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:52"}, {"kompetensi_dasar_id": "38cf0950-1010-4617-a4ae-382b4db804a8", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 827390600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain main engine and associated auxiliaries", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "38cf9476-c6e2-47aa-a029-9ca6ca012f69", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  perjuangan bangsa Indonesia dalam upaya mempertahankan kemerdekaan dari ancaman <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "38cf9f9e-4272-4611-8bea-d94c82f97d76", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "38cfae0b-a7d9-47d5-99ca-2eaacb8b60bf", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>a lajur (worksheet) perusahaan manufacture (Harga pokok pesanan/harga pokok proses)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "38cfd76b-6743-48ce-b0e2-5db69f9fe8d2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis lembaga keuangan bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "38d01acc-453f-4ca4-b29a-a5d0c078f4ef", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkartel benda kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "38d17be2-f84c-411b-bc14-4da64bc0067c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi data analisis konduktometri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "38d1a5e9-a0df-4f0a-b99c-5b930b069120", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 827310200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ILO Convention,SCTW 1995 dan SCTW-F", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "38d1cbf8-ef42-4eba-81c3-7c12f20e3b66", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor-faktor kema<PERSON>an dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:01", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:01"}, {"kompetensi_dasar_id": "38d1f896-f181-48c0-85d5-97f4f2578354", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasangan instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "38d21a4b-f458-425a-81b4-47469163e969", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 804100600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa hasil pengoperasian peralatan hubung bagi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "38d2f6f0-edfd-49fa-acf4-211d03ead60e", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 820140200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem pengaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "38d30188-cadb-4237-9f01-82f9bc932b36", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 802031800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan prosedur per<PERSON> dialog", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "38d3b9e4-3a0d-462a-9410-8cf6195990af", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar penggu-naan kontrol suhu dengan sistem mekanik, elektrik serta  hubungannya dengan interface dalam pembuatan sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:45", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:03:45"}, {"kompetensi_dasar_id": "38d43cf0-b62c-4303-9753-5397a3e90f4a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran Indonesia dalam hubungan Internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:50"}, {"kompetensi_dasar_id": "38d43f00-3b38-474c-b804-11e4e4dc79eb", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "mengidentifikasi penanganan limbah bengkel perawatan otomotif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "38d4b15c-41ac-4802-8fd6-83cf6ad1fc08", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 818020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan sifat fisik dan mekanik  batuan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "38d51581-b096-4505-af0a-78b2264933cf", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 826090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>un<PERSON>kkan prinsip stasiun pakan satwa liar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "38d5b805-4289-4a0a-93ad-00164fe95c54", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengangkutan hasil panen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "38d5f336-e259-40f2-8f4d-aa46075f5208", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar proyeksi orthogonal dan isometrik dari objek gambar atau benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:33:44"}, {"kompetensi_dasar_id": "38d6f588-9539-44ae-9f20-7a087ae7c139", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:56"}, {"kompetensi_dasar_id": "38d9006a-9365-428d-97c6-4d0b1ef7845e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 807021910, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sebuah komponen pesawat udara berbentuk kurfa dari logam dengan proses peregangan. <PERSON> pen<PERSON>n", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "38d96a54-500a-4eda-b361-0982947dc7fd", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan   teknik  pembuatan benda kerja   pada mesin bubut, dengan su<PERSON>n/toleransi khusus.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "38d9983a-fb99-489a-942f-0e571af54c4d", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 831010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> jenis-jenis kain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "38d9a1d8-b8df-4fbe-8f29-6b3128e49c2d", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik tenun satin dengan ATBM", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "38d9c3c2-3630-422d-b9e7-ebf0b559dacc", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804050470, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pela<PERSON> pek<PERSON> konstr<PERSON> dan properti", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "38d9c9f0-a666-4960-9456-b0902a31abc4", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 800080230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peraturan pemerintah yang terkait dengan laboratorium kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "38da3ea2-d184-42bb-8c7a-04ad7b551b2b", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 843080510, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan dhodhogan dan keprakan untuk mendukung pedalangan ringkas dengan cerita Mahabarata atau Ramayana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "38dac4f9-da92-4658-8dea-8ada4b8cc8bd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 831101200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat desain dasar dengan prinsip men<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:49", "updated_at": "2019-11-27 00:28:49", "deleted_at": null, "last_sync": "2019-11-27 00:28:49"}, {"kompetensi_dasar_id": "38db3e32-c78e-410b-b2b1-640c52dceaab", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usahadengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:20", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:20"}, {"kompetensi_dasar_id": "38ddbba5-b510-47e0-9b67-46e0b7144c37", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan & menerapkan instalasi sistem audio paging", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:41", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:30:41"}, {"kompetensi_dasar_id": "38df4fe4-f19d-4321-b920-b6587593c45c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:12"}, {"kompetensi_dasar_id": "38dfe1f5-861e-4918-9236-f531c33cccd4", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan bagian-bagian struktur jembatan berdasarkan spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:34:01", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:34:01"}, {"kompetensi_dasar_id": "38e0d312-5291-49d1-8394-21ed408f1835", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional, lisan dan tulis, pendek dan se<PERSON><PERSON>, yang melibatkan tindakan memberi dan meminta informasi terkait pendapat dan pikiran, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:12:01"}, {"kompetensi_dasar_id": "38e107f5-5e11-40ed-a3e3-6950d8e40536", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 818010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pemetaan geologi detil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "38e15215-d630-4975-ac26-b16efb9ddec6", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:03"}, {"kompetensi_dasar_id": "38e175b5-331e-40c5-9cc4-861f6e62cf2b", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:30"}, {"kompetensi_dasar_id": "38e1dc94-1f78-4d62-8aa7-a51a1df7932e", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang proses perawatan sumur migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "38e2015b-e7a9-410f-b9a3-2ccee57d7bcb", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 830140200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan perawatan wajah menua dengan teknologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:34", "updated_at": "2019-11-27 00:30:34", "deleted_at": null, "last_sync": "2019-11-27 00:30:34"}, {"kompetensi_dasar_id": "38e304fd-ca0d-4631-9c54-0a1fad28b217", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804190200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan perangkat keras mikrokontroler pada sistem instrumentasi kontrol proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "38e32574-e7db-4df5-995c-9788f8fa02e6", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara menyusun gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:01", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:57:01"}, {"kompetensi_dasar_id": "38e38d40-d3b3-482a-af5a-16e24ab86600", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:53", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:02:53"}, {"kompetensi_dasar_id": "38e3bef7-a838-4ffb-b2cc-fa95afd5aca4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807021600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konstruksi sistem mikroprosessor.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "38e3ea4e-e90a-4f80-afd2-16ebc0175dfc", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:46"}, {"kompetensi_dasar_id": "38e40fdd-eff9-4771-a1d6-b8668e407fab", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 816010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> proses pem<PERSON><PERSON><PERSON> kain grey", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "38e57401-7c26-480f-9374-f6d042f622ad", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 815010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah dalam ranah konkret dan ranah abstrak penanganan mesin Roving", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "38e5af5c-dae6-408e-ac00-c6cbb7f5e7c4", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:07", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:07:07"}, {"kompetensi_dasar_id": "38e7fa2d-4598-4f2e-b0c3-fe0f90b6d484", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemasangan sistem Proteksi motor listrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "38e82171-e9ed-4a12-bc4e-8c5f239c1d09", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 802031110, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil line test key frame dan inbetween", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "38e82cdd-0caa-45e0-8cb6-131f294bd63c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Prosedur memperbaiki Peralatan Tangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "38e91278-dcb3-45fd-a736-9f91fb0e3177", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:12"}, {"kompetensi_dasar_id": "38e9aeec-3a4e-4516-ae66-d4a63a1eeff9", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 822040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pengoperasian hand power tool", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2019-06-15 15:12:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "38e9b438-b682-4a26-85c4-f21fb162d0cb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:12"}, {"kompetensi_dasar_id": "38e9e870-a1f0-43c9-86ae-7b372ccc08c5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843090300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data perkembangan teater Barat Modern", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "38ea27d4-13ae-4cce-8f9f-9be00990074a", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 807021910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan bahan/material sesuai dengan bentuk benda kerjanya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "38eaa92e-1755-4ba7-a820-85cd295307d9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804160600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar bentangan secara matematis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "38eabb57-5101-4858-bf7d-953d169584ad", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:24"}, {"kompetensi_dasar_id": "38eadab8-fdf7-4241-8b79-f1a48ac61040", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami satuan-satuan charge, force, work dan power dalam contoh perhitungan sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:51", "deleted_at": null, "last_sync": "2019-06-15 15:06:51"}, {"kompetensi_dasar_id": "38eb7b07-1239-4540-b1b7-deb1a60574aa", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan workshop equipment", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:14", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:14"}, {"kompetensi_dasar_id": "38ec1ff7-99c4-4af2-8b2e-b9f7325fb515", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 829090900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang dan menilai makanan utama (main course) dengan teknik fusion (fusion food)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "38ecc41b-c632-4813-bdfe-5e6c8e006add", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemograman mesin  frais CNC", "kompetensi_dasar_alias": "Mampu menggunakan  teknik pemograman mesin  frais CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:59"}, {"kompetensi_dasar_id": "38ef0881-3adb-4382-a94c-a752ac79e87c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 829050500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis wadah hidangan dari sayur dan buah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-12-11 11:03:44", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "38f04135-53c2-45a1-aba8-90dfd1a767ac", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 831101400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep menggambar dalam bidang seni rupa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "38f10717-c162-4948-95d7-488468b5b8dc", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 827210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsip tata letak bahan dan alur proses pada pengolahan hasil perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:35", "updated_at": "2019-11-27 00:29:35", "deleted_at": null, "last_sync": "2019-11-27 00:29:35"}, {"kompetensi_dasar_id": "38f19ba3-fbf0-4a2f-87db-4b34ff22fca0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:11"}, {"kompetensi_dasar_id": "38f50597-e01a-4dce-b44c-2759e71ba79a", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 809020100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil pengolahan limbah cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2019-11-27 00:27:35", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "38f51fbb-eb7d-4761-817f-b8b297d64d80", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih dan menerapkan strategi menyelesaikan masalah dunia nyatadan matematika yang melibatkan turunan dan integral tak tentu dan memeriksa kebenaran langkah-langkahnya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:51"}, {"kompetensi_dasar_id": "38f55910-aced-4b44-8db7-390c4aa0d7a9", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan konsep dasar ilmu kimia dalam percobaan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:08", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:08"}, {"kompetensi_dasar_id": "38f5c080-8890-41cf-aa4b-b171d8d3168e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan badan dengan sistem body scrub peeling", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "38f68ebc-f290-4ee7-9418-382652ce6689", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:10"}, {"kompetensi_dasar_id": "38f72a85-aef6-477c-b3c2-d59d8de3e89f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 842040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan cara merawat dan me<PERSON>baiki/MR peralatan finishing kayu teknik semprot", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "38f87dfe-b215-4242-b421-8c830281a484", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 818010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengukuran dengan alat ukur theodolit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "38f98b52-c447-47c3-b971-86959e3236ca", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda datar (survace grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:18", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:03:18"}, {"kompetensi_dasar_id": "38fb77d5-95d7-4d11-af6d-50ff6ab4d5b2", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> titrasi penetralan (asam basa)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:28", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:27:28"}, {"kompetensi_dasar_id": "38fc4eb4-1ac0-4dfa-8a97-573146ad00e2", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "38fecd89-e884-47fc-aa4f-90013847cedf", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teori pasang surut air laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "38ff5139-2652-435d-a806-11c27757cf6a", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyimpulkan kelayakan bahan kimia untuk disimpan atau dibuang", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:24"}, {"kompetensi_dasar_id": "38ff5d92-6c6d-4bed-b28c-0260f1cea8d8", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401141100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memberikan informasi pemakaian obat yang berhubungan dengan bioregulator", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:20", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:10:20"}, {"kompetensi_dasar_id": "38ffac99-c2b7-443d-b7c2-25e4d4a4aeea", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:39", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:39"}, {"kompetensi_dasar_id": "39006396-e3a7-4f9c-8619-7073ca352f64", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem kontrol opened & closed loop dalam kehidupan sehari-hari dan otomasi industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "39013a4a-3a1f-4615-9f39-1f6083980ab4", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menganalisis aturan sinus dan kosinus serta menerapkannya dalam menentukan luas daerah segitiga.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:17", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:17"}, {"kompetensi_dasar_id": "39026e4b-b4ac-43bd-bb8f-b240a0d1ff05", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:05"}, {"kompetensi_dasar_id": "3902a9f0-500c-4c41-8f25-3bd4fce4e987", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 824050600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is perekaman gambar untuk film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:50", "updated_at": "2019-11-27 00:28:50", "deleted_at": null, "last_sync": "2019-11-27 00:28:50"}, {"kompetensi_dasar_id": "3904a255-7d68-4ccd-b303-19b7c5fd2cb3", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:40", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:40"}, {"kompetensi_dasar_id": "3906494b-e69c-4bfe-a9ec-628dcb6195ec", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803081000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>, spesifikasi dan kuantitas komponen pneumatik yang dibutuhkan system control", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "3907cc6f-c74c-4895-b6b4-f1f930560a30", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil survey toponimi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "3909a422-4c30-40fe-ac59-a2f9516cb3a1", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 829020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> proses pencucian guest laundry", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "3909aa39-7590-4004-89bb-c2fb18591312", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 829111100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Menganalisis kue dari adonan pie", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "3909ca33-5fb6-4651-8e46-862adc79ecad", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 805010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis peralatan ukur jenis optik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "390a35db-100f-439f-a73b-cbb266535715", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan koor<PERSON>si proteksi jaringan transmisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "390b4f88-45a7-4750-85d5-c6fb564d9ecb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor yang mempengaruhi struktur bangunan berdasarkan kriteria desain dan pembebanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:49", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:26:49"}, {"kompetensi_dasar_id": "390d04bc-55e9-4098-86af-3b9af5483f3a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:18", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:18"}, {"kompetensi_dasar_id": "390d30b1-f4b7-4563-b9e5-bd20615243f3", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827320110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Identifies Rudders and propellers", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "390ebfc6-f4a1-4454-81c2-7e84a6516dae", "id_kompetensi": "4.29", "kompetensi_id": 2, "mata_pelajaran_id": 824050700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan hunting dan survey lokasi produksi film", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:13", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:13"}, {"kompetensi_dasar_id": "390f2e91-ab1a-4334-9dfb-fa6d85062dfa", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda datar (survace grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:33", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:33"}, {"kompetensi_dasar_id": "390faa3e-20b5-4cef-b957-32bfba63d0b5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825240200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkombinasikan pengemasan produk diversifika<PERSON> hasil perikanan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "39107ea0-58e9-47eb-8f15-077d1cb2b225", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan prosedur <PERSON> dan <PERSON><PERSON><PERSON> serta <PERSON>n Hidup dalam pelaksanaan pekerjaan <PERSON> Bangunan Gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "3910cae3-f58e-4647-affb-b63801e44d6b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:01:08"}, {"kompetensi_dasar_id": "3910f95a-a1a8-4b7e-b962-30238da3204b", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar blok diagram teknik kontrol", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "39111d91-42f0-41f5-b76d-668ed262e234", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:20"}, {"kompetensi_dasar_id": "39115e9e-43bf-4350-899c-55d846ab2bf1", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 822130100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengetahuan dasar dan konsep tentang generator sinkron dan asinkron.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:51:15", "updated_at": "2019-06-15 14:51:15", "deleted_at": null, "last_sync": "2019-06-15 14:51:15"}, {"kompetensi_dasar_id": "391162af-da9b-4f42-9ddb-eca77c523831", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401251050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan tahapan pen<PERSON>an laporan keuangan untuk perusahaan manufaktur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2019-06-15 15:07:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "3911f9d7-6064-4f5d-93a0-af1edb5f163d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:41"}, {"kompetensi_dasar_id": "39121006-66b5-4c57-920f-f261e91309c7", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "3913677c-d46b-435c-a86b-a74fa07f5ab6", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "3913ab75-f099-4b5e-8453-3d49e7ffe8f7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825230300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengembangan produk hasil perkebunan tanaman tahunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2019-06-15 15:07:08", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "3914a938-d42e-4311-8c2d-5932ee281d24", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:30"}, {"kompetensi_dasar_id": "3915aec5-5779-4655-8b7c-561f823e6af1", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> lanjut usia mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "3916b675-cddd-4461-a94a-66bffa214b78", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:36", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 16:06:36"}, {"kompetensi_dasar_id": "3919e534-31ba-4a56-8a8a-56d1f37e39d7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:50"}, {"kompetensi_dasar_id": "391a8bd2-cb1c-45ce-a4fd-39aedf723a77", "id_kompetensi": "4.36", "kompetensi_id": 2, "mata_pelajaran_id": 800050430, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan perawatan luka dasar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:12", "updated_at": "2019-11-27 00:30:12", "deleted_at": null, "last_sync": "2019-11-27 00:30:12"}, {"kompetensi_dasar_id": "391af348-4669-4c33-a73d-0b687ac3cd99", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur per<PERSON>an upah untuk pekerjaan konstruksi bangunan gedung.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:29", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:29"}, {"kompetensi_dasar_id": "391bc5ea-236e-4c8f-b62f-bdc6fe0a167a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800060700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat alat peraga.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "391bd345-230b-4c2f-a148-de3724166108", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 814130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>i est<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "391c9387-8c24-4e9c-94b6-f9826f465156", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:42"}, {"kompetensi_dasar_id": "391dce00-34e8-4b57-8a8e-dffaee2c1aa9", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "391fad27-6cb9-48f7-bce6-20cc96867b60", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis standard ISO mengenai tata letak gambar dan layout kertas gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "391fce38-398a-4c19-ba54-f9c88aa002a5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemograman  mesin bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan teknik pemograman  mesin bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "391fd616-e327-405e-80e0-aa37cace1032", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 820090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja <PERSON> Converter (TC)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "3921882e-e24a-4dbb-898c-ca7ea2c1016a", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 825063100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan perencanaan kebutuhan semen beku ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:18", "updated_at": "2019-11-27 00:28:18", "deleted_at": null, "last_sync": "2019-11-27 00:28:18"}, {"kompetensi_dasar_id": "3921a1a7-530a-4444-beca-573e2b801534", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802030300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan pengelolaan informasi digital melalui pemanfaatan perangkat lunak pengolah informasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "3921cbcd-9949-414f-9c1a-7a6e54377e5f", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "392255ec-78d3-4e0b-bd98-c9cc050334eb", "id_kompetensi": "3.38", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis desain turbin dan kelengkapan mekanik pico hydro", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "3922bdce-f59a-4aa9-be7c-d399ab6dce84", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827350600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part part C. Rule 20 - 31", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "3922f2bc-2c5b-42a0-8de1-b70bcafb6da8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804080200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara Melakukan perawatan dan perbaikan <PERSON><PERSON> /<PERSON><PERSON>p jenis mesin gergaji pita, mesin bor horizontal, mesin shaper (spindle moulder), mesin over head router, dimension saw, multy boring mashine, hollow chisel morticer, chain morticer, mesin bubut kayu, dan special attachments", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "3922fd19-5491-40ca-adb9-19149dd0244c", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 817020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami istilah cadangan migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "3923c051-ab27-41f3-9cc8-bbed959<PERSON><PERSON>c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "3923f8de-2258-46bf-81d3-23c5e064cb70", "id_kompetensi": "3.36", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penyutradaraan dengan pertimbangan (type of shot, camera angle, composition, cutting, continuity)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "3925d4bf-d760-4546-955b-3825cbd009af", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:04"}, {"kompetensi_dasar_id": "3927290a-012e-4569-bb9d-cc54dc7bf822", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843063000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>n   ragam gerakan tari daerah lain bagian tengah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "39273aae-c8bb-4414-bf52-7df1384930bc", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:10"}, {"kompetensi_dasar_id": "39275def-07fd-4c68-b474-45b0210cb62c", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:10"}, {"kompetensi_dasar_id": "39289ffc-39bf-4094-9b89-147548ecc34d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 801031500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melaksanakan teknik pekerjaan sosial dalam memberikan layanan kepada klien", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "39293b0c-431d-4563-ae0c-64fea49206f0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 801030700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan bimbingan konseling pada lanjut usia mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "3929f819-68c9-4ae1-8388-a69374d9bc94", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 822080110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan perbedaan sistem sesuai perkembangan Teknologi Engine Management System", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "392ac76c-eeb7-4536-bac3-57e60d07a989", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor-faktor kema<PERSON>an dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:39"}, {"kompetensi_dasar_id": "392bbf06-a7dc-49fd-be3c-e2ccc2193dbf", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 808060620, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan konsep/deskripsi material komposit sesuai dengan SOP", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "392be273-375b-46e0-928b-4e9de48bdc4e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menentukan sumber-sumber air irigasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "392c6fae-7ac7-4d0f-8326-376fb0a4849b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 843070500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalis<PERSON> per<PERSON>han pola ketukan (tanda sukat) penulisan notasi vokal tembang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:02", "updated_at": "2019-11-27 00:29:02", "deleted_at": null, "last_sync": "2019-11-27 00:29:02"}, {"kompetensi_dasar_id": "392c9890-129c-42f2-9629-800ba83d32f4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:57", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:59:57"}, {"kompetensi_dasar_id": "392d349b-07c7-4047-a418-60a3c3896e87", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "392f4b1d-e6fd-4eb8-893b-c3d3a5f85319", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:18"}, {"kompetensi_dasar_id": "392ff262-077b-445d-a95e-c5421d175ca4", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprogram sistem kontrol berbasis PLC menggunakan sistem komunikasi data", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "3930466f-36f8-4fd5-b406-770d4ea39755", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820030700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan DC power supply", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:18", "updated_at": "2019-11-27 00:27:18", "deleted_at": null, "last_sync": "2019-11-27 00:27:18"}, {"kompetensi_dasar_id": "3931fbff-98f5-41f0-b254-8bb0f686977b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827210400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi biaya produksi pengembangbiakan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "393244a5-6ed0-4584-a3a0-ab8566f69350", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami berbagai peraturan perundangan serta konsekuensi hukum bagi para pengguna dan pengedar NARKOBA dan psikotropika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:13"}, {"kompetensi_dasar_id": "39339d14-f4f5-46d9-930b-724b358b48c0", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 829040400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesk<PERSON><PERSON><PERSON> bahan minuman (kopi, teh, coklat)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2019-06-15 15:07:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "3933b878-3d2f-4ab4-a0ef-09db470c8ad1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan cara mengukur tahanan isolasi instalasi penerangan pada bangunan sederhana (Rumah<PERSON>, Sekolah, Rumah, Ibadah)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "3933f164-b965-4100-997b-04cfb06c982e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pem<PERSON>n kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:12"}, {"kompetensi_dasar_id": "39349537-55d0-4a9a-85d4-ceed92410310", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 801031100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan interaksi secara efektif dengan anak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-11-27 00:30:26", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "3934d7a2-e494-4f1e-a945-ee1c5a69f39a", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 820070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kinerja pada sistem transmisi manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:21", "updated_at": "2019-11-27 00:27:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:21"}, {"kompetensi_dasar_id": "39353637-f4e0-42ec-87f8-40a9ae15e256", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 830100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan berbagai usaha dibidang kecantikan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "39354fa5-a268-4428-ab0d-f0f7fb6eb860", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON> hasil pengujian bahan pakan dan pakan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:08", "updated_at": "2019-11-27 00:28:08", "deleted_at": null, "last_sync": "2019-11-27 00:28:08"}, {"kompetensi_dasar_id": "3935e284-e2eb-4b15-9a72-85977977cd35", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 843061200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kreativitas membuat aransemen lagu dengan menambahkan instrumen ritmis, non ritmis dalam sajian mandiri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2019-06-15 15:07:31", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "3935f251-e428-4c4f-9cfd-dc27345085ca", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis KCKT", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "39378acf-0e80-4660-9a10-22af1bb3ca25", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 802032400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:38", "updated_at": "2019-11-27 00:28:38", "deleted_at": null, "last_sync": "2019-11-27 00:28:38"}, {"kompetensi_dasar_id": "39379d35-07e2-4768-807f-72789b88e684", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 839080100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan produk teknik jahit perca pola geometris", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "3937f112-ab5e-4e88-a121-ba9e8ad85151", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:20"}, {"kompetensi_dasar_id": "39381e80-f111-4bcf-bf85-ad0472d4a1b4", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 826110100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Meggunakan teknik konservasi tanahdan air dengan metode kimia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "39399549-cc3a-40a5-8284-27c9b12b5518", "id_kompetensi": "4.37", "kompetensi_id": 2, "mata_pelajaran_id": 820030400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginspeksi Sistem Udara Start dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "3939cbc3-db69-47e1-bf26-315241a485a6", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:55", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:02:55"}, {"kompetensi_dasar_id": "3939cf9a-9135-45f8-bb08-6363c6e15c83", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 801031300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Memberikan solusi bentuk layanan rehabilitasi sosial penyandang disabilitas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "393a0fd0-b72b-49f5-ad6d-8461252ed770", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843061000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bentuk dan struktur dasar garap gending", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:02", "updated_at": "2019-11-27 00:29:02", "deleted_at": null, "last_sync": "2019-11-27 00:29:02"}, {"kompetensi_dasar_id": "393ad4a4-9770-4337-8e80-c06c3507e85a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an beberapa teks recount lisan dan tulis dengan memberi dan meminta informasi terkait peristiwa/pengalaman sesuai dengan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "393c2848-55e8-4f42-8b81-1639e307877b", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401141200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sediaan infusa dan/atau aqua aromatika", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "393cb5d1-4a06-4dc3-8929-7de9cfb2f3a2", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi cara menghindari tindakan kor<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:01:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:01:19"}, {"kompetensi_dasar_id": "393d52ed-d29e-471f-81dc-061282c7f71d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827350700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Use  nautical almanac", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "393e1ed9-9801-4c86-b9c6-72fce98a659c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencana sistem akustik ruang kecil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "393effda-26a5-4ca5-b01b-ed2880601c35", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 830030100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan kelainan postur tubuh dan jenis lemak dalam tubuh manusia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "393f0502-509d-41c9-b111-c082d60f8dd5", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> narasi / voice over", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "393f34df-0b66-4890-ab54-10fa6fed0131", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penanaman tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "393f9741-1094-4049-bfda-416333d0287d", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk menyatakan dan menanyakan tentang pengandaian diikuti perintah/saran, dengan memperhatikan fungsisosial, struktu rteks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:15"}, {"kompetensi_dasar_id": "393fb027-ef19-4be1-b9f0-39c0247c3889", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemesinan bubut  kompleks untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:53", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:08:53"}, {"kompetensi_dasar_id": "393fd4c7-4163-4b2a-afd3-32c9ea107695", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827390700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Use Drilling machines", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "393fdc15-2bf5-453e-aedd-9f6b67f390c6", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807020500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan komponen pasif sesuai dengan karakteristiknya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "39409440-4f93-443f-a28c-4f72d4d2abe7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804131510, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "3945d754-a7f6-4a38-a2d8-2400860e0baa", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 804100810, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jumlah bahan, tata letak dan biaya pada instalasi Penerangan 3fasa untuk kolam renang sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "394669ac-500f-4218-8aa2-aba25305c3a3", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 820110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pemasangan perangkat tambahan pada bodi kendaraan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "394826e9-3931-46a5-83bc-f88aca7beb82", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep ruang sampel dan menentukan peluang suatu kejadian dalam suatu percobaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:53"}, {"kompetensi_dasar_id": "39483f88-9edd-4712-8d50-a95c4988c27e", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "394a2f6c-de9f-4c2d-a42c-129c110c2dee", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804140200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan fungsi peralatan pengecoran", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "394bcaa7-2188-4fb8-a54d-73352c470259", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:57"}, {"kompetensi_dasar_id": "394c06a4-2395-41c1-8ebb-eebfbadb50a7", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 803071200, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan karakteristik televisi berbasis internet protocol (IPTV)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "394cff2c-11d4-4f6e-8e47-d7a741c3ad38", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 827390900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate  routine pumping operations", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "394f2538-14bd-4ff9-ba47-327812c7780b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (K3LH) dalam kegiatan laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "394f2893-be9a-4c3a-b27f-e09d4149f420", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:18"}, {"kompetensi_dasar_id": "394f5ee4-71e8-4735-86dc-6482e2a068cd", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis komunikasi data serial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "3951167b-1df7-4d95-a2ad-0d8d5b70509a", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825061200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan inseminasi buatan pada unggas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:08", "updated_at": "2019-11-27 00:28:08", "deleted_at": null, "last_sync": "2019-11-27 00:28:08"}, {"kompetensi_dasar_id": "39518601-906b-45bc-87d0-8d57d0413808", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 841010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan finishing per<PERSON><PERSON> berbatu permata ikatan bezel dengan teknik poles", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "39527562-d250-4492-a219-68652d9d4414", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 803090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan komunikasi mikrokontroler dengan komputer (interfacing).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "39533acc-ac75-437d-9989-0a76e0141456", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penyambungan logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "3954486e-fbcd-4280-a226-e37913e21416", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:16"}, {"kompetensi_dasar_id": "3954a32d-f898-45a1-bca5-d271e0db6deb", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan Teorema Fundamental Kalkulus untuk menemukan hubungan antara integral dalam integral tentu dan dalam integral tak tentu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:23", "updated_at": "2022-10-19 23:19:15", "deleted_at": null, "last_sync": "2019-06-15 15:31:23"}, {"kompetensi_dasar_id": "3954ac2d-03b2-48a4-b13a-5c464e9581b7", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur per<PERSON>an RAB pada pekerjaan perawatan bangunan gedung yang tergolong rehabilitasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:34:01", "updated_at": "2019-06-15 15:34:01", "deleted_at": null, "last_sync": "2019-06-15 15:34:01"}, {"kompetensi_dasar_id": "3956416b-1a0d-4b3e-983c-0113e43487f1", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 830050040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengetahuan bedah plastik pada hidung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "395702d2-dafe-4c83-9f1d-825dfbe48996", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "395751c1-0a9f-4fbd-a874-183979b885c4", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821090800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>ungan sederhana pada bantalan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "3958c3a1-89b4-437f-8e56-593993732c7b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804110200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan konstruksi motor bakar 2 langkah dan 4 langkah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2019-06-15 14:49:59", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "395a7b56-f604-457f-98ab-ada7a7e02bb5", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis rancangan aplikasi PLTS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "395b7f52-01c4-4a48-8728-7b18d7e99057", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:32", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:32"}, {"kompetensi_dasar_id": "395d2684-c44e-4d09-be67-559c4f3c61c8", "id_kompetensi": "4.29", "kompetensi_id": 2, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan overhaul   sistem bahan bakar diesel pompa injeksi In-Line", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "395e83d6-f68e-447f-a4b9-de397211ac5d", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 825230100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengevaluasi kegiatan pelaksanaan unit produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "395e8b10-0bb8-4b05-a982-736828e8801f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur keselamatan dan kesehatan kerja serta lingkungan hidup K3LH  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:05", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:05"}, {"kompetensi_dasar_id": "396016c8-4f17-454d-902b-ac4bf978e608", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat produk kimia industri skala kecil misalnya sabun mandi/deterjen/parfum/sampo mobil/tinta/semir ban/ semir sepatu/etanol/air minum dan lain-lain sesuai dengan hasil analisis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "396297e6-bb03-44ed-9263-3f502f1e25ef", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan program siaran musik sesuai segmentasi pendengar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "3963cf72-9a4c-440b-a6b4-f5a6f81dfbb9", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 801031400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is masalah sosial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2019-06-15 15:03:12", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "3963f371-df26-4f36-93ce-79b8119673fa", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:16"}, {"kompetensi_dasar_id": "3964034c-23a5-41f0-8097-a97bed192b72", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis din<PERSON><PERSON> pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945 dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:17"}, {"kompetensi_dasar_id": "39652a54-f4f4-4fab-9004-c6db0f5cbc87", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "396589c5-b765-457a-8113-a23a81e19a35", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengoperasian peralatan absorpsi mengikuti SOP.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "39659bb9-4c37-4c6b-bbd0-e9cfd94bfbef", "id_kompetensi": "Proses bisnis bidang otomotif secara menyeluruh ", "kompetensi_id": 3, "mata_pelajaran_id": 800000107, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami proses bisnis bidang otomotif secara menyeluruh pada berbagai jenis dan merk kendaraan, serta pengelolaan sumber daya manusia dengan memperhatikan potensi dan kearifan lokal.  ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:18", "updated_at": "2022-11-10 19:56:55", "deleted_at": null, "last_sync": "2022-11-10 19:56:55"}, {"kompetensi_dasar_id": "39666694-b140-4f04-b0bb-bedbb8613833", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843062200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan evaluasi karya seni karawitan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "39673112-fc60-4c46-bd7d-e41aff04b4fb", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807021610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komponen pasif berdasarkan karakteristik kelistrikannya (arus searah, arus bolak-balik)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2019-11-27 00:29:57", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "3968ad45-2073-4ac8-a7c7-a295f10a5fe3", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 821070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Teknologi pembangunan kapal baru", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "396974cd-3731-4787-9c3a-391bb7051ba4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 828090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Mengan<PERSON><PERSON> per<PERSON> kantor (office furniture)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "396a8e01-eefe-4ce3-b0de-123b80fec241", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 825100500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prosedur kalibrasi alat mesin ekstruder", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "396aecc5-3aff-47d5-a6a6-7ae2042b4e4f", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi kerja baterai", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:25"}, {"kompetensi_dasar_id": "396b47c0-99f4-4136-8bd6-9b379684d877", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401260000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hambatan dari keberagaman budaya dalam masyarakat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "396bd1e9-9b7b-490d-a39a-d31656643310", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 827210300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, da<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:51", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:51"}, {"kompetensi_dasar_id": "396c03ef-9fde-4992-a1a7-c86fe697c3d4", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> cross shot <PERSON><PERSON> rang<PERSON> gambar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "396c5a95-35b5-422f-af77-d7b883ff80e3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran Indonesia dalam hubungan Internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:50"}, {"kompetensi_dasar_id": "396c8e96-517d-4fb8-8042-b346ab89ddd8", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401251104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Mengelompokan ciriciri ekonomi Islam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "396f91df-8088-42bc-9d24-2f301ea20209", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal dan menya<PERSON>kan<PERSON> dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:21", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:04:21"}, {"kompetensi_dasar_id": "396fa547-7594-441c-80ea-e4f62df3e935", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis yang melibatkan tindakan member dan meminta informasi terkait perbandingan kata sifat dengan memperhatikan fungsi social, struktur teks dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:11:52", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:11:55"}, {"kompetensi_dasar_id": "39703514-e7da-4ee9-af84-a7080eecbb40", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 830060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan penataan rambut artistik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:33", "updated_at": "2019-11-27 00:30:33", "deleted_at": null, "last_sync": "2019-11-27 00:30:33"}, {"kompetensi_dasar_id": "3970f08b-5817-43a8-b1bd-a1e66b68acc4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:10"}, {"kompetensi_dasar_id": "3971267f-9b20-4e0e-a425-ac167d49681b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan evaluasi benda tuang sesuai gambar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "39731fb4-1cb7-47a5-bc07-ae70e33250d3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Alat Pemadam Api Ringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:51"}, {"kompetensi_dasar_id": "3973329e-fe3e-4c04-9cc8-c5b6238d7dda", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 802040800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengulas bisnis siaran radio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "397402ee-52e9-492c-9aaa-591ec1710f07", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:16", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:16"}, {"kompetensi_dasar_id": "397689d5-1600-49c0-aebb-c7af9a7422fd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> analisis penanganan kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:35"}, {"kompetensi_dasar_id": "3976ed07-607b-44ab-84ce-74935cc38af1", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan tata cara pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:19", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:19"}, {"kompetensi_dasar_id": "3977a14b-03d9-4817-bf33-2ebffc4459b9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843060620, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik tata rias karakter teater konvensional Asia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-10-19 23:19:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "3978200d-cb65-43ee-b3f0-000156cfc741", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "3978d24f-593c-440d-9fca-e2b880c89651", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:39"}, {"kompetensi_dasar_id": "397b4546-14c4-44f6-88aa-5a769dd6113e", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:52"}, {"kompetensi_dasar_id": "397c5751-f3b0-4b89-bad6-0186fc0b2523", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 820030400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dan mengevaluasi Overhead Crane", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "397c7c90-f74a-4c31-9add-e3e3e0ac178b", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825070110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Merawat motor listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "397cd7a7-7ebe-4cad-bd55-7e29ada7cb7f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827391000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply  <PERSON>’s law", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "397de16f-f371-40c3-a9b4-bf1d65739cbf", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804101100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pekerjaan elektromekanik dari bahan non logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "398031b2-3049-4daa-bb55-b50eaf3f7a2f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827210200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kualitas air pada media budidaya komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "39811a4e-a74c-4a34-be38-eaf707c5b128", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan sifat turunan fungsi terkaitdan menerapkannya untuk menentukan titik stasioner (titik maximum, titik minimum dan titik belok).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:23", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:23"}, {"kompetensi_dasar_id": "398257e9-6051-4e1f-ae5e-4a19ed7b2edf", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menempatkan (memasang) dan menggunakan mesin potong otomatis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "3982760b-f7bb-4077-bc33-26cd5cfbfb38", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:15"}, {"kompetensi_dasar_id": "3983522d-43ad-4a24-93b7-7ccbb6782409", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis titrimetri sederhana", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:09"}, {"kompetensi_dasar_id": "39855bb9-9c71-4e63-9468-e7c9545163b0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 802021200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami<PERSON><PERSON> keamanan jaringan yang dibutuhkan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "3986df91-0918-4b48-a38b-bfa688be19a8", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usahadengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:18", "updated_at": "2022-10-19 23:19:18", "deleted_at": null, "last_sync": "2019-06-15 16:08:18"}, {"kompetensi_dasar_id": "39874c4d-2596-4f8a-9c14-517dc8259629", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 816010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses pengelosan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "3987c9f6-e79c-4305-9a36-96bcb7105087", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:18"}, {"kompetensi_dasar_id": "3988281d-009f-415e-a12e-6f866a36038c", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 804100700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>ara lighting arrester", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:02:51", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:02:51"}, {"kompetensi_dasar_id": "3988b1af-25ca-44c6-ad41-983faafffef6", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "39894360-87e1-4084-8ddd-04fe890ece57", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 803081000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit sistem kontrol pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "3989dce8-4d47-495f-9ed1-74acf377b1e6", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:03", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:03"}, {"kompetensi_dasar_id": "398a3727-d247-413d-afc1-83a4cc3edafc", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:05"}, {"kompetensi_dasar_id": "398b54f0-9e27-49ba-930c-7a156aff6311", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:31", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:31"}, {"kompetensi_dasar_id": "398cb713-6dfd-4d34-8977-25d1425dd491", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 825062400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis fisiologi reproduksi hewan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "398cbdfb-5a46-4655-8b9c-cdd40c996389", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 828150100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memberi contoh persyaratan pekerja bidang perhotelan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "39910cc5-0f25-422e-b54e-234c7f626256", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "39927f6d-d78f-4efc-ab51-fbdd92b7b935", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401251300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan strategi bauran pema<PERSON> (marketing mix)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "39939d6b-3d62-4c3a-8009-6ff4b9fa08bd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 815010800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki hasil produksi mesin Ring Spinning", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "39941342-e622-4657-95f2-b0f780429b9f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan kriteria penggambaran struktur jembatan sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "39941e5e-b2ce-466e-81aa-4c11ddc7445b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800080200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan tentang resistensi, immuniti dan bakteriophag", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 15:06:48", "deleted_at": null, "last_sync": "2019-06-15 15:06:48"}, {"kompetensi_dasar_id": "39961083-4b21-4cb2-8a02-044cd7fb6f9a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> analisis penanganan kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:17"}, {"kompetensi_dasar_id": "39967b5d-0a04-4bc6-b641-16a57dd6c223", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 807022600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji program G Codes incremental benda kerja di mesin frais CNC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "3996f269-14b2-4b61-9eee-2d8a2d39eef1", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825210500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dan melaksanakan pengawasan dan pengendalian mutu selama proses.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "3997962e-2f30-4d49-8098-fc27d8eb9d92", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 804100810, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar <PERSON>leng<PERSON>pan Hu<PERSON>ng Bagi (PHB) Penerangan 3fasa untuk lapangan olah raga sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "39980e9b-b4eb-450e-ab55-b99b8a0f1470", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 804051200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan finishing kayu dengan bahan pelapis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "399845da-8450-4e70-a31b-2441c7311a87", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 802010700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengkodean alur program dalam aplikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "3999481f-1374-4527-8e81-da940c2556d1", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 809021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON>h alat dan bahan cetak offset gulungan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "39997c4b-82b9-4e9a-b52d-f7e86cfeb531", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 826080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang alat bantu untuk mengidentifikasi tumbuhan dan satwaliar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "39999f84-ae56-443e-b01f-a8270b154ef7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:08"}, {"kompetensi_dasar_id": "399a96df-208f-4fe8-8c86-f8a5d017cedd", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, konsep dan prosedur khr<PERSON><PERSON><PERSON><PERSON> (TLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:27", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:27:27"}, {"kompetensi_dasar_id": "399ac195-a5c4-497c-a895-51e7116cad59", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:35", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:35"}, {"kompetensi_dasar_id": "399b2880-e4f5-411e-8409-ab70a4da936f", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 839010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan  kesehatan kerja meliputi persyaratan ruang kerja dan penyakit akibat kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "399bfa54-b1bc-4f3a-8dcd-a16184e8fcc7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:19"}, {"kompetensi_dasar_id": "399cb4b3-354b-498d-91c1-f4a958973531", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802030310, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang dokumen tahap pra-produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "399cd8a3-0231-49be-accc-5e89f5c08922", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Memahami peraturan tentang tenaga kesehatan, pek<PERSON><PERSON><PERSON> kefarmasian dan unit pelayanan kefarmasian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "399ce1da-4c6c-4f48-9ff2-2eeffc77bc84", "id_kompetensi": "1.3.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> bahan, media dan teknik yang  digunakan dalam  berkarya seni rupa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2019-06-15 14:49:54", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "399d32a1-68b2-465e-bf36-86c6b16b8887", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:00", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:00"}, {"kompetensi_dasar_id": "399d5c60-ffd7-4b53-bf10-ef348fbeb005", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 816030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>, konsep dan prosedur proses pen<PERSON><PERSON><PERSON> ben<PERSON>, pengendalian parameter proses, perawatan mesin dan pengujian ketahanan luntur warna serta penerapan konsep K3LH.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "399eed8c-93a8-43a2-896b-9f457ee37c29", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:17"}, {"kompetensi_dasar_id": "399f2068-1897-47d1-bf28-4463875924bd", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prinsip dasar permesinan kapal perikanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "399f3d70-21de-4d75-9c91-f8e655b6172b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "399f7340-2790-4e24-b811-4925c8bd7ac0", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 828190100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun materi pemanduan wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "39a1587b-0e55-4d67-a3fe-e25b3e60ca0e", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 800030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemusnahan barang kadalu<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "39a22c69-3862-4613-b22d-0a59a9fe21bb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 814160200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang pengolahan limbah cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:16", "updated_at": "2019-11-27 00:29:16", "deleted_at": null, "last_sync": "2019-11-27 00:29:16"}, {"kompetensi_dasar_id": "39a38aab-771b-4098-8b3b-3275227e190e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "39a3c2fa-9e99-4389-8834-7047433c4e8d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jenis-jenis garis pada gambar teknik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "39a424e7-e82f-42fd-b608-709c7157945e", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>, <PERSON><PERSON><PERSON>, per<PERSON><PERSON><PERSON><PERSON> dan penutup dalam teks negosiasi berkaitan dengan bidang pekerjaan secara lisan atau tulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:05"}, {"kompetensi_dasar_id": "39a44efe-69b0-4bb3-8daa-19713028a204", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola besar dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:36"}, {"kompetensi_dasar_id": "39a6c8b3-3933-4f7d-b010-b9ac865de024", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 802021200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil analisis fungsi dan tatacara pengamanan server-server layanan pada jaringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "39a76c96-98a2-4ee2-b9fc-e761b2cbc4e6", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:52", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 14:49:52"}, {"kompetensi_dasar_id": "39a801cd-18d2-492b-9217-8d2a38c5702c", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "39a816bb-b583-4d7e-bc7f-e47dd4246975", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:09", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:28:09"}, {"kompetensi_dasar_id": "39a83939-1d6f-4aba-a04d-549f96470d33", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan karakteristik Turboprop, TurboShaft, turbojet, turbofan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "39a83a53-c9ec-487d-87a1-6a1bb19b1de7", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 828060100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       <PERSON><PERSON><PERSON> norma, standar, prosedur dan ka<PERSON>h k<PERSON>pan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "39a896a8-8725-488e-b32f-453a2d4e03bc", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:01", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:01"}, {"kompetensi_dasar_id": "39ab0720-817b-4eee-8662-404d1e94d381", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827360200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "explains the arrangement of the code into : single-letter signals, two-letter signals for the general section, three-letter signals beginning with ‘M’ for the medical section", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "39ab309f-da1f-4da8-8ce3-0069db52e378", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:05", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:05"}, {"kompetensi_dasar_id": "39ac1399-cd20-4676-838a-ec2495d4ec34", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan bagian dari \r\nproses dan memilih alat bantu \r\nuntuk menghasilkan \r\nkomponen yang spesifik sesuai \r\ngambar kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:17"}, {"kompetensi_dasar_id": "39acadbb-60f3-48f8-a852-8d6f1a97ae4f", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 401251060, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pengisian SPT Masa PPN dan PPnBM dalam Formulir 1111", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2019-11-27 00:29:58", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "39acd103-661b-4f5c-998c-2bb6aa4f716c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "39acedb5-feb4-4987-9ded-3a7fa6e3bd75", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:34", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:05:34"}, {"kompetensi_dasar_id": "39ad7138-a9f3-4b67-a6b9-57223ab11c1c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820140100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pen<PERSON> k<PERSON>aan da<PERSON>rat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "39ade203-e8cd-4b9f-874c-2da6902db28b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 827351200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate E-Loran (menunjukan E-Loran )", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "39aec22f-de1e-4460-bbca-64c309c6535e", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 804050480, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan dan pengecekan alat jenis optik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2019-11-27 00:27:39", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "39af2ffe-834d-4e5f-853c-50d7a56a9111", "id_kompetensi": "<PERSON>car<PERSON>", "kompetensi_id": 3, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Pada fase ini peserta didik dapat mengan<PERSON>, mengidentifikasi dan membuat kreatifitas\nbentuk yajna\ndalam\nMahabharata\ndan yogacara\nsesuai dengan\nkearifan lokal\nkaitannya\ndengan nilainilai budaya\nbangsa dan\nkebangsaan.\nHal ini\ndilakukan\nuntuk\nmelestarikan\nbudaya daerah\ndan penerapan\nnilai\nkeagamaan\nHindu di\nNusantara.\nSerta\nmewujudkan tri\nkerukunan\numat\nberagama agar\ntercipta\nkehidupan\nharmonis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:46", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "39b08b58-fca2-4f4a-92b7-a4ce8dd65da7", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 822190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil pemeliharaan instalasi Electronic over load & over current protection", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "39b2483a-34d6-48ad-be80-b7bc0f6d5108", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:18", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:18"}, {"kompetensi_dasar_id": "39b2534a-0301-41cb-9b1e-fa3b152bf0dc", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825020600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemeli<PERSON>an kesuburan tanah tanaman perkebunan semusim", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "39b4595b-8d85-4d25-840b-5020a1a4823f", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 401141400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis reaksi redoks dan elektrokimia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "39b48c74-a6a7-4db5-ba08-cfe58457fa08", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:57"}, {"kompetensi_dasar_id": "39b53653-43b9-4b76-ab2b-d76bf9677923", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pemeliharaan anak ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "39b6b14e-c285-466f-b0da-681e8d3a0e0b", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 828041100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan pengoperasian aplikasi pengolah presentasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "39b82cdb-6a4d-491b-beb8-fc8c02111d6c", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 825021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menunjukkan teknik pengendalian ruang pertumbuhan kultur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "39b88a14-c027-4057-a698-d928008de6d2", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "39b8d331-d36b-450a-a41b-7fc4391be7fd", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengendali motor DC Brushed dan <PERSON>, untuk pembalikan arah putaran dan pembatasan arus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "39b8f7b3-e63f-4a5b-84f8-9fba7638e0cc", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "39baf142-d419-41c8-9e9c-0f04d6e6877b", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 814031000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki tingkat kesadahan air untuk proses penyempurnaantekstil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "39bedf57-5bc4-4df9-8b0c-bc31f3d3467a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802030310, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan logika dan algoritma komputer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "39bfe957-fbd1-46bf-a4aa-e7e43b90bede", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 814070200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah administrasi penyimpanan barang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:38", "updated_at": "2019-11-27 00:29:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:38"}, {"kompetensi_dasar_id": "39c0bcd7-db90-4b0a-ac60-0d88cd71271d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:20", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:20"}, {"kompetensi_dasar_id": "39c195ec-6162-4381-90ae-98ac95e56321", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:15"}, {"kompetensi_dasar_id": "39c341ee-413a-4ddf-8717-52fcaf057eff", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 837030100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat maket interior apartemen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:41", "updated_at": "2019-11-27 00:28:41", "deleted_at": null, "last_sync": "2019-11-27 00:28:41"}, {"kompetensi_dasar_id": "39c3eded-a2cd-463f-9a48-07f9eb14dd79", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:23", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:23"}, {"kompetensi_dasar_id": "39c41e72-bb08-4d55-9564-eb3f07be5d9c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827090110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perhitungan cutting rate", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "39c92507-4492-41d8-9b75-0ee6c758b504", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat dan karakteristik limbah dalam penanganan limbah B3 dan non B3", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:56:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:56:59"}, {"kompetensi_dasar_id": "39c93b98-0e92-4f7e-94c7-e52c2d33fa60", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 804132500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikanfungsi-fungsiperintahdalamperangkatlunak CAM pemesinan bubut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "39c98eb9-77be-495e-93ec-49086aa6d7d9", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data dari situasi nyata, memi<PERSON>h variabel dan mengkomunikasikannya dalam bentuk model matematika berupa persa<PERSON> fungsi, serta menerapkan konsep dan sifat turunan fungsi dalam memecahkan masalah maximum dan minimum.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:49"}, {"kompetensi_dasar_id": "39cb2a3a-d09a-4165-8714-ed10d67ebf2b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode pencarian kerus<PERSON>n, perbaikan & perawatan macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "39cb65ed-25f0-488c-a5d5-bd538e2d8dd6", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 800020410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan pemeriksaan penyakit sistem indera berdasarkan manifestasi klinis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "39cc176d-0212-46ce-8a5f-bf08659a50ed", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 842040300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik men<PERSON>kan kayu secara alami", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "39cd177e-719f-4b49-a48b-32f7480cf029", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821090710, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membentuk pipa dengan peralatan kerja pipa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "39cf4d2d-d86c-4e69-b545-aab9b85da850", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> sing<PERSON><PERSON> suara akhir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "39cf5055-82fe-4674-853b-27ab9dbb98c8", "id_kompetensi": "4.53", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang rang<PERSON>an listrik tiga phasa tegangan rendah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "39cf7971-8eae-4976-a1ad-3dd9cc0321b1", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 828140100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> jenis-jeni<PERSON> , struktur dan perkembangbiakan mikroorganisme di bidang makanan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "39cf79cf-f2be-4235-981a-0ae911ea9e11", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825010220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Membuat gambar di atas permukan benda kerja logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "39cfa914-e569-4310-aba8-f70ef1eace71", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821020100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan alat pencegah pencemaran dilaut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "39d156d7-db47-4cac-8fa2-a25a9372cebe", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:49", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:49"}, {"kompetensi_dasar_id": "39d274ea-0df8-46ed-8dac-92b454c7326c", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401131600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis kadar le<PERSON>k", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "39d2c2e6-c128-4f72-a23f-847730acbec8", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur <PERSON>an RAB pada pekerjaan instalasi pipa air panas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "39d2f40a-5c81-401c-b8bc-51bc54cb1283", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jamur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "39d3c2bf-5e9b-4047-bc8a-181216a4bd06", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pela<PERSON> k<PERSON>, tab<PERSON><PERSON>, dan dakwah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "39d407bf-9c90-4cbd-8caa-82c45cd956a8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 807020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan daily inspection", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "39d47e9b-1cb7-45f4-b453-635f58d3327a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 839020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat nirmana datar dan nirmana ruang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "39d49d49-ff6d-46ba-acd8-fe21e17ee3e9", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengartikulasi aplikasi Vibration sensor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "39d55d95-2a87-48a6-babd-edd15cb0bafd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate actions on stranding/grounding", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "39d63f8c-4669-4f43-ab62-ab74766886d8", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam special service tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "39d641a5-8cd8-4fc7-9390-61a20e850731", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401141500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca resep dan salinan resep", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "39d79822-dce2-4823-962d-f6658fc74c8f", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung neraca masa pada proses steady state dan non steady state pada berbagai unit operasi teknik kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "39d80736-f3cd-4b13-9e92-c06eca378a56", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 843060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gerak tari tradisi putra berdasarkan karakter tarian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "39d87f16-37cc-4bb3-a928-d575e734d794", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "39dad66b-773b-4ec3-bec2-c513456a9a52", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip-prinsip teknik pengukuran tanah.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:50:16"}, {"kompetensi_dasar_id": "39daf970-7086-42e2-93c8-6b92c75ff22f", "id_kompetensi": "3.95", "kompetensi_id": 1, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami prinsip dasar metode pencarian kesalahan pada gerbang dasar rangkaian elektronika digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "39dbc25f-58e4-46db-bb22-a529448c03fa", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802032700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar obyek 3 dimensi dengan perangkat lunak secara tepat dan efektif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:54", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:03:54"}, {"kompetensi_dasar_id": "39dcb609-f95e-49a8-a290-4f07116484d8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 843060610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>dakan tata rias dan busana tari putra dengan tari putri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:29", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "39dcc145-01c1-4387-9872-d8e77a4180d7", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827391100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain automatic control devices (process control and system control)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "39de0476-f694-4620-8a5b-f448c0c9f44b", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 803081400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan jenis jenis loop sistem pengendalian kontrol <PERSON>s", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "39e02a7b-17ae-44ca-89a1-cc498f400ef1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:29"}, {"kompetensi_dasar_id": "39e0f2de-b43b-4c6f-8433-7e8c1338addb", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyelesaikan masalah yang berkaitan dengan ukuran pemusatan data tunggal dan data kelompok", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:07", "updated_at": "2019-11-27 00:30:07", "deleted_at": null, "last_sync": "2019-11-27 00:30:07"}, {"kompetensi_dasar_id": "39e12c15-9800-42fe-a092-d7c0ad9e4b1d", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 820130200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pemasangan body kit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "39e159b2-d565-499c-9c45-72054b34e8a0", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843110300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ingatan peristiwa dan emosi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "39e21f72-9577-4ec7-b67c-90ef95d3f5fa", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "39e229d6-b36a-4aba-8029-e10dc95e1538", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengoperasian dan perawatan sederhana dalam proses <PERSON>inan dan pembekuan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:29"}, {"kompetensi_dasar_id": "39e3a252-c254-4fd8-84e7-9794154f5b3d", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 800070200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemeriksaan laboratorium berdasarkan penyakit sistem kulit (integument)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2019-06-15 14:59:28", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "39e3c25b-5f2e-4dab-8c4b-51c061795974", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menciptakan formulasi pakan ternak unggas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "39e556cc-3827-40a2-90be-b9e373077e89", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan mempred<PERSON> ", "kompetensi_id": 3, "mata_pelajaran_id": 401901000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>, peserta didik dapat mengajukan pertanyaan lebih lanjut untuk memperjelas hasil pengamatan dan membuat prediksi tentang penyelidikan ilmiah. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:25", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "39e593d9-778b-49eb-a0ae-889a458c6265", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 401251190, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengan<PERSON><PERSON> pen<PERSON> neraca saldo", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "39e60293-c626-4481-9f06-00d1928c22c5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "39e7fddb-a92b-4d16-98b0-2f731f511c08", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 829080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Menganalisis hidangan dari telur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "39e82a73-6805-4a9b-b4ad-804bb0060aa8", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825250600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memb<PERSON>t bak/kolam terpal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "39e837cc-9fc1-4f4e-8f11-5e67cbd596ce", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 401251103, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pengertian dan konsep syirkah mudharabah  muqayyadah on balance sheet", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2019-06-15 15:07:16", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "39e85aab-022c-4456-af65-06bf45176c79", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 828150102, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis jenis-jenis bahan pembersih dan bahan saniter dan menemukan bahan penggantinya (alami )", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "39e8d41f-006d-43c2-b3b6-f46964ede3dc", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami makna keterlibatan aktif  umat Katolik dalam membangun bangsa dan negara Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "39e91fcc-720a-467d-b3c5-a09e9af74230", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 804011000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar papan rangkaian tercetak (PRT) lapis tunggal (single layer) secara manual berdasarkan diagram rangkaian pada kertas kalkir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "39ea3e2e-606f-4ccb-90b2-24462204ba8f", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 802040900, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan naskah audio visual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "39eb0a87-3baf-45af-a795-f4a017bac95f", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 401251220, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat aplikasi buku besar pembantu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2019-06-15 15:07:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "39eb30f8-186c-4378-b2db-810e7b5a7867", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan perangkat lunak CAD 3D sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "39ecbcba-584c-434f-9e87-264ed3c25a15", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:59"}, {"kompetensi_dasar_id": "39ed0ace-b22d-48c7-b46c-ce2394640b2b", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur p<PERSON> (Perkerasan lentur dan Per<PERSON>asan kaku)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "39ed4c90-299b-4fb5-9647-d5c5ca72f03f", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803071500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menent<PERSON><PERSON> pemakaian jenis-jenis konektor saluran transmisi sesuai kebutuhan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}]