[{"kompetensi_dasar_id": "33d94464-e004-4519-9107-491df410554c", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 401251040, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan entry transaksi penjualan barang jadi dan transaksi pelunasan piutang dagang pada perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2019-11-27 00:29:58", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "33d9e1f8-793d-4cfb-a94f-cbe5113fe4a1", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803050200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit rangka pelat logam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2019-06-15 14:49:58", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "33d9f445-22ae-44e7-a9e3-558f5eac53c0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 828010104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menyusun catatan transaksi pada bukti-bukti penjualan tunai", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "33db20c7-c162-410d-925a-17cc755f50f6", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821140500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis rancangan gambar proyeksi pik<PERSON>l (3D)", "kompetensi_dasar_alias": "", "user_id": "ac8d5b4e-9eeb-4f73-967e-7888d5881f2d", "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:59", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:26:59"}, {"kompetensi_dasar_id": "33db404e-8f53-451f-8a3a-a51f5d752e68", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur perawatan dan perbaikan konstruksi rangka dan dinding bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "33db4675-e1b0-4bee-9ea0-065a686d7f11", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami system koordinat pada gambar CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:37", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:37"}, {"kompetensi_dasar_id": "33db5f58-b093-4983-942f-57d07460e9a4", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 821190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis system  jaringan distribusi tenaga listrik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "33db8786-8cbf-4cb2-891f-858a30720115", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami transmisi (pengiriman) data.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "33db9c30-0007-4345-9887-f8bdf31f94b5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam gambar ruangan kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "33dd6eb8-2807-4717-ad32-83ea3f25b13d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur <PERSON> dan <PERSON><PERSON><PERSON> serta <PERSON>n Hidup dalam pelaksanaan pekerjaan <PERSON> Bangunan Gedung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "33ddb3d1-80a3-4166-a5d2-403a5ba3a1a8", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804011100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji<PERSON> garis-garis gambar teknik sesuai bentuk dan fungsi garis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "33de0bf8-d98e-4404-aa00-b4b830d34e5c", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> dasar-dasar kontrol", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:52", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:52"}, {"kompetensi_dasar_id": "33dec6fe-aade-4119-b029-28715ddc5fe7", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:57"}, {"kompetensi_dasar_id": "33df2bec-ec2e-413b-8ef0-acde4c4c503d", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 800030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>an hasil produksi obat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "33df69b0-1de5-463f-9bb6-9f8166c764c4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:37"}, {"kompetensi_dasar_id": "33df793d-152f-4e23-b137-1f66380fbc89", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengujian dan pengukuran peralatan elektronika konsumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:56:58", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:56:58"}, {"kompetensi_dasar_id": "33e11f8f-1647-43fb-8373-ac9ca8bbbb5c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:34"}, {"kompetensi_dasar_id": "33e189e6-527c-4440-b5a3-6ebe25ea65e7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>  putaran mesin   \r\nberdasarkan kecepatan potong \r\nbahan benda kerja sesuai table \r\nyang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "33e3336a-4ac4-4b29-a305-063032436aae", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "33e38abf-c47e-4cac-944f-204c6b245a94", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 817040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik Pengambilan contoh Lpg", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "33e61548-b69c-4b1e-9eff-f56ceea6f649", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan produk-produk yang dapat dihasilkan dari minyak bumi untuk bahan bakar, bahan petrokimia dan bahan lain.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "33e6f2c9-65cb-4352-a4e1-16530408654f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:55"}, {"kompetensi_dasar_id": "33e7349f-2217-474a-80c3-f38522c3bd0e", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 800050410, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan antenatal care", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "33e77393-b87c-461f-b760-21dcb5cf9dc7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>  putaran mesin   \r\nberdasarkan kecepatan potong \r\nbahan benda kerja sesuai table \r\nyang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:08"}, {"kompetensi_dasar_id": "33e7fc0c-4c7f-4b71-a310-1bfa9a9341d1", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan peker<PERSON><PERSON> konstr<PERSON> beton  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:20", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:20"}, {"kompetensi_dasar_id": "33e908ff-ad1e-483a-92cb-daa1aa5e2a20", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "33ea3b3f-32f1-46d2-8d8d-7088059448b8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-ni<PERSON> k<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, perdamaian dan keu<PERSON>han ciptaan sesuai dengan ajaran <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:47"}, {"kompetensi_dasar_id": "33ea5769-769e-48a8-82b1-085b70b248c9", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 802010600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menciptakan efek morphing untuk objek 2 dimensi dan 3 dimensi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "33eb5470-5745-4b61-951e-27d4d825b11f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>inkan shuffle feel ke dalam teknik instrumen spesialisasi pada genre musik pop", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "33eb7063-4191-4921-a5af-0aac947b887b", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat integral tak tentu dari aturan dan sifat turunan fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:02"}, {"kompetensi_dasar_id": "33ec4121-bbfa-447c-acaa-5a80ca73e5eb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:44", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:44"}, {"kompetensi_dasar_id": "33eecc08-08f7-4f88-ba74-6e43ff3f86ef", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825070110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Memahami prinsip kerja motor diesel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "33f06158-c893-4f39-a415-5e27506bb9fc", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penjagaan terhadap resiko pada lanjut usia partial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "33f0b117-d0e6-491e-a8cb-00e9303bc120", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:49", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:26:49"}, {"kompetensi_dasar_id": "33f1b150-24ed-4c63-98a5-1fb0cca4a02c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:36"}, {"kompetensi_dasar_id": "33f1d054-4683-44e1-9fc4-265a39a71143", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> ka<PERSON>, permutasi dan kombinasi pada masalah konteks<PERSON>al", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:21", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:00:25"}, {"kompetensi_dasar_id": "33f27b3f-4aeb-4edf-8342-3578410fe69d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:05", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:05"}, {"kompetensi_dasar_id": "33f3e872-e65b-47f9-98c9-bf2390b71f5b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:11"}, {"kompetensi_dasar_id": "33f44821-3adb-4741-8bcb-cac9af1cc8a6", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 825060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pedoman budidaya yang baik (good farming practice) dalam pemeliharaan ternak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "33f46c9c-9f42-4548-8eaf-4ea7c4226f48", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja  sistem bahan bakar diesel pompa injeksi In-Line", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "33f5cd26-992c-40c3-a78d-216bd4ad443e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengurutkan struktur ragam gerak tari tradisi putri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "33f64b17-cc69-40e3-b0ca-979b2d727c41", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja(rancangan) pemasangan komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "33f915c6-1a95-4db9-8391-428842cf214e", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401141800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis limbah hasil produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2019-11-27 00:30:23", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "33f924f3-c86f-460b-97a1-9e0908d76170", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 807022600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyempurnakan program G Codes incremental benda kerja di mesin bubut CNC", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "33fa4b45-53ef-4679-adfe-3c21eebefcb5", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang mencerminkan kesadaran beriman kepada <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:52", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 14:49:52"}, {"kompetensi_dasar_id": "33fa8d85-7498-4b78-bbec-bbe7b0183903", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "33fadf17-60f8-4bf9-8398-9c90d5db8180", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 843050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil analisis genre musik R&B", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "33fb53c7-9614-46b9-b94b-3f053ab2ae4b", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827350600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part D. Rule 32 - 37", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:12", "updated_at": "2019-11-27 00:29:12", "deleted_at": null, "last_sync": "2019-11-27 00:29:12"}, {"kompetensi_dasar_id": "33fb946b-0515-4eee-8ac5-889fd6c64eaa", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:17"}, {"kompetensi_dasar_id": "33fc1466-7d30-4b86-950b-fa3e1d48a93a", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 300310100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis ungkapan berisi pesan singkat dan pengumuman/ pember<PERSON><PERSON><PERSON> (al- akhbar aw al-ma<PERSON>luma<PERSON>), dengan memberi dan meminta informasi terkait kegiatan sekolah, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan dari teks khusus sesuai dengan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "33fc5de9-5fb3-45cf-93cf-2d9f7231466b", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 820070500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kerja system penerangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "33fc8b9d-5101-4567-9fa8-a95593d66d73", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan perkembangan seni  budaya Nusantara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:02:45", "updated_at": "2019-06-15 15:02:45", "deleted_at": null, "last_sync": "2019-06-15 15:02:45"}, {"kompetensi_dasar_id": "33fcee23-0a94-4f18-be6c-d20b6f43e878", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip K3LH mengikuti SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:38"}, {"kompetensi_dasar_id": "33fd0eb0-c52c-477e-88a8-831c737ae0f0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:53"}, {"kompetensi_dasar_id": "33fd219e-0083-42ac-87f5-5c2e896e98bb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:19"}, {"kompetensi_dasar_id": "33feb5a7-9ae4-4c72-9a7f-1240aae469e4", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825062000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON> pem<PERSON>an aneka ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:02", "updated_at": "2019-11-27 00:28:02", "deleted_at": null, "last_sync": "2019-11-27 00:28:02"}, {"kompetensi_dasar_id": "33ff04ef-a738-43fb-b500-2e9cdefa8bea", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> proses sublimasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:11"}, {"kompetensi_dasar_id": "33ff3059-7484-440b-a4e7-8f810a3ac614", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi system distribusi tenaga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "3400b656-7ffa-4637-8308-7eb14c741cbb", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 843110300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Meragakan teknik pengembangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "3400ca89-db0b-4e5e-a08e-d3a25bf558fa", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 800030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> pengu<PERSON>an sampel produk ruahan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "34033c56-551d-426e-8e04-52e48dd3c193", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:31"}, {"kompetensi_dasar_id": "3403c0ee-b53b-4220-945b-1bb2649f40ba", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara pengetokan panel dengan cara hot dan cold shrinking", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "3404936d-39ee-4b85-b2ae-0f050165930a", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang dan mengajukan masalah nyata terkait luas segitiga dan menerapkan aturan sinus dan kosinus untuk menyelesaikannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:16", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:08:16"}, {"kompetensi_dasar_id": "34055bc4-c0a6-4aab-86e3-a2b1abeee45b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "3406906a-440b-48dd-9b81-564696e953e8", "id_kompetensi": "Lingkup kerja pada bidang <PERSON> dan <PERSON><PERSON> ", "kompetensi_id": 3, "mata_pelajaran_id": 800000139, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, pese<PERSON> didik mampu menje<PERSON>kan siklus akuntansi pada per<PERSON> jasa, dagang dan manufaktur, baik secara manual maupun menggunakan aplikasi komputer akuntansi. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:22", "updated_at": "2022-11-10 19:56:59", "deleted_at": null, "last_sync": "2022-11-10 19:56:59"}, {"kompetensi_dasar_id": "3406b5f4-6e99-468d-9b83-96d79c0645ab", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Men<PERSON>ji<PERSON> penanganan hasil produksi ternak unggas petelur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "3406bd7e-5dfe-450f-aa1a-e0ead434f276", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825280100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyimpulkan sifat bahan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "3406f9da-a1d5-4667-9856-bee71ca1953c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI) dan menya<PERSON><PERSON><PERSON> dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:49", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:49"}, {"kompetensi_dasar_id": "34079706-44f6-4511-8b4c-6707257d1c79", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:07", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:07"}, {"kompetensi_dasar_id": "3407b332-3261-479d-a10a-9415eeb8d1bb", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pekerjaan cetak saring (sablon) 1 warna", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2019-11-27 00:27:35", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "3407ba24-8584-4b04-9afb-cd0943a44c1c", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804010210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data kebutuhan pekerjaan desain interior", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "3409738e-a475-401e-b4ca-5a48f24c0ec2", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan system proteksi tenaga listrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "3409d7c7-3740-49ac-b910-429e2ccf3453", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802031800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prosedur <PERSON> dialog", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "340a5c36-86ea-4745-9e86-88d13e60abed", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat secara berkala pada sistem pelumasan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "340a9d4e-1099-4d4a-abe3-d58d39c09fc9", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola besar dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-10-19 23:19:18", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "340add37-8750-43ad-92f9-128cb87a607c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kegiatan (yang sedang di lakukan, sudah di lakukan, akan dilakukan, tidak dilakukan) atau merupakan kebenaran umum secara formal, informal dan honorifik, (기본 문법), seperti: Kegia<PERSON> sehari-hari, keg<PERSON><PERSON> kesehari<PERSON>, sesuai dengan konteks penggunaannya, dengan memperhatikan fungsi social, struktur teks, dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulis yang melibatkan tindakan menyatakan dan bertanya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:30"}, {"kompetensi_dasar_id": "340be9f6-6839-471f-af58-e3c4bbb30c03", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 821080300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengoperasian mesin bor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "340c5c65-d067-4024-91a1-b9bd30dceb71", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan K3 dalam penggunaan alat dan mesin produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "340c91ae-69bf-4e08-9b51-5f2933568ae8", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 300310100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan isi teks cerita (al-qash<PERSON>) pendek dan se<PERSON><PERSON>, dengan memperhatikan fungsi sosial, struktur teks, dan unsu<PERSON> k<PERSON>, sesuai dengan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:55:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:55:21"}, {"kompetensi_dasar_id": "340cab3a-f4c3-4038-9583-5debbf9944d8", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 828160100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat analisis kebutuhan dan harapan pelanggan internal dan eksternal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:19", "updated_at": "2019-06-15 15:07:19", "deleted_at": null, "last_sync": "2019-06-15 15:07:19"}, {"kompetensi_dasar_id": "340cf35f-53b5-4ad9-ae50-fb51d6f578c2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:11", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:33:11"}, {"kompetensi_dasar_id": "340d06b2-aa0c-4867-a594-a17e26b72af4", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "340d1122-5648-4586-b10b-c04e3cbbdf33", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangani sampel untuk pemeriksaan laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "340d77f6-d796-4d03-be2f-0b79b88ceeb5", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 831010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan identifikasi serat buatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "340ff5f2-f6c9-4d6b-a079-6153247050d2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahan baku dan bahan pembantu mengi<PERSON>ti stok<PERSON>, si<PERSON><PERSON> kimia fisika bahan dan intruksi kerja dari industri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:02"}, {"kompetensi_dasar_id": "34105439-35e9-47ba-ae30-fb78e1b0c405", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pola rotasi lagu yang akan disiarkan di radio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "3410f928-a5b8-4e99-a810-664a2614d7aa", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 815010900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>an serat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "341103bd-e968-4720-b667-dcb2f91b9aea", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 817010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi bagian-bagian dan karakteristik batuan penyusun bumi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "3411422c-c0eb-4dcb-b670-1d582374a24c", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 843080510, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gending suasana gecul  untuk penyajian pedalangan utuh dari cerita Ma<PERSON>arata atau <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "34118359-e98e-4583-976a-f768c83dcf0a", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 827390900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain boat winch", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "3414f323-fd67-488e-b006-70423bd13c5b", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi  rangkaian aktivitas gerak ritmik (masing-masing tiga hingga lima gerak).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:45", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:45"}, {"kompetensi_dasar_id": "3415c6e8-73e6-45bd-b285-b30114a343ec", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:21", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:21"}, {"kompetensi_dasar_id": "34164be7-ff7e-48ba-9511-96b6a11476fa", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis pengawetan dan pengolahan hijauan pakan ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "341653f3-3c47-4084-9073-5ac041decfb4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi sistem penotasian tabuhan instrumen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "34166997-1dd6-41e8-87f1-1812927a4beb", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 801030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi layanan terapi farmakologis bagi lanjut usia dengan petunjuk dokter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "34171212-f9c4-4010-a95e-9fe1522b9ebe", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:18"}, {"kompetensi_dasar_id": "34178be4-8bef-49da-9e28-fbb8cc975b49", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 809020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan persiapan sesuai pesanan cetak dengan teknik cetak tampon (Pad Printing)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "34180154-75b4-492e-b6e9-fc9bb5b442c6", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:11"}, {"kompetensi_dasar_id": "341857d0-9f84-4cc3-b2cc-f6595f42c56d", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802031400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami storyboard", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "341894d4-9418-4bc4-9213-b315811a390c", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:49"}, {"kompetensi_dasar_id": "3418a20d-364c-4543-83f4-10f0c9d458cc", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "3418fd8a-46e4-4db7-8ce3-6b06e93ccf82", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemesinan gerinda datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:04", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:03:04"}, {"kompetensi_dasar_id": "341a077e-a0c2-4206-996c-b2fb396b1c2c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan panggilan hidupnya sebagai umat Allah (Gereja) dengan menentukan langkah yang tepat dalam  menjawab panggilan hidup tersebut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "341b7a1b-4abe-4747-b39b-4eaa63266ba8", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI) dan menya<PERSON><PERSON><PERSON> dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:02:38"}, {"kompetensi_dasar_id": "341d0678-f1fa-4d40-83e8-2f116cb42d65", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis operasional sistem AC/DC power.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "341e9917-fd04-442c-ba60-6fcee7a6f243", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 807022700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kondisi mesin frais", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "341eeb09-a267-4bae-bb63-d09e014eca3a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis tegangan pada struktur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:17", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:07:17"}, {"kompetensi_dasar_id": "341f8f3d-a966-4418-9ba3-0610050c1684", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasanganinstalasi kontrol motor berbasis PLC", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:26", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:26"}, {"kompetensi_dasar_id": "341fc3c4-83e2-45ac-8880-bbc9b9b650ff", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 814030600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dasar statistik dan probabilitas dalam perencanaan pengendalian mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:29", "updated_at": "2019-11-27 00:29:29", "deleted_at": null, "last_sync": "2019-11-27 00:29:29"}, {"kompetensi_dasar_id": "341fe53d-17b1-46cd-b8ef-e4b2f519e975", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan centrifuge", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "34210c4d-5226-4d4c-8a57-17e4d9a26846", "id_kompetensi": "Tenaga pendorong/power plant ", "kompetensi_id": 3, "mata_pelajaran_id": 800000111, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> akhir fase <PERSON>, peserta didik mampu memahami berbagai jenis tenaga pendorong (power plant) yang digunakan oleh pesawat udara. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:19", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "342171a3-1dad-4bc7-a926-bd989035e803", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis pencatatan pengeluaran kas bank", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2019-11-27 00:29:58", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "********-42a6-462f-834c-46f978595604", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi teknik penulisan naskah siaran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "3423dc62-c70f-48db-a008-474d91139910", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>, konsep dan prosedur proses pencel<PERSON><PERSON> kain, cara pengendalian parameter, perawatan mesin dan penerapan konsep K3LH.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "34250dbf-81cc-471a-b471-0da9a8c8cc60", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804130800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil pengelompokkan bahan  berdasarkan sifat-sifat bahan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "34250e72-940f-4af2-856d-8f2609e68e9e", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 803081000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja sistem kontrol elektropneumatik berbasis rele", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "342530ab-e101-4924-a8b7-5cf28ef378e4", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan spesifikasi dan karakteristik baja.  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:04", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 16:04:04"}, {"kompetensi_dasar_id": "3425bf0a-e918-4eae-ada9-c9225ab03e0c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "3426b37a-4b68-4057-8649-79dbd9eeeed7", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pek<PERSON> konstr<PERSON> tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "34270c07-ff59-4b81-ae92-e29e066d9241", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 803081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membangun macam-macam komponen otomatisasi proses pada loop control", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "3427f2fe-4788-4f0e-a55e-4673e7268295", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300310100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mendemontrasikan teks sederhana terkait kemampuan (al- kafaah) dan kemauan (al-iradah) melakukan suatu tin<PERSON> (al amal), dengan memperhatikan fungsi so<PERSON> (wa<PERSON><PERSON><PERSON>), struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "3429b0c0-d874-4a92-bf5a-a005a529dfeb", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis peran keluarga dan sekolah sebagai lembaga pendidikan utama dalam kehidupan masa kini", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:00", "updated_at": "2019-11-27 00:30:00", "deleted_at": null, "last_sync": "2019-11-27 00:30:00"}, {"kompetensi_dasar_id": "342b74d9-8a36-4bf2-ac4b-dadbcf12dffb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 801040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Activity of Dayling Lifing pada lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "342c012c-c030-4ca0-820d-6abd83bab8ac", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 814050100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung jumlah sediaan pengaman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "342c9ac2-ffe8-4a4e-a843-7eb146e70995", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pengumpulan data  k<PERSON><PERSON>an pasien, k<PERSON><PERSON><PERSON>, dan ma<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "342cbedd-ad9c-43d0-8a42-2c829a5194aa", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan per<PERSON>ungan diagram benda bebas dan teori keseimbangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "342e0c63-2676-4f5a-a02d-26448f3aadd6", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827290500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kelayakan usaha pengolahan  rumput laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "342edc50-ae30-4629-828f-c783ef2d7993", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 821190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi gangguan sistem tenaga listrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "343066cb-ce8e-4d64-bfb2-f92a27372cf1", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:55", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:02:55"}, {"kompetensi_dasar_id": "34306968-580a-4aae-8e90-f02f0638c6d0", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:28:17"}, {"kompetensi_dasar_id": "343105b5-2398-4f54-9924-bbaffc9e8d96", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 828041100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan pengoperasian aplikasi pengolah angka", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "343191f3-bc20-46e2-aa8b-4c38e62dec15", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804011000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis-jenis per<PERSON> ker<PERSON>a ben<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:29"}, {"kompetensi_dasar_id": "3431e715-404e-4601-b249-2c12e29062c4", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil perawatan berkala kelistrikan kendaraan ringan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2019-11-27 00:27:26", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "3431f440-25da-4b5e-a322-85ad3770e23b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Alat Pemadam Api Ringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:04"}, {"kompetensi_dasar_id": "34328397-c1ed-46ff-bc6e-3fcfd132263b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827080400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan garis haluan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "34334733-5f73-4cd3-8281-dfbea5d65e97", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 825020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan pengendalian Organisme Pengg<PERSON> (OPT) tanaman sayuran sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "3434a93d-d584-4324-8730-449c9f78d503", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar instalasi listrik dengan konduit, cable ladder dan cable tray/trunking sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "34360dc7-6686-4725-9eb6-001c4ddd8849", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 807021920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang proses pengelasan GTAW sampai posisi 3 G untuk material mild steel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:47", "updated_at": "2019-11-27 00:29:47", "deleted_at": null, "last_sync": "2019-11-27 00:29:47"}, {"kompetensi_dasar_id": "34363a6b-962d-413d-b718-0166aa308cf1", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 815010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan proses pada mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "3436d2fd-a11a-4f34-9423-9ca6ee650573", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 832030200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan karya seni lukis realis teknik aquarel dan plakat.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "3437d33c-8428-4f0e-90d1-be66edfa21d2", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843061000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan permainan instrumen pembawa lagu utama dan pengolah lagu utama pada materi lagu ritmis dan non ritmis tingkat dasar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "3439fd86-58a1-423c-9281-10d2afb0401f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "343a4b04-9c26-40df-a510-516ecbe0837e", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> penye<PERSON>n akhir produk tenun satin dengan ATBM", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "343aae25-5f1d-4d8e-92fa-7123e150d583", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 815011000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>gua<PERSON> proses mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "343b0fd1-41ea-429c-a950-c54390e06870", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:44", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:44"}, {"kompetensi_dasar_id": "343b99c5-28fa-478a-91d6-9121b4cc3854", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:30:35"}, {"kompetensi_dasar_id": "343e9df0-f9fd-41f1-b7f0-1aaf9d0754bc", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> sebagai pembawa damai sejahtera selaku murid <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:03", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:03"}, {"kompetensi_dasar_id": "343f7156-d687-455b-82d4-19e8a1e20b80", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan refrigeran dan oli refrigeran yang digunakan pada sistem tata udara domestik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "343fc95f-fc4e-43b2-87bc-16c2fd506d8c", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:23", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:23"}, {"kompetensi_dasar_id": "343ff67c-581c-4416-9109-869dfd76a698", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan mesin  bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan mesin  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:04", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:04"}, {"kompetensi_dasar_id": "344044a1-850d-4a9f-8f30-bff6a3aa3cdd", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:21"}, {"kompetensi_dasar_id": "3441a5f8-10c3-4471-9302-2724a475d37e", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Member<PERSON>n solusi terhadap gangguan kebutuhan cairan dan elektrolit tubuh", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "3443260f-05b2-4a3e-8bc8-e3314ad4a738", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 822010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar teknik elektronika berdasarkan standar ANSI dan DIN", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "3444d288-de63-404f-a873-465fe7f16e31", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:49"}, {"kompetensi_dasar_id": "3445035b-b241-450b-8c1b-fe29a9194c64", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:32", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:32"}, {"kompetensi_dasar_id": "34450714-086b-4f14-9ffa-f842c18e4af4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "3445a6ba-8ed3-4a8a-99d5-239a99703a42", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804080700, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> cara membagi  garis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "3446d9fd-487b-4fce-8693-221d3caf2d11", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:15"}, {"kompetensi_dasar_id": "3448e163-e951-4466-83b5-523baa9c11fb", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan titrasi pen<PERSON> (argentometri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:47", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:47"}, {"kompetensi_dasar_id": "3449af39-b2c0-4242-8947-91f8b762761a", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825250200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menyajikan hasil pengujian kandungan komponen racun secara volumetri/ refraktometri/ spektrofotometri pada bahan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "344a193c-11fe-492a-af4f-65716b7c6b46", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 820070500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi sistem pengaman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2019-06-15 15:03:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "344b0d71-6f93-4bbe-86ff-7baca64c2a74", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil susunan acuan cetak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "344bfee6-3412-4e3c-bb26-6eed8d9bafd1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825230200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengidentifikasi food allergen pada produk pengolahan hasil hewani (susu, telur, ikan, kerang) dan peraturan kementerian terkait", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "344fc612-ecbf-492f-b1d6-cbd1e912f60b", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401110220, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keterkaitan antara struktur dan fungsi gen, DNA, Kromosom dalam proses penurunan/pew<PERSON>an sifat  dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:55", "updated_at": "2019-06-15 14:49:55", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "344fd8b2-dc90-4996-ba3c-a44128b9720f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "3450600d-6f3a-405c-880e-8cab8214f818", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis disain penataan instrumentasi medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "3452900f-532b-4e90-8fb6-7a972b827142", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 830090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menerapkan penataan sanggul gelung malang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:33", "updated_at": "2019-11-27 00:30:33", "deleted_at": null, "last_sync": "2019-11-27 00:30:33"}, {"kompetensi_dasar_id": "3453c803-3bf9-4082-89e7-70870c8c7651", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai pelanggaran HAM di Indonesia yang merusak kehidupan dan kesejahteraan manusia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:31"}, {"kompetensi_dasar_id": "3453f155-887a-4da8-84e0-a9054c41f4e5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:17"}, {"kompetensi_dasar_id": "3453f692-872b-45ab-b237-0188ea623193", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807022210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat pitot static tube", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "3454970a-e3be-4648-90c3-6d525c96369f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsi<PERSON> pen<PERSON>a daftar analisa harga satuan pekerjaan bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "3454da66-bc4c-4596-8b74-7c8e48557742", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> sebagai pembawa damai sejahtera selaku murid <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:01"}, {"kompetensi_dasar_id": "34562bc1-51f2-4076-9526-cb049f18d31d", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penyimpanan data analog", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "3457f23d-c359-48b7-bfd1-7d120ec281ce", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem Engine Management System (EMS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:30", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:07:30"}, {"kompetensi_dasar_id": "34581d4c-5b13-46fc-98d6-a7f6f641de0f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825220300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Memahami penyimpanan penggudangan pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "3459776d-4628-4105-ab8e-afbf603bcc29", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 802010700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan desain aplikasi lanjutan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "345a4f29-8202-48a8-bd24-f196ee20828b", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem rem", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2019-11-27 00:27:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "345add3c-126e-4e93-a1b3-2c825251b46b", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 803081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> kalibrasi positioner", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "345be702-c34c-47cf-896d-6f26de628257", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:26"}, {"kompetensi_dasar_id": "345bfc47-4e6f-4434-979e-409c3b0e4c68", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 821190400, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan aktuator pada sistem pengendali", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:20", "updated_at": "2019-11-27 00:28:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:20"}, {"kompetensi_dasar_id": "345c871a-fea6-4ccd-95d5-56e0397c37b5", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 808060610, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontrasikan cara mencampur bahan material komposit sesuai perbandingan tututan pekerjaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "345ced60-83ad-4491-8d49-ef6ef6fde7f9", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gambar <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "345d0d79-e3e1-44f9-9e57-23b29a706770", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara menyusun gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:20"}, {"kompetensi_dasar_id": "345d2760-bd0d-4895-9e41-b1362c81bea6", "id_kompetensi": "4.32", "kompetensi_id": 2, "mata_pelajaran_id": 825050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan prose pengolahan benih tanaman hortikultura/perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "345d8642-5bec-4205-a3db-7f7d90df52d8", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perubahan demokrasi Indonesia dari tahun 1950 sampai dengan era Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:45", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:45"}, {"kompetensi_dasar_id": "345f752d-f45c-4146-8072-4e248e994106", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis din<PERSON><PERSON> pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945 dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:11"}, {"kompetensi_dasar_id": "34603425-7442-4619-abbb-79818e15ae53", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:08"}, {"kompetensi_dasar_id": "3460a0ee-4aa4-4154-9a9c-8ef066deefa8", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan material pipa dalam kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "3460cba8-6f77-46ad-a4bb-8b81d3b077d6", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:32"}, {"kompetensi_dasar_id": "34621457-68fb-4884-8acd-60fc3e8cb7bd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kondisi operasi sistem kontrol motor berbasis elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "346266f7-b832-4892-be93-d6909d2bf003", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401251610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Merancang program loyalitas pelanggan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "3462ca94-9c8f-4809-a1a5-7b404524c566", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803061000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian penguat depan audio (universal pre-amplifier).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "3462d8b0-f1c4-423a-ae1a-0d4f9d48e1b4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi jenis-jenis power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:37", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:37"}, {"kompetensi_dasar_id": "34639806-7df3-48b7-a7d4-50195f0148a0", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 809021000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memodifikasi warna tinta cetak offset lembaran sesuai standar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "3463d8d2-b020-4887-b61e-d95a95565664", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> unsur-unsur tata letak berupa garis, bidang, il<PERSON><PERSON><PERSON>, tip<PERSON><PERSON><PERSON>, warna, gelap-terang, te<PERSON><PERSON>, dan ruang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "34645e34-3619-4c29-b9ce-6970432e7d4a", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 802032800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat desain kemasan produk (packaging)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "3464c4c7-53c9-451d-ac32-0f5b2ab4c1f4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:20", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:20"}, {"kompetensi_dasar_id": "3464f031-e04a-4f57-b506-dfed1d34b404", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825062400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar hormone reproduksi hewan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "3466397d-d41e-4025-9cde-2f2a01e9084b", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807020500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan Symbol-simbol gerbang lojik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "3466d925-7cb0-4749-88c9-9517814efa1e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803070810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memecahkan masalah dengan algoritma pemograman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "3466da83-ec90-4cd6-8ee6-cd470eef90a3", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807020500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian digital berdasarkan macam-macam gerbang logika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "3466ddd4-3b08-43f7-95fa-ae8b5630ec64", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "34686e26-fad3-41c5-870c-ca379a98fb22", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan berbagai peraturan perundangan serta konsekuensi hukum bagi para pengguna dan pengedar NARKOBA dan psikotropika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:58", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:58"}, {"kompetensi_dasar_id": "3468d976-8c98-4108-871c-79664cd23826", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar simbol dan logo.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "3469b7da-448d-437f-910a-47b082920331", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 804100600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peralatan dan bahan pemasangan saluran tegangan menengah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "3469fcd3-8fec-44ee-9c39-e33deee83afa", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Merencanakan sistem Instalasi\r\nPipa Air Bersih\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "346a0ad4-12c1-4d01-af89-d0896de88ae2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja instalasi kontrol motor dengan VSD", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:22", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:06:22"}, {"kompetensi_dasar_id": "346a9e76-d2d7-4fb8-bb39-ce8c141c6155", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:51"}, {"kompetensi_dasar_id": "346b8752-b25c-47a5-b6a3-a319c2cec2d8", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 817090300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan tubing completion", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2019-06-15 15:00:03", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "346bded8-10df-4bad-969c-dce592450212", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:54", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:54"}, {"kompetensi_dasar_id": "346f08cf-f58b-4fe0-b64f-b22fb606090c", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "3471d21c-2057-49a1-bb2a-9e248f4730a1", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "3471dbcd-9874-4250-889e-427c0d769419", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804131700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan problem proses Finishing logam (Lapis tembaga dan lapis <PERSON>) dan per<PERSON><PERSON>nya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "347309a7-338b-4539-baf5-e494d9ebfddd", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 826130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan cara analisa usaha agroforestry", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "347431cc-25c5-4c7b-93b8-51dfee587edf", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 802030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> alir proses produksi multimedia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "34743626-7504-4e62-9aa8-d62c7cbdcf3f", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 834010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan konsep relief teknik plastering", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "347469ea-9bdc-43a4-9213-ec3a0ad4bc44", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800081000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis clearing pada jaringan untuk pengujian histologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:33", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:30"}, {"kompetensi_dasar_id": "3475b714-f73c-47bd-a9d4-2446086e701c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:23", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:23"}, {"kompetensi_dasar_id": "34771e45-1e37-4232-bb2b-80949336bd1b", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802032200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Teori <PERSON>ar Komunik<PERSON> dalam konsep Fotografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "34780e31-29e5-47d3-8947-602dc8642b74", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis susunan acuan cetak rotogravure produk kemasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "347897ea-a816-4211-9a84-114577dbfdde", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memresentasikan hak atas\r\nkekayaan intelektual", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:00", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:02:00"}, {"kompetensi_dasar_id": "34794a98-68d6-4e7c-8bad-bfa7158bb7ab", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:44"}, {"kompetensi_dasar_id": "347b243f-cd9f-459d-930d-0e367194e7c8", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "347c2a1b-d8ac-49f0-8282-3279cb956998", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825070110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan standar prosedur operasional motor diesel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "347c6e37-96ba-4ae0-952a-a4f67658b7c0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan  putaran mesin  \r\nberdasarkan kecepatan potong  \r\nbahan benda kerja sesuai \r\ntable yang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:17"}, {"kompetensi_dasar_id": "347d704b-b227-43ff-90fc-d8529001cbb4", "id_kompetensi": "3.19", "kompetensi_id": 2, "mata_pelajaran_id": 401130620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis kadar karb<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "347dd256-f4f1-4520-9f4e-d72a42a7120b", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sirkit sederhana programmable logic controller (PLC) pada sistem kontrol otomatis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "347dd7a5-6ed2-4b4c-92d3-9e67555e3710", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825270500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prinsip pengambil an contoh hasil pertanian, dan hasil olahnya berbentuk padat dalam kemasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "347dee06-2b7c-4f60-8eef-5f1cd743a87f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik perancangan gambar 3D menggunakan komputer dengan program 3D Max dalam menggambar kapal (speed boat)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "347ffbbd-4aef-40ec-866e-fa995d4667d0", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 827210200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengelolaaan kesehatan benih hasil rekayasa pengembangbiakan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "34818238-ff2b-42d8-ad31-1f14d0b9a8bd", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pen<PERSON> & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "3481e91e-4c2e-4f9b-8395-7b5198f0c0da", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkonstruksikan  checker transistor bipolar /FET.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "34827ead-6e1a-4604-b947-7e55f8a228ae", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 828080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mencatat penggunaan dana kas kecil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "3483340b-4b98-4c44-a21e-3eee32836c27", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengoperasi-kan peralatan  survey dan pemetaan ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:21", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:21"}, {"kompetensi_dasar_id": "34839aad-17d0-4501-a923-55d918428b9b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bagian-bagian measurements of the respiratory system", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "348485ff-29b1-4e94-b5ea-95743b905bb6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803070810, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuatprogram dalam menyelesaikan masalah dengan Menggunakan bahasa C", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "34858451-2428-416e-8132-a0c439a93fc1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Engine Nacelles/pylons", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "34865f86-7920-4bc0-87bc-714ca8a68088", "id_kompetensi": "4.58", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai jenis katub pengarah elektro hidrolik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "348706fc-f300-4d3f-8845-a7e81906ab42", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843061100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan jalannya sajian repertoar gending/lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "3487ae8b-73b9-4430-b996-1babb6909d2e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804011200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan ukuran sesuai fungsi dan pandangan utama gambar teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2019-06-15 15:03:16", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "34890a2d-fa3d-4e55-a03b-45ad06be95f0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:24"}, {"kompetensi_dasar_id": "34897c5e-17cf-43ea-a1ef-bdb66a983b7f", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 828090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kegiatan administrasi kantor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "34899953-6884-45b8-941d-26293a24d452", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih dan menerapkan strategi menyelesaikan masalah dunia nyatadan matematika yang melibatkan turunan dan integral tak tentu dan memeriksa kebenaran langkah-langkahnya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "3489d3eb-8819-474b-8011-685543fe8a13", "id_kompetensi": "3.38", "kompetensi_id": 1, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi piranti kontrol elektronik berbasis thyristor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "348ad33f-6878-4fda-8360-060864de58cb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara menyusun gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "348ae1c0-d6aa-4cda-869a-3d42e86feab5", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 825061200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON> re<PERSON>ana usaha/proposal pembibitan ternak unggas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:08", "updated_at": "2019-11-27 00:28:08", "deleted_at": null, "last_sync": "2019-11-27 00:28:08"}, {"kompetensi_dasar_id": "348b9ecf-8377-4195-89ec-280390e3241a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:56:58", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:56:58"}, {"kompetensi_dasar_id": "348c1553-35fe-41bc-94db-f88ff12abf11", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 828041100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan pengetikan (keyboarding) 10 jari kecepatan 200 EPM dan ketepatan 99%", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "348c4662-d207-4e16-a765-7a1347884782", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati macam-macam bentuk konstruksi plafon kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "348c7a58-624a-40c8-a865-2da17d7c2245", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 818010400, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": " Menyajikan kesehatan dan keselamatan kerja dalam pelaksanaan pekerjaan geologi sesuai peraturan yang berlaku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:31", "updated_at": "2019-11-27 00:27:31", "deleted_at": null, "last_sync": "2019-11-27 00:27:31"}, {"kompetensi_dasar_id": "348c955e-b80c-4c7c-8cd6-ea3b1233c103", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 807020610, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan langkah-langkah pembuatan instalasi sederhana hydraulic instalation", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "348d9303-9f66-40e4-ae63-e49dc60a011b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 835010200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cetak tunggal/rusak relief", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "348e6a1a-8634-472f-bc20-473a57d2907d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "348f494a-5fc3-494c-833c-4f23e1a21834", "id_kompetensi": "<PERSON><PERSON>haman perkembangan teknologi di industri dan dunia kerja dan isu-isu global terkait dunia broadcasting dan perfilman (wawasan industri) ", "kompetensi_id": 3, "mata_pelajaran_id": 800000148, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik dapat menjelaskan perkembangan proses produksi industri broadcasting dari media analog sampai dengan media digital, FTA dan OTT, podcast, live streaming, live casting, streaming tv, web series dan video on demand. Industri 4.0, Internet of Things, teknologi digital dalam dunia industri, isu pemanasan global, perubahan iklim, aspekaspek (singkat) ketenagakerjaan, Life Cycle produk industri sampai dengan reuse, recycling secara teliti, berta<PERSON><PERSON><PERSON> jawab, mandiri dan bersama. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:23", "updated_at": "2022-11-10 19:57:00", "deleted_at": null, "last_sync": "2022-11-10 19:57:00"}, {"kompetensi_dasar_id": "348f4fcc-a3bb-4b1b-b675-154588c75e98", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "348f7b6f-8000-4300-a57e-ccc3dae5f4fd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800030500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur pengemasan primer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2019-06-15 15:07:03", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "34906c13-30af-4f95-8928-0c4081e322db", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 830050400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan nail art teknik wrap dan stamping", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:34", "updated_at": "2019-11-27 00:30:34", "deleted_at": null, "last_sync": "2019-11-27 00:30:34"}, {"kompetensi_dasar_id": "3490940b-77b8-400e-9163-0c81b10300ab", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan kontrol <PERSON>, te<PERSON><PERSON>, k<PERSON>mbaban pada proses industri kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:19"}, {"kompetensi_dasar_id": "3490da2b-ecea-4349-aff3-7fe469056f83", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 804010900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencoba perangkat keras untuk CAD  sesuai konsep dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "34910dbb-0132-4aa1-aa5d-9ec01da326c9", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 843070110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengadaptasi tata irama sajian vokal non  ritmis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "3492878f-860b-40e5-bac8-c351a404fe71", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 818010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sampling bahan galian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "349327dd-59c9-4375-805b-ef09db38fbb0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan macam - macam sensing element pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "3493e2bd-0709-4150-b4a2-c15f7546f3a9", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:44", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:44"}, {"kompetensi_dasar_id": "3494b5ff-a6b7-4c5f-b5b7-478c2299cfe2", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:29"}, {"kompetensi_dasar_id": "3494d152-6dee-491c-902e-ab572669ca34", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis gravimetri pengendapan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "3495239e-1f56-4905-8379-66a10b799d62", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 843060620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyeleksi alat dan bahan tata rias usia, karakter teater konvensional Asia, Barat dan karakter realis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "34981c67-c971-4494-8079-a9323561008c", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:23", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:23"}, {"kompetensi_dasar_id": "34982436-4a36-4c2f-afa5-418ec24ba767", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:44"}, {"kompetensi_dasar_id": "349825d3-c247-43af-bb1b-be128e5bee91", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:39"}, {"kompetensi_dasar_id": "3498eba0-49d4-4d33-8d1d-590d31f58f20", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 830050200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penanganan keluhan pelanggan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "349954f8-a82f-40b0-87cd-da14d8b5902c", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 827210600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis data/informasi stok bahan baku hasil industri perikanan laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:35", "updated_at": "2019-11-27 00:29:35", "deleted_at": null, "last_sync": "2019-11-27 00:29:35"}, {"kompetensi_dasar_id": "3499e2cf-001e-4e22-86b0-122691aea138", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 827390600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain main engine and associated auxiliaries", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "349bfb19-217d-49a3-9510-a180dfdc48c3", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik analisis secara potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "349cb478-409f-4121-9d55-48af1f5be5d3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 802040400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> undang-undang penyiaran dalam kerja penyiaran.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "349ebda6-4e7e-4c21-8b22-bef17d1f3f89", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi dalam perlombaan salah satu nomor atletik (jalan cepat, lari, lompat, dan lempar) dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:49", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:49"}, {"kompetensi_dasar_id": "349edee9-1565-476f-a89d-94a470d96a77", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>ah penyalaan api pada furnace/dapur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2019-06-15 15:03:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "349f9721-696b-4a4d-a236-7f0544674367", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis sampel dengan kromatografi kertas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:58", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:58"}, {"kompetensi_dasar_id": "349fd920-54fc-4dce-ae92-b02aeec62a5a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 802020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis jenis media yang sesuai dalam komunikasi data jaringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "34a139b7-945e-451a-8b5d-9d745e14c80e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803070200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaplikasikan beberapa sensor proximity se<PERSON><PERSON> fungsi, p<PERSON><PERSON><PERSON><PERSON><PERSON>, dan karakteristiknya pada peralatan system control", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "34a33ac9-3151-41a3-9689-7eaeb324dd62", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:18"}, {"kompetensi_dasar_id": "34a3d763-d78a-4f39-85a4-7a668df1ca05", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> sistem bahan bakar in<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:57", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:57"}, {"kompetensi_dasar_id": "34a818e5-ef69-4b0a-aeda-bc3c2482884e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 801031300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis disabilitas sensorik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-11-27 00:30:26", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "34a92574-f694-46cc-8374-8a28827a38ea", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:37", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:37"}, {"kompetensi_dasar_id": "34a98da5-34a5-4c74-ae5e-ffd815f846c5", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804160700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang bahan gambar detail komponen mesin dan produk rakitan kompleks secara rinci", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "34aa624c-a53c-4259-bdf8-0171bc713af8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 843090100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami setting panggung dalang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "34aad8f8-8583-459c-b9c9-a777f09969ed", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:18"}, {"kompetensi_dasar_id": "34ab0bbe-b00c-4ef7-bc3a-19de3bc6d6ae", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 802010300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data server melalui aplikasi desktop", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "34ab64a3-b101-4ce4-99fa-344498233d86", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:16", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:16"}, {"kompetensi_dasar_id": "34ae1759-f370-46c9-9126-1d9ac36504db", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> perintah draw dan <PERSON> untuk menggambar kusen pintu dan jendela 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "34ae7545-e1a3-48cd-ac2d-9f5f78e3f209", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkategori elemen-elemen struktur berdasarkan karakteristiknya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:20"}, {"kompetensi_dasar_id": "34af370b-c6d1-40b5-8e7a-83d453595ab8", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 828190130, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memberikan  informasi tarif k<PERSON>us penerbangan internasional kepada pelanggan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "34b0d72e-6748-4c43-92c3-d830fcd4872b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:57"}, {"kompetensi_dasar_id": "34b2d0e5-ebc9-44a6-9142-38f5bf36072c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 821060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> gambar  kons<PERSON><PERSON><PERSON>  lantai, dinding dan atap kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "34b3edef-af91-41eb-bbd4-3b3de78cedef", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821140500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan gambar kerja dan gambar susunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2022-10-18 06:44:03", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "34b42bfc-bc27-4db9-8e29-e22af1330a6f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 821030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami tentang test kete<PERSON>an kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "34b47365-a676-4b9c-9dc2-534e244b4c9b", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 820140100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  metode pengontrol kualitas kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:21", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:21"}, {"kompetensi_dasar_id": "34b47dc9-9c54-45ed-8a4e-3eed53ab4cba", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 830050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan perawatan badan dengan system balut badan (body wrap)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "34b58b9b-fd23-40ba-a9f1-78aded94c0d4", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:19"}, {"kompetensi_dasar_id": "34b5afdf-7c60-42cc-ae6a-382f6ae9f3e3", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> layanan housekeeping", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "34b5c939-61e5-4e6b-9404-183767dbeef5", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON>  putaran mesin   \r\nberdasarkan kecepatan potong \r\nbahan benda kerja sesuai table \r\nyang tersedia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-10-19 23:19:27", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "34b83ed2-e845-4ff5-a36f-a350c24d3c45", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dalam penggunaan peralatan dasar laboratorium    (alat-alat gelas dan non gelas)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "34b92ee1-3f0f-4a5e-8896-1c2b37adb485", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 800080230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keluhan pelanggan di laboratorium medik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "34b9e40d-7804-4555-8d95-eda6d84e1c90", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembersihan area kering dan basah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "34bd1cf8-7ef8-48c6-adf1-c0f21271513c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:06"}, {"kompetensi_dasar_id": "34bd3798-396c-47f2-bbfd-98002ce82509", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dan ap<PERSON><PERSON> cermin dan lensa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "34beed95-8e27-495e-8a48-5007f3abdd7f", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 820090300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kinerja Steering System", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "34bf6c7a-e6d0-43d3-b8ca-96064082888a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bagian-bagian instrumentasi laboratorium medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "34bf8c11-4c88-4c88-a9f4-f803117a6e2d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 828200110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan klasifikasi tamu atau delegasi yang akan datang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "34c0c42f-3a5d-44ef-95cb-961fff032fa6", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> beberapa  rang<PERSON>an senam lantai", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:14"}, {"kompetensi_dasar_id": "34c13574-3226-40c6-bbfc-f649fa89f313", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami fungsi perintah dalam perangkat lunak CAD untuk membuat dan memodifikasi gambar CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "34c1b42b-179a-4341-b6b3-60118cfa2153", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 843030400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeksplorasi bentuk kemampuan kreatif dalam bidang seni", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "34c29551-545f-4fd6-a71b-d14b5759953b", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804100600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan konstruksi sambungan tenaga listrik tegangan rendah (SLTR)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "34c35b35-2c0f-40c5-b367-b0a55366b332", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 822190500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengvaluasi kondisi Memelihara saluran kabel udara tegangan rendah (SKUTR)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "34c4a7ce-a197-4707-a766-3897594031d2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> da<PERSON> agama Hindu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:41", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:06:20"}, {"kompetensi_dasar_id": "34c5b8f0-a400-4025-9b20-a89d212467eb", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 827290300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola administrasi kegiatan pembibitan rumput laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "34c5df87-59d2-4107-a5a6-00cfa45e4ff9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827110330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan tugas ABK mesin pada saat persiapan, pela<PERSON><PERSON><PERSON> dan pasca docking", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "34c615e6-3cb8-4103-8460-5cad1a13f987", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "34c66e56-881d-4c3d-8cd8-f84b10b4a5e9", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 827290200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung parameter keberhasilan penanaman rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "34c6756f-50a6-4526-9f98-439f4398b414", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan rencana usaha agribisnis ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "34c76f6b-50b7-42c3-8c70-1a8aff1b9968", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan mesin gerinda datar/  survace grinding machine untuk berbagai jeni<PERSON> p<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:44", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:44"}, {"kompetensi_dasar_id": "34c781d0-3f6d-4194-85fb-6b66a54e24af", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 828050100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menganalisis surat dinas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "34c97793-0610-4b31-a950-a02b6e85bf8c", "id_kompetensi": "3.8", "kompetensi_id": 2, "mata_pelajaran_id": 401130620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan asam asetat skala laboratorium", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "34cab90c-8869-4a77-b35b-dbbd3fb2f582", "id_kompetensi": "Teknik dasar peker<PERSON>an kehutanan ", "kompetensi_id": 3, "mata_pelajaran_id": 800000132, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> akhir fase E, peserta didik mampu memahami melalui praktik terbatas tentang dasar-dasar pengelolaan pekerjaan dan teknologi yang digunakan di bidang kehutanan.  ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:22", "updated_at": "2022-11-10 19:56:58", "deleted_at": null, "last_sync": "2022-11-10 19:56:58"}, {"kompetensi_dasar_id": "34cbe10b-a3c1-45dc-9de3-df273b5481d5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan prinsip dasar  penggunaan kontrol kelembapan  dengan sistem mekanik dan elektrik serta hubungannya dengan interface dalam sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:30", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:05:30"}, {"kompetensi_dasar_id": "34ccab18-bc08-4e1c-83e8-15e28707a19f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 807022200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan laporan kerja mesin frais CNC", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "34ce18c8-61e5-4be4-9065-b11a0a2f19b6", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 821090320, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar konstruksi bangunan sekat kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "34cf5a45-ed59-453f-bf71-57ebb3e4ee8e", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803071500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa  data teknis antena Vertikal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "34cf6ef7-4e29-483b-bf5c-5c1bcac7c1e4", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804051200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan mesin tetap/statis dalam pembuatan furnitur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "34cfc7bc-56e0-46f6-9cc3-7aa35c3ac89d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan bagian-bagian struktur jembatan berdasarkan spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "34cfcbd5-c156-476f-9001-7ad8cb519f36", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 401251060, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pelaporan pengisian pajak on line", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2019-06-15 14:58:25", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "34d00390-6ab1-47fc-babe-75d2f75d5bdd", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan teks prosedur berkaitan bidang pekerjaan dengan memerhatikan hasil analisis ter<PERSON><PERSON> isi, str<PERSON><PERSON>, dan keb<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:10:50"}, {"kompetensi_dasar_id": "34d06b64-ee00-49e8-b197-c948f085e2c2", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 800020500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengolahan limbah medis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "34d143a4-03b3-4486-ac29-30ec2bf8d792", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:13"}, {"kompetensi_dasar_id": "34d1a68c-b52b-42fa-8d60-ed59dc92b657", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 817050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON> bagian-bagian peralatan produksi bawah permukaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "34d2769c-a8af-4bb8-98ab-9405dee880a1", "id_kompetensi": "4.39", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan inspeksi pengatur Beban Boiler", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "34d36d21-201b-48b8-b9b6-94608ae9660a", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802020800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> manajemen harddisk pada server", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "34d3bdd9-50d3-466a-9988-92a4bd8b068b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, dan merancang koreografi aktivitas gerak ritmik, serta mengevaluasi kualitas gerakan (execution).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:33"}, {"kompetensi_dasar_id": "34d60644-54e5-4bb1-acaf-22355ce6d9a3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 826010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan produksi bibit  tanaman hutan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "34d6152c-faa9-4150-8ae7-cd2022d32bcd", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825130300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakuka pen<PERSON>n luas lahan pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "34d803b6-83c1-42ce-a4ef-8f45c7fca12b", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 825020610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melaksanakan pengairan tanaman perkebunan semusim penghasil minyak atsiri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "34d8456d-6eef-4e2b-9cb3-e477d7d7f8a7", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 802030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami produk-produk multimedia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "34d8c91e-0c3f-4cb9-9ffc-6791a7e87fe0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>n <PERSON> ( K3LH ) dalam kegiatan laboratorium", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:00", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:00"}, {"kompetensi_dasar_id": "34d8f0da-2c33-4713-922f-a34c820a9c99", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 843110200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji data sifat hubungan antar karakter dalam lakon", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "34d97ea8-7cad-48cf-a9a4-fa5b5fb12fcf", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "34d9ed32-b407-43bf-bfeb-84ec4d7b2847", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:23"}, {"kompetensi_dasar_id": "34db5402-e07c-447f-a389-983a6a176920", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804190100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan kekentalan suatu zat cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "34dc24cf-b2af-43fd-aff1-60361c92225c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:37", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:37"}, {"kompetensi_dasar_id": "34ddc4fa-0844-4564-b719-d8ba83f1c7ab", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:59"}, {"kompetensi_dasar_id": "34dfc039-4f21-457a-a3aa-f243a711303b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perhitungan jaringan distribusi tenaga listrik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-10-18 06:43:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "34e05f72-dda8-4bf0-8012-5bed8d450439", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> hasil evaluasi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:15"}, {"kompetensi_dasar_id": "34e11c0f-884c-45b4-8d68-d537a3acd4c9", "id_kompetensi": "<PERSON><PERSON><PERSON> dasar proses produksi pada industri manufaktur dan rekayasa bidang konstruksi kapal ", "kompetensi_id": 3, "mata_pelajaran_id": 800000112, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami praktik dasar yang terkait dengan seluruh proses produksi dan teknologi yang diaplikasikan dalam bidang manufaktur dan rekayasa konstruksi kapal, antara lain praktik dasar alat ukur, membaca gambar konstruksi kapal, mengg<PERSON><PERSON> konstr<PERSON>si kapal, mengelola informasi dalam gambar, serta penerapan industri 4.0 pada lingkup Teknik Konstruksi Kapal. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:19", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "34e1269c-a1e8-4ffb-afdb-5c269e89f180", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem per<PERSON>,  bahan baku dan bahan penunjang mengikuti intruksi kerja industri bersangkutan, <PERSON><PERSON><PERSON>, kim<PERSON> bahan dan stok<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "34e1aee4-2ba8-47bc-a007-1d8430227aa3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 801031400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melaksanakan kode etik pekerjaan sosial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "34e3556b-1e5a-4be0-a076-5e18c9afae7a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300310500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Memahami tindak tutur untuk memberi dan meminta informasi terkait memperkenalkan diri dan orang lain, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "34e37714-0422-43bd-a45d-d2304ca935f3", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 820090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasi sistem hidrolik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "34e4f517-f8eb-4c1e-8d30-b79e084b4dde", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem utama Engine dan mekanisme katup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "34e51d6b-b4df-424b-ae5a-4c3b73133554", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "34e6750e-04fe-4f8f-9818-e5778028331e", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 803080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan Prosedur Pemeliharaan Sistem Kontrol PLC pada SCADA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "34e676bc-83cf-4e9c-8b29-2af8135b0aab", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:30", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:30"}, {"kompetensi_dasar_id": "34e6b3af-4291-41c2-aeb4-1d0218991e76", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 826130100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pola/model agroforestry", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "34ea171f-15e2-4799-8e1c-03c5fe173112", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:54", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:54"}, {"kompetensi_dasar_id": "34ea6d9f-de2f-499d-ba6d-f9606a717273", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 830140200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perawatan badan dengan alat teknologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "34ec1e3e-210e-4700-87b1-5632bde304f7", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 821080300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep pengoperasian mesin spindel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "34ed066b-1e20-48b9-9a48-e5ecd43d7d28", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401140900, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil pengukuran bidang polarisasi dengan polarimeter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "34eeb099-dc89-4eb9-92a4-b9bfa795a259", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803081200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara macam-macam sensing element pada sistem instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "34eeee99-694e-4d70-9683-d3ea882f96ab", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 820130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi Preventive maintenance sesuai dengan buku literatur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "34f100b1-84a7-487b-b7a7-07f0b794fadb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 807021810, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja wiring connection, prose<PERSON><PERSON>, p<PERSON>sangan Antena radio komunikasi dan Navigasi pesawat udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2019-11-27 00:29:58", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "34f1014d-a71d-4a73-8249-4399dfbc9f1e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:29"}, {"kompetensi_dasar_id": "34f137be-453f-4a65-be53-3f4469a097ec", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 807020810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja OP-Amp", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:38", "updated_at": "2019-11-27 00:29:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:38"}, {"kompetensi_dasar_id": "34f1deed-8ae2-4c47-942f-4e17ab0940ad", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kekuatan bahan dari sifat elastisitasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:20", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 14:59:15"}, {"kompetensi_dasar_id": "34f2253d-514d-4183-845b-8885c703bc46", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON>t titling dan efek visual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "34f4162f-eafa-4398-b114-bbb3b447c458", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang fungsi naik dan fungsi turun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:42", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:42"}, {"kompetensi_dasar_id": "34f66df8-e936-442a-a847-3168137f96fd", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:21"}, {"kompetensi_dasar_id": "34f68b03-13f0-4d7f-a2c7-2f78a9c3dcbf", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pen<PERSON> & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:55", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:59:55"}, {"kompetensi_dasar_id": "34f7bf9b-5933-4c6a-a44b-14b261ec4f0b", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan alat-alat ukur hidrolik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:40", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:06:40"}, {"kompetensi_dasar_id": "34f7d9e1-11e0-4ff4-8d20-d58fe42183a7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 843050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeragakan progresi akor progresi akor primer dengan menggunakan akor balikan kedua", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:06", "updated_at": "2019-11-27 00:28:06", "deleted_at": null, "last_sync": "2019-11-27 00:28:06"}, {"kompetensi_dasar_id": "34f8a9d7-927e-4778-9e62-69f0ff13d979", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan metode pengurutan kulit kepala", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "34f8bd17-bba8-4448-abf2-cf43dc997af3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam special service tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:17"}, {"kompetensi_dasar_id": "34f92a3b-1538-4264-8e12-a607337d8fb6", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 818010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep Pemetaan Geologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "34f95090-c196-40a0-98b9-c94ab9cbaf43", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804210100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>pan proses pemboran", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "34fa3086-9192-45a5-abc5-23ec0c946bb3", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan permas<PERSON>/ isu dari berbagai sudut pandang yang dilengkapi argumen dalam berdebat berkaitan dengan bidang pekerjaan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:05"}, {"kompetensi_dasar_id": "34fa67f1-a1f0-4ae7-bee6-8bca34d1fd03", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-ni<PERSON> k<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, perdamaian dan keu<PERSON>han ciptaan sesuai dengan ajaran <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:47"}, {"kompetensi_dasar_id": "34fb0d68-11f9-4819-a47e-51a42a54f853", "id_kompetensi": "3.4", "kompetensi_id": 2, "mata_pelajaran_id": 814031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perhitungan perhitungan nilai pabean barang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:40", "updated_at": "2019-11-27 00:29:40", "deleted_at": null, "last_sync": "2019-11-27 00:29:40"}, {"kompetensi_dasar_id": "34fb5ea0-f551-4b01-8799-dbc198d4477b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401131110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat garis-garis gambar teknik sesuai bentuk dan fungsinya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "34fbd36a-5e53-4d25-a4fc-5e41f1e984c5", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 800050430, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pertolongan pemberian makan melalui selang nasogastrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "34fc200e-0cff-45d1-a452-20ab208dcc79", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827290500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penanganan limbah dan hasil samping pengolahan rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "34fd97cf-962c-4aee-8cf4-b5a6e320074e", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 824060300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang  isi program Siaran Online", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "34fda1cf-4eeb-45a1-aaba-d7a1c851ebda", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 843060410, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis ragam gerak dasar tari putri berdasarkan pola ruang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "34fea79d-eda9-418d-8bdb-66cc1b54989c", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan pendeteksi range temperatur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "34fee6f0-f16b-4622-9fc2-34a0189b673c", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks anekdot, <PERSON><PERSON><PERSON><PERSON><PERSON>, laporan hasil observasi, prose<PERSON><PERSON> kom<PERSON>, dan negos<PERSON>i yang koheren sesuai dengan karakteristik teks yang akan dibuat  baik secara lisan mupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:49", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:26:49"}, {"kompetensi_dasar_id": "34ffad6c-9275-400a-81d1-ebb321260d04", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:04"}, {"kompetensi_dasar_id": "34ffeaa0-08e8-40cb-aa8f-4d1357484753", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "35007305-5109-4e13-8221-a249403a05c4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "3501015f-edd1-44c4-80b0-d6bebd1de159", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 830080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> berb<PERSON>i desain pewarnaan rambut double aplikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "35022e33-ef0d-4d1d-9f03-c562a32f69f3", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 809010500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> alur proses produksi barang cetakan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "3502772b-ff93-4857-acf2-04c08c3a326a", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825062400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengi<PERSON><PERSON><PERSON><PERSON>an hormon reproduksi hewan jantan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "3503344c-90c7-4ce3-9ca4-678a03ae3816", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:36", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:36"}, {"kompetensi_dasar_id": "350340a4-935a-4add-8814-c87733<PERSON><PERSON><PERSON>", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:42"}, {"kompetensi_dasar_id": "3504ce07-1c73-4c34-bda7-8d19aecb9cee", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "35050c3a-68dd-4818-91ed-f0df355a6cf5", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis k<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "35056e3c-8885-4fca-b3b6-db8760796916", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843060410, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ragam gerak dasar tari putra", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:29", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "3505d16a-1cee-4641-984c-531765d2ccd8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jeni<PERSON>, <PERSON><PERSON><PERSON>, dan fungsi clay body dan bahan lain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "350603a2-71fb-4d00-8db1-c0853acef06a", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "35062ce5-8b7b-4fdb-9f28-11e24774412f", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:22", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:22"}, {"kompetensi_dasar_id": "35076ea0-6f8d-4f05-a789-d998406115b3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 828140100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>  bakteri penyebab keracunan makanan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "350a17f4-9052-4663-a27d-af55311002e1", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:55", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:59:55"}, {"kompetensi_dasar_id": "350b2b8f-9693-4faa-afa1-bc63caa59049", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit berbagai sistem penggerak (actuator system) pada peralatan elektronika instrumentasi medik (media elektro mekanik, pneumatik, hidrolik)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "350c6504-ed5f-4f6f-b4b7-95cddc772ade", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:18"}, {"kompetensi_dasar_id": "350c8524-6f73-4aa1-a690-6c4f11bd7a6c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843030400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkomunikasikan bentuk-bentuk pengembangan kreativitas seni", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "350daaae-ef77-4d96-bbed-142f3ae281ed", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterpretasi makna teks cerita sejarah, berita, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:02"}, {"kompetensi_dasar_id": "350e103e-db15-47d2-9f07-4c6bee74f9ea", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:45"}, {"kompetensi_dasar_id": "350e23a9-8e74-44d1-afc9-a964c64acf83", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:37"}, {"kompetensi_dasar_id": "350f477f-fd39-4d2e-962b-7184edd90cf4", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bahan-bahan grafika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "35109719-60f2-4a19-a1f5-5b95a991fd73", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:51", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:51"}, {"kompetensi_dasar_id": "3510fc1c-b900-4ee0-bd00-f659a7b0cf31", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 814140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses pelumasan serat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "3512b6c8-657c-4eb2-b99c-3b1b8d69d515", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "memeriksa instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "3513dc8f-9486-4d87-b96a-67e2d50f3e78", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dan prinsip kerja listrik arus sea<PERSON> (DC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "3517a5ec-2d54-4159-8d3f-d3d76e199a10", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827350300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Describes the use of self-tensioning winches", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "351993d5-89cb-47e7-9758-d6d6adb73a71", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 820140300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki mekanisme blok silinder dan kelengkapannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2019-11-27 00:27:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "351a3750-7769-4804-8fe1-f39fefbc2140", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemesinan bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan  teknik pemesinan bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:52", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:26:52"}, {"kompetensi_dasar_id": "351b4690-eb1b-482e-b462-2b0d1658b090", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaitkan bagian-bagian instrumentasi radiologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "351b5a39-36d5-4040-9ce7-fd2f7d6307ab", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 828210100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis pembatalan reservasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "351ce438-e201-4a4a-9ec6-1764f864ee9f", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Memproduksi teks eksplanasi berkaitan dengan bidang pekerjaan secara lisan atau tulis dengan memerhatikan struktur dan kebahasaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:07", "updated_at": "2019-11-27 00:30:07", "deleted_at": null, "last_sync": "2019-11-27 00:30:07"}, {"kompetensi_dasar_id": "351d4b87-1e1e-439d-b6bb-d5e22fa80593", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804040100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fenomena batuan di lapangan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "351d71b8-cd63-4dfd-86f8-c75d62561158", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan macam-macam power tools", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:25"}, {"kompetensi_dasar_id": "351e3730-53d6-4e95-8ce5-728895ee1fe9", "id_kompetensi": "3.35", "kompetensi_id": 1, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengambilan dan penanganan semen ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "351f46ba-950f-48ac-aee7-53befb6f39ba", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807021400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> siste<PERSON>, cara kerja, cara memer<PERSON> kond<PERSON>, pema<PERSON>an perangkat komunikasi internal pesawat udara.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "3520763f-d8c0-43be-a258-5aff438f4c3d", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan sifat turunan fungsi terkaitdan menerapkannya untuk menentukan titik stasioner (titik maximum, titik minimum dan titik belok).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:02"}, {"kompetensi_dasar_id": "3521543c-158e-4a10-a644-26d59385c8c7", "id_kompetensi": "4.2.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (31): 13-14 dan Q.S. <PERSON>Ba<PERSON> (2): 83 sesuai dengan kaidah tajwid dan makhrajul huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:40", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:40"}, {"kompetensi_dasar_id": "35216276-2688-4e07-8dc3-1c26f92a7158", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "3521bee2-825c-470a-a498-da81989a05c7", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat dan karakteristik limbah dalam penanganan limbah B3 dan non B3", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "3521c24d-5d3a-4226-9ec3-9b7fc7b985ae", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>r nilai-nilai demokrasi pada konteks lokal dan global mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:01"}, {"kompetensi_dasar_id": "352346a2-55b0-47ad-ba74-0e82dae33cc4", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan alal-alat ukur elektrik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:25"}, {"kompetensi_dasar_id": "3524eea0-0b59-4c91-bb39-3938725a7d8b", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 831080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis komponen komponen rok sesuai desain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "3525a347-6540-4c70-ac0b-1663dfb560d1", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Auxiliary Power Units (APUs)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "3525b77a-5e0a-4e2f-ab75-87810f33a504", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807021300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, prin<PERSON><PERSON> k<PERSON><PERSON>an proteksi kelist<PERSON>n pesawat udara.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "3526417e-e104-49bb-a93e-5652221f4b08", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 803071400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa proses k<PERSON><PERSON>an <PERSON>er dan driver", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2019-11-27 00:27:46", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "35269a38-32e9-4950-aedb-27c8c94830c2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami penyetelan steering linkage", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "352772c8-2016-456f-b762-91374a3a4215", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 826070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi software pengolah data pengukuran digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:13", "updated_at": "2019-11-27 00:28:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:13"}, {"kompetensi_dasar_id": "35277b89-6a18-409f-bd3b-2d4992c5b465", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 821090310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pekerjaan gambar Mouldloft kapal <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "35299f98-569e-41ef-b7e6-ce8a0633dba5", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 800081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur pemeri<PERSON> kep<PERSON>an bakteri terhadap antiseptik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:19", "updated_at": "2019-11-27 00:30:19", "deleted_at": null, "last_sync": "2019-11-27 00:30:19"}, {"kompetensi_dasar_id": "3529d89f-adee-4b89-9ab8-11059f4a93fa", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> hasil usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "352a9023-7286-46ff-bc9d-9168d5937bb8", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 828120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> re<PERSON>ana promosi objek wisata berdasarkan jenis dan karakteristik wisatawan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "352af7be-60a2-4b1e-a9a2-0565421cae3d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:45", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:45"}, {"kompetensi_dasar_id": "352afc0e-2343-4462-81c9-a3923d09e6a1", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membantu mobilisasi pada lanjut usia mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "352b4baf-c30b-4c4b-bb11-90b13bf5957c", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 840020130, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat resep glasir keramik bakaran rendah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "352bfa57-64fd-44b5-94b9-41a991e8e426", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 831080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan pembuatan dan pemasanagn tiket, label dan pengikatan blus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "352cd1f1-dbfb-4d19-bc11-ccf187d22196", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi  rangkaian aktivitas gerak ritmik (masing-masing tiga hingga lima gerak).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:13", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:13"}, {"kompetensi_dasar_id": "352d0262-8f5a-4df1-b2b0-94eb7aca48b2", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 843070300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontasikan ornamentasi vocal ritmis pada lagu mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "352eabb8-8a8b-4411-939b-e6f02ddd9f4f", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 817050100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "memahami  jenis-jenis unit wireline", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "3530d092-ac2c-4d84-83ec-cde5132c19bf", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804101100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pek<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "353109c3-3bca-411e-a80b-23532b1bf997", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan peralatan penukar panas sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:07"}, {"kompetensi_dasar_id": "35315271-1e29-49dd-89f8-5b8d03dd5928", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 841010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembuatan perhiasan tanpa permata", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "353185ae-a9be-4a7c-a81c-4b78df4d1499", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803040800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi perangkat indoor & outdoor,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "3531fd2c-180e-4c78-89fa-5c28eeb39a87", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 802032200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan Pen<PERSON>an <PERSON>angkat Penyinaran di dalam studio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "35322ecb-2f3c-4484-942b-659a75e35db7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:08", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:08"}, {"kompetensi_dasar_id": "353464f0-2c25-42ee-8692-e6c26ab4e31c", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 826060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan Aplikasi GIS dalam perencanaan teknik sampling inventarisasi hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:13", "updated_at": "2019-11-27 00:28:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:13"}, {"kompetensi_dasar_id": "3535970a-a017-4da2-92d5-d15c67ffb6ba", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Unggul pada kompetensi:\r\nMenghitung volume pekerjaan pada pelaksanaan konstruksi bangunan gedung\r\nMenghitung upah untuk pekerjaan bangunan gedung\r\nMenghitung Rencana Anggaran Biaya (RAB) pekerjaan konstruksi bangunan gedung\r\nMenyajikan jenis-jenis bahan yang digunakan untuk konstruksi bangunan gedung.\r\nMenghitung kebutuhan bahan untuk konstruksi bangunan gedung\r\nMenyajikan Memahami prinsip penyusuna daftar analisa harga satuan pekerjaan bangunan gedung\r\nMembuat daftar analisa harga satuan pekerjaan bangunan gedung.\r\nMenyajikan jenis-jenis pekerjaan pada pelaksanaan konstruksi bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "35359a9b-cf09-46b7-a2d3-faa374c0a11e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami etimologi multimedia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "3535f287-b3ba-4ebd-bff9-da45a10776ec", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:50", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:50"}, {"kompetensi_dasar_id": "353651f5-2dc2-46a4-82a0-75b7b6732715", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 825250700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis substrat se<PERSON>ai jenis ikan hias yang akan dipijahkan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "35372052-1b41-48dc-ac55-b618a2bf8496", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:38", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:38"}, {"kompetensi_dasar_id": "3538b6c9-9361-4c23-8527-377a54e60414", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan  sistem starter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "35396f8f-4e52-4784-a9eb-41762c42393a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802040900, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah informasi tentang sejarah perkembangan media", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "353ac14c-ac04-4a31-a656-e26076c41909", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> rang<PERSON> PWM-(Pulse Width Modulation)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2019-06-15 15:12:30", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "353ac25b-4a65-41e2-8ca7-d8b3a4b507c7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara kerja peralatan pemotongan logam dengan nyala api.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2019-06-15 14:52:54", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "353af95a-e4f1-4bc6-9d93-6816c40c7594", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 839050100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi batik tulis modern", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:50", "updated_at": "2019-11-27 00:28:50", "deleted_at": null, "last_sync": "2019-11-27 00:28:50"}, {"kompetensi_dasar_id": "353b85a0-b013-42cc-8493-7cd6182da34e", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "353bd5b9-49dc-47fa-a165-79ba00fc3281", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> prosedur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "353cfa95-9483-410f-82b7-f6d5d8c42b7e", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyusun teks eksposisi analitis tulis, terkait isu aktual, dengan memperhatikan fungsi sosial, struktur teks, dan unsu<PERSON> k<PERSON>, secara benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "353e03e8-dab7-4754-bca5-1676fcf2bebf", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803081000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kondisi operasi system control elektropneumatik berbasis rele", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "353e5bb7-526b-458d-b2a0-a89601b04fb4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemesinan bubut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "353f9e00-e3ce-4fd1-8c7d-7ea5a9e27600", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 803090400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mensimulasikan output berbagai tipe kontrol (tipe P, I, PI, D, PD, dan PID) pada aplikasi peralatan control instrumentasi medik menggunakan software Matlab (tanpa-,/dan dengan system umpan balik)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "35418f9c-f8f5-41e1-88e3-4d755616f0e0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 826070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penentuan posisi dengan receiver <PERSON> se<PERSON>ai prosedur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:13", "updated_at": "2019-11-27 00:28:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:13"}, {"kompetensi_dasar_id": "3542f821-7782-48f5-871a-890d41a33638", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804100700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> prosedur pem<PERSON> trafo <PERSON>n", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2019-11-27 00:28:15", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "3542fe52-4b48-430f-a723-c487f031517c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses fisika dan proses kimia pada industri phospat dan boron.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:10"}, {"kompetensi_dasar_id": "3543e1e5-6b8e-4468-b192-94b4523004d7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 832030400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> langkah-langkah/prosedur pembuatan seni lukis dinding (mural/Mosaik)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2019-06-15 15:07:25", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "35446433-b80b-4137-86f9-465712bdab40", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 801031500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menerapkan pencatatan dalam praktik pekerjaan sosial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "35463fbd-851e-4832-bb52-0717e3999a2f", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:53"}, {"kompetensi_dasar_id": "354742c0-8b50-4453-9f72-84abf8175738", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825280300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menetapkan teknik pengendalin bahan hasil pertanain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "3547fba9-0e59-4137-9f53-b242cdd747ef", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 807022210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeksperimenkan pengukuran suhu pada pesawat udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "354b5d08-f916-42ae-b4a2-d02804994e94", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:07", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:02:07"}, {"kompetensi_dasar_id": "354e04e0-059d-41f2-a07e-809fb0495762", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:17"}, {"kompetensi_dasar_id": "354edebb-383f-4631-b44f-0c6fac7aa9ab", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 831060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Membuat desain celana kerja sesuai dengan konsep colase", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:36", "updated_at": "2019-11-27 00:30:36", "deleted_at": null, "last_sync": "2019-11-27 00:30:36"}, {"kompetensi_dasar_id": "35525f5c-2e68-43dd-9ee8-d192914130e1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis elektrothermal,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "35529f6c-2586-40c9-a332-9c9ad75d64ef", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 830050200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat persyaratan dan kontrak kerja karyawan/ SDM salon SPA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "3552e4c2-fff0-4ae7-9643-2454d280f672", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "3552f06a-4b4a-4157-843b-30006c92c548", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membuat prosedur peker<PERSON>an \r\npembubutan berdasarkan \r\ngambar kerja ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:07", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:07"}, {"kompetensi_dasar_id": "3552f40b-6c4a-46af-9e72-b13c14631471", "id_kompetensi": "4.37", "kompetensi_id": 2, "mata_pelajaran_id": 825063300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi kebutuhan semen beku pada ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:16", "updated_at": "2019-11-27 00:28:16", "deleted_at": null, "last_sync": "2019-11-27 00:28:16"}, {"kompetensi_dasar_id": "35545c64-e9d0-4a60-90f7-ba59355115d6", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "3555bcdf-1b68-444a-81c9-0f16c772f870", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 808020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar bentangan dengan metoda garis segi tiga bentuk benda geometri dengan penampang berbeda bentuk alas bujur sangkar dengan puncak lingkaran dengan titik pusat semestris", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "3556dc3b-00ba-4d5e-acd8-72c54ddd3cce", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami dasar-dasar system pneumatic", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:25"}, {"kompetensi_dasar_id": "355710a5-b11e-40df-b986-487df182c489", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 842040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pembuatan ukiran tiga dimensional pada produk kriya kayu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "35573866-05e9-44d6-99f9-8e5e30ff50ab", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> berda<PERSON>kan hasil verifikasi alat ukur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "3557d320-a6f5-4426-88aa-70ef3ec11f3b", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 825063100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> pemasaran hasil dalam usaha aneka ternak (hewan k<PERSON>ayangan)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:18", "updated_at": "2019-11-27 00:28:18", "deleted_at": null, "last_sync": "2019-11-27 00:28:18"}, {"kompetensi_dasar_id": "3558adc9-db58-41af-86f8-60cabfbc2f9c", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804111100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifiksi Alat yang digunakan untuk Perawatan peralatan Hidrolik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "3558d4f0-da0b-4853-af33-0d45fcbcce29", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:54", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:54"}, {"kompetensi_dasar_id": "355a58e6-5bcb-4db7-8fbd-edda23c9966f", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827350600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part D. Rule 32 - 37", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "355a8595-140a-43c2-8afa-f03a5b67631b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan  prosedur \r\nk<PERSON>n <PERSON> dalam \r\npeker<PERSON>an memb<PERSON>ut", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:12", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:12"}, {"kompetensi_dasar_id": "355a9850-16e0-4f02-92af-2da352372a09", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 807020520, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>tan dan komponen jig, fixture", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}]