[{"kompetensi_dasar_id": "64014616-38dc-4ffa-a0ff-4c334a151733", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami pembuatan gambar detail komponen mesin dengan CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:33:58"}, {"kompetensi_dasar_id": "640161cd-bacf-4174-9249-09080dd9f8c4", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Men-set up sensor liquid level", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "640202a4-67bc-41d3-9d4e-13f92dd44e3a", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 807021910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kes<PERSON>han yang terjadi dalam proses pembentukan sebuah komponen pesawat dari pipa (tube)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "64030c9d-9fdd-4c64-8f62-8908a8d7c765", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827290300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi sistem perkembangbiakan rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "640441b7-66eb-46a2-868a-acc6cb4307aa", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara hidup sadar dalam kehidupan sehari-hari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:57:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:13:57"}, {"kompetensi_dasar_id": "640472f3-fcad-418f-908c-ff6e38455274", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827250100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, menalar dan menyaji analisa usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "6406839e-d210-4685-abd6-401053327d21", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803080900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rang<PERSON>an  kendali digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "6406a184-2230-4f9c-abb2-ff2edaa9f386", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (K3LH)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:10"}, {"kompetensi_dasar_id": "6407cbc5-1912-4310-8cbc-af2695f32db7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:10"}, {"kompetensi_dasar_id": "6408f1cb-c1c9-46bf-b998-c192d19e0161", "id_kompetensi": "2.3.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami musik kreasi berdasarkan jenis dan fungsi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:26", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:26"}, {"kompetensi_dasar_id": "6409b1d3-64b2-427f-bfe3-37a781463964", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 825063500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON> pengemasan hasil produksi susu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "640a4d95-2581-463f-ac87-35bf3f71ab7b", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 828020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "640ba224-a8a6-47ac-9095-36f7e0224597", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ragam gerakan tari tradisi putra bagian awal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "640c9e1e-64cf-437f-885b-549112fa5b88", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Memahami public area section", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "640ce164-8015-4157-b2c9-d3f7edebc419", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 824050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kerabat kerja produksi drama", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "640d1d9f-817a-4048-b85d-221c4a901f83", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:40"}, {"kompetensi_dasar_id": "640d24af-17d1-4ea1-aa4b-8aa112a8dc01", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perhitungan untung rugi kegiatan budidaya perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "640d5988-b560-42d3-8cda-7b6a8a3ce98e", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 803070810, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perintah untuk mengakses input dan output port digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "640d8c0b-da34-4c3b-83d4-9ab5b1bc12d1", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 829020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pengemasan dan menyi<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "640da8b2-6530-42e6-9c92-e01158377f9b", "id_kompetensi": "4.35", "kompetensi_id": 2, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Membuat komponen-komponen sistem aplikasi pemanfaatan PLTS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "640fd8e2-89e5-414b-8e66-a5ece2b18dc0", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 823030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>r proses dan hasil rancang bangun reaktor biogas skala rumah tangga/ atau dan komunal yang dilakukan secara mandiri, efektif dan kreatif sesuai K3 dan di bawah pengawasan langsung", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "6410d6e9-a5f8-4b45-8869-7e9112c80f06", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menetukan masalah kontekstual yang berkaitan dengan transformasi geometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:20", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:00:24"}, {"kompetensi_dasar_id": "6410d810-ab8d-4b2e-978a-de074ec63d3b", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401141600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami formulasi dasar sediaan obat cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "64111bbb-5d4d-486e-add3-795af6e47244", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843080600, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan bentuk gerak wayang binatang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2019-06-15 15:07:31", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "64127b89-70c5-4bf2-84a4-d04e4cb78bf1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> konsep momentum, impuls dan hukum kekekalan momentum", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:20", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 14:59:15"}, {"kompetensi_dasar_id": "64138a69-319a-4f76-b994-f76db3a35914", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843090900, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah  iringan tari berdasarkan tema garapan tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2019-06-15 14:58:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "6416f425-5f07-4e90-a6e1-6e5863281dcc", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:34"}, {"kompetensi_dasar_id": "6416fd08-2217-4c43-b482-619f72e007be", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati macam-macam kelompok penyemenan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-10-18 06:44:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "64180983-2b9e-4acf-89bb-6f8cad53a63a", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 817040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengambilan contoh Gas   Alam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "641a679e-82df-4d79-9394-5463bb13895c", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 827140200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis ikan berdasarkan sistem syaraf biota air", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "641abcb4-3fa1-4bad-b9f2-1df4f2cb8c12", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis-jenis dan fungsi peralatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "641af736-388d-4666-9f0b-efef4a0ecd6d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:24"}, {"kompetensi_dasar_id": "641b8d9d-c5a5-4608-8e73-26c3d7c47100", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis tentang budaya politik di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:45"}, {"kompetensi_dasar_id": "641bee31-5be9-46a3-9e8b-5649e0c0c590", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:58:08"}, {"kompetensi_dasar_id": "641c00b6-c5ab-4f36-87bd-9c6725128987", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 840030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat dekorasi claybody plastis dengan teknik relief.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "641c03c6-988e-4fae-96d6-6cf6cf4bd7a9", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan energi  dan bahan penunjang dalam suatu industri kimia berdasarkan azas kekekalan energi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:24"}, {"kompetensi_dasar_id": "641c182c-fc9f-46df-aa81-4848bf58303e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:22"}, {"kompetensi_dasar_id": "641c406c-ae03-41be-911c-32da830b913d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur penebaran benih pada kegiatan pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "641f2c80-68fb-491f-ac13-813f93819bde", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:55"}, {"kompetensi_dasar_id": "641f3d78-1b36-4465-9c84-64f4600c2673", "id_kompetensi": "4.1.2", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON><PERSON><PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159 dengan lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:56", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:56"}, {"kompetensi_dasar_id": "641f8023-dded-417f-8b1c-fedfc7861c98", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 820070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> kerja tim (team work)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "6420564b-b88f-458a-81fd-a03029a6134c", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 817120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> prosedur diagram alir proses pengolahan migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "642238c1-85ed-4706-8248-89cb67d8a89c", "id_kompetensi": "Prinsip-prinsip dan konsep <PERSON> dan <PERSON><PERSON> ", "kompetensi_id": 3, "mata_pelajaran_id": 800000139, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu men<PERSON> pen<PERSON>tian akunta<PERSON>, tujuan pencatatan akuntansi, pihak-pihak yang membutuhkan informasi akuntansi, prinsi<PERSON>rinsi<PERSON> akuntansi serta konsep Akuntansi Dasar dan <PERSON>. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:22", "updated_at": "2022-11-10 19:56:59", "deleted_at": null, "last_sync": "2022-11-10 19:56:59"}, {"kompetensi_dasar_id": "64236be8-0496-4700-9bbb-98b36026aff6", "id_kompetensi": "4.31", "kompetensi_id": 2, "mata_pelajaran_id": 803071100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengu<PERSON>r tegangan dan bentuk gelombang perangkat sistem telepon", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "64238688-3d9e-46b3-a543-95e40357faab", "id_kompetensi": "4.43", "kompetensi_id": 2, "mata_pelajaran_id": 803071400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan kegiatan pemeliharaan VSAT", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "6424bbfd-f24f-4fc2-a75a-0958c60692e0", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 401251190, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis pencatatan penerimaan dan pembayaran bunga", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "6424e6b3-e541-4c0b-9471-d13a6a123b4a", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 817120110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembuatan laporan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "6425fb8e-e0f3-4e45-a758-0fd6a3f42329", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825021300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menyajikan peluang usaha per<PERSON>nan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "64263e82-a7b0-40f2-9405-8792a94c7ace", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur perawatan dan perbaikan jaringan air bersih", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "642663bf-634e-4e58-b1be-3ef23a06efe8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:34", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:34"}, {"kompetensi_dasar_id": "6427d471-df14-4ba7-825f-45a1cbab321e", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:26"}, {"kompetensi_dasar_id": "642880ae-6e32-4942-bae6-fd778314bebe", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:20", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:20"}, {"kompetensi_dasar_id": "64288369-2ff4-4167-b341-1dd5929d1ee9", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:24", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:24"}, {"kompetensi_dasar_id": "6428c3db-58d4-4274-aee0-ddc5dc7a7713", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843061600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode pelatihan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "64292d73-8a65-4635-9ea0-9d23a6e672e4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "64296532-4334-4206-9a42-206d8012a967", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 832030100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melukis dekoratif modern dengan beragam media", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "6429c2c1-df6a-4337-ab18-d3e86104ce61", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825210400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip pengoperasian peralatan pengolahan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "642c6665-8cf8-4d06-a801-99a5e3da6e62", "id_kompetensi": "4.28", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan  powerplant removal dan installation", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "642cb62d-1645-43e1-acc7-cf5ed1387768", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks yang menyatakan fakta dan pendapat, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:22"}, {"kompetensi_dasar_id": "642e19ab-ade6-4191-9c8c-8c6f65e48e4a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>r nilai-nilai demokrasi pada konteks lokal dan global mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:08"}, {"kompetensi_dasar_id": "642e97bd-135c-4931-bcc9-b9c49c7e099a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:30"}, {"kompetensi_dasar_id": "642ec327-ef61-42b2-8a9d-f65c5eb8e9df", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala Antilock Break System (ABS)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "642f0ad8-2f61-4d12-b3e1-2e5d3b4aeedf", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:37", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:37"}, {"kompetensi_dasar_id": "642fbc67-0eb3-46b2-b30b-f4fba6d19401", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pemesinan frais kompleks", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:53", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:08:53"}, {"kompetensi_dasar_id": "64303200-cd52-4aeb-ac70-62202f0776a1", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 825061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pencatatan/recording data produksi telur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "64308b9d-e240-43cb-9428-2391d3ae9bf1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 805010300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> ruang ling<PERSON>  pek<PERSON>an survei dan pemetaan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "64308c46-740f-4b39-b524-a9f2a102d5d3", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan akatuator pnumatik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "6431c582-f58d-481e-b2c8-12ef8fd8db27", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825110100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengoperasian  /pemasangan /penyetelan alat mesin pasca panen (pendingin/pengecil ukuran/pe-ngering/RMU)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "6433129c-f45f-4f95-9433-3d2ff7ecb59c", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 817020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi Hu<PERSON>n antara tekanan-volume-temperatur pada <PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2019-11-27 00:27:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "6433919f-8ec8-4a50-9fac-6d04af4df519", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "6433cd59-1303-472b-a364-63ec7569267d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:53"}, {"kompetensi_dasar_id": "6436659d-1d6f-4a12-9aa4-9a6f57b50451", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan badan dengan masker alami ( buah dan sayuran)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "6436a900-4947-43ce-b8ad-173b675370e6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 822020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami penggunaan satuan dasar listrik menurut sistem internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "6436d9b1-0baa-4bd9-a9df-25959108a35a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 819030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis dan menerapkan organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": "", "user_id": "f593f0fd-51a9-4e0d-b08c-877b5fb2bf95", "aktif": 1, "kurikulum": 2013, "created_at": "2019-06-15 15:27:28", "updated_at": "2019-06-15 15:27:28", "deleted_at": null, "last_sync": "2019-06-15 15:27:28"}, {"kompetensi_dasar_id": "64371b24-9ff2-42cc-9e68-9f5114abbfbd", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON><PERSON> unsur unsur desain: titi<PERSON>, garis, arah dan bentuk dalam gambar 2 dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "64374e09-c1f9-4ca9-a915-f329c3826481", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:07", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:02:07"}, {"kompetensi_dasar_id": "6437b7ec-7250-42e0-ba96-51b62a7346af", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825060900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan penanganan aneka ternak yang sakit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "6438ceb0-ed5b-4a31-ada5-becaf263a386", "id_kompetensi": "Prinsip dan teknik kerja laboratorium pengujian mutu hasil pertanian ", "kompetensi_id": 3, "mata_pelajaran_id": 800000131, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>da akhir fase E, peserta didik mampu memahami prinsip, prose<PERSON><PERSON> pen<PERSON>, dan perawatan alat gelas (gelas alat ukur volume, gelas wadah, gelas aparatus destilasi, aparatus  ekstraksi, aparatus filtrasi, aparatus titrasi, gelas wadah, gelas reaktor/pencampur, dan alat gelas penunjang), alat bukan gelas (neraca analitik, oven, waterbath, tanur, inkubator, autoclave, fume hood atau fume-scrubber, hot plate, bunsen atau burner, Laminary Air Flow/LAF), penggunaan bahan kimia pereaksi dan standar (pembuatan larutan dan standardisasi larutan), teknik kerja aseptik, sterilisasi peralatan dan sterilisasi media, serta penanganan limbah laboratorium. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:21", "updated_at": "2022-11-10 19:56:58", "deleted_at": null, "last_sync": "2022-11-10 19:56:58"}, {"kompetensi_dasar_id": "6439b9d8-a327-4a07-ac70-e4355769455c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843061400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> fungsi seni karawitan dalam masyarakat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:16"}, {"kompetensi_dasar_id": "6439f4a8-3382-49ec-ac9e-632a3a5bf31e", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 807020610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara pembentukan sambungan-sambungan pipa  Hydraulic instalation", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "643a0236-6afe-4722-82d0-bb395a04af8b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:26"}, {"kompetensi_dasar_id": "643b422e-5c34-4ea7-8a2e-9f417a982331", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 801031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengelompokkan lanjut usia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "643dbd06-9446-4349-891c-2d349cab831f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:56"}, {"kompetensi_dasar_id": "643edb3d-5388-48d9-ade7-37ca0ce3f129", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 814010101, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi resiko bahaya untuk mengurangi  kecelakaan kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "643edc5a-d8ea-4b67-8b96-3c2fc994cdac", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 820030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem <PERSON>in", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "643f7ba8-cc4a-4d59-b548-94c65df6d94b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 807021700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan autopilot", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "643f9c7f-2584-48ac-979c-ee078022a7ea", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda datar (survace grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:03", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:03:03"}, {"kompetensi_dasar_id": "643fc547-5ab7-4756-99ef-f5f7e8ea8cb9", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi listrik dengan k<PERSON>, cable ladder dan cable tray/trunking.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:49", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:49"}, {"kompetensi_dasar_id": "64401fdb-7dd6-49e3-a245-4884a440f944", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 820140200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pengaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "64406130-35c8-4fda-8f08-0dfee6f822a4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820110100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami wiring diagram sistem penerangan alat berat sesuai dengan buku literatur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "64419e79-4ae0-47d8-8c28-78348ee8b0f6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:16"}, {"kompetensi_dasar_id": "64434a9d-f12b-42ce-9acf-2daff3c5dd5f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> macam-macam jenis dinding interior kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "6443a66e-b74b-4bc3-afb3-812cfc7dc4be", "id_kompetensi": "3.40", "kompetensi_id": 1, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan testing and commissioning system HMI", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "6445f916-b8ff-4819-bda9-4f4b370ed85a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804120300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengelasan pelat dengan pipa pada sambungan sudut posisi vertikal dengan las gas metal (MIG/MAG)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:24", "updated_at": "2019-11-27 00:29:24", "deleted_at": null, "last_sync": "2019-11-27 00:29:24"}, {"kompetensi_dasar_id": "64464a33-f250-483d-8744-9a35f9a5dd19", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan energi  dan bahan penunjang dalam suatu industri kimia berdasarkan azas kekekalan energi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:56"}, {"kompetensi_dasar_id": "6446b2d6-7ef6-4f2f-9442-9d9a6a0c91cc", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:43"}, {"kompetensi_dasar_id": "6446beaa-5cbf-4c30-ab3a-f0038cb0a42b", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> filter analog", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "6446d5bd-83fc-4754-b4fd-8c96129666a0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengadministrasian fasilitas dan aktifitas laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:13"}, {"kompetensi_dasar_id": "6446ee69-1498-42c1-b40a-465e66ec712c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 843062300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan tata irama dalam sajian karawitan etnis nusantara mandiri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "64473af7-1e89-4f09-acd8-c7fe03e588ca", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:17"}, {"kompetensi_dasar_id": "6447bf5a-233c-4ed9-9f5e-94f76060967c", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 827090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan cara pengoperasi kan alat tangkap pancing tonda", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "6448192a-6144-48f3-830a-c256c2c3f056", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pengoperasian Penerangan Jalan Umum sesuai dengan PUIL", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "644843f4-5933-472a-9eab-c97d3721637d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dan identifikasi proses absorpsi dan adsorpsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "64488929-7a8d-4001-ae69-c5881a1dec2e", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:00", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:00"}, {"kompetensi_dasar_id": "6448b325-bfa8-498d-9219-2bbdef08247a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 814080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menentukan faktor perlakuan pendahuluan pada penyimpanan penggudangan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "644aa2e7-ef98-4a16-832f-ef0fb50ed7ca", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> spesif<PERSON><PERSON> dan karakter<PERSON>ik baja. ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:05", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:05"}, {"kompetensi_dasar_id": "644af50d-8eb5-4e1e-8363-c39f27e7293c", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perbedaan sistem proses terbuka (batch proses) dan tertut<PERSON> (continues proses) pada industri kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:45", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:45"}, {"kompetensi_dasar_id": "644c39c5-57cb-451c-b86a-c86d34aef2ef", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825220200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis pengujian mutu bahan nabati dan hasil olahnya secara mikrobiologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "644c7de9-ebf7-4768-bdc2-6ef5fad8f5dd", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyiapan sampel dan standar analisis kromatografi kertas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:34", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:00:34"}, {"kompetensi_dasar_id": "644c851b-f0bc-4bb1-8094-5d7e3f107715", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:33"}, {"kompetensi_dasar_id": "644d96d8-6418-4139-a746-b69bdd3c0447", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeliharaan ternak ruminansia perah periode dara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "644ddca5-fa89-4157-828a-736df3964424", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 822010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pekerjaan bengkel berdasarkan keselamatan dan kesehatan kerja (K3) menurut standar danundang-undang regional (nasional) dan internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "644e8dc2-fdf4-4fb0-bca9-0a1d0050546c", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:01"}, {"kompetensi_dasar_id": "644f6081-c293-4e12-a2c0-c13e02c2ae5c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:01", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 16:05:01"}, {"kompetensi_dasar_id": "64504340-f632-4e99-bf12-55e52246773e", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola kualitas air pada pemeliharaan larva ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "645074e8-04d4-4cdc-8e7d-288e63b6e9a0", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 824060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  rundown Talkshow", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "6450859a-46a8-4b49-9d11-f3fc7e21da2e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "6450c2ff-246c-46eb-b9ea-9750fa9a0724", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:40"}, {"kompetensi_dasar_id": "6450f883-3d51-4bcc-bbc6-889ed510abc8", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 825060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil produksi pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:02", "updated_at": "2019-11-27 00:28:02", "deleted_at": null, "last_sync": "2019-11-27 00:28:02"}, {"kompetensi_dasar_id": "64529ffe-201c-4ce8-83b5-30073739acd4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:33", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:33"}, {"kompetensi_dasar_id": "6452be08-3d07-43c5-b0f9-ea8e1b5a6a99", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cetak coba/ proof image.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "64552a41-b431-41c1-b4b8-7b72f11f2a75", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis tentang dinamika kehidupan bernegara sesuai konsep NKRI dan bernegara sesuai konsep federal dilihat dari konteks geopolitik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "645542fa-2c11-4a5a-8d0b-48e17c4fe7ac", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 802031200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami animasi karakter 3 dimensi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "645553de-4ebf-4364-be01-d7a6acffafd1", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "64557481-20fd-4c50-9311-9fb70795d06a", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804111100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami Prosedur Perawatan peralatan Pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "6455f3ea-c228-4eff-9436-eca2a7c6075b", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 803060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> rang<PERSON>an penguat daya, VU-meter & protektor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "6456e72a-bbe5-4675-a354-36697de60c9a", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 804040110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan konsep time schedule dan kurva S", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "6457a18e-fbb7-48e2-b8d1-89a6fe22d952", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:53"}, {"kompetensi_dasar_id": "6457a7b0-4352-4734-bbac-9b70ca388328", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "6457e54d-f24f-4ded-9865-49c8ba9610fe", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401110220, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data hasil pengamatan  perubahan lingkungan dan kesehatan sebagai dampak polusi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:55", "updated_at": "2019-06-15 14:49:55", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "64583257-4782-4f95-8f98-6f559aa258a0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843060300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi teknik memainkan iringan tari dengan menggunakan pola  irama, tempo dandinamika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:29", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "6458a2f1-d0b9-4003-9013-7dbc9bea03f6", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis program molting", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "64592794-655f-4da9-b3b5-9df62dac2937", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807021500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami gambar reproduksi rang<PERSON>an listrik dan elektronika dengan teliti dan cermat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "6459850b-f23a-4af1-8ac8-59dab8329274", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memresentasikan Tri Purusha sebagai manifestasi Ida Sang Hyang W<PERSON>hi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "64599da0-e1a8-4db6-9f6f-4ff4ee1edde4", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:33"}, {"kompetensi_dasar_id": "6459e16d-a879-4af2-aecc-7b256b780c11", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 802040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil atribut cetak (tanda potong, tanda lipatan, tangga keabuan, color bar, tanda register) pada hasil susunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "645a13e3-0f5d-4274-9806-d7e69afa6d74", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON>ami dasar-dasar system hidraulik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:52", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:52"}, {"kompetensi_dasar_id": "645b7b1c-ccb5-44f0-9f88-6f566d016ad2", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelompokkan kebutuhan dasar manusia sesuai tingkat usia menurut te<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "645bbc07-e1b5-420f-b04e-67bb17218f6a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam power tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "645cd841-3443-4a41-98d2-ba68628577a0", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:10"}, {"kompetensi_dasar_id": "645de84f-7b1c-4e4d-b2a4-488091e8082f", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat-sifat transformasi geometri (trans<PERSON>i, refleksi garis, dilatasi dan rotasi) dengan pendekatan koordinat dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:15"}, {"kompetensi_dasar_id": "645dfa92-c080-4e6d-8dd1-3ccdbfa46d75", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 818010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode sampling dalam pengambilan contoh batuan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "645f435f-b9ae-4c3c-b066-bb7b41d740dc", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:50", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:03:50"}, {"kompetensi_dasar_id": "646104d2-80f4-4fd2-a18c-a170591ac2e7", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 827110340, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> syarat dan sifat bahan pelumas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "64617e3b-e612-4909-b403-4fa97058cd62", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 817090300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemboran tegak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2019-06-15 15:00:03", "deleted_at": null, "last_sync": "2019-06-15 15:00:03"}, {"kompetensi_dasar_id": "6462d925-bedb-4806-88c0-147e7366c8c6", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 820090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggantikan filter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "646506ee-2af8-4156-adba-47cb76a7fb8d", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843061300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi sistem penotasian tabuhan instrumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2019-06-15 14:58:17", "deleted_at": null, "last_sync": "2019-06-15 14:58:17"}, {"kompetensi_dasar_id": "6465838e-17ae-4e24-bd8f-2f034200d860", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 804010700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyiapkan perangkat keras untuk CAD 3D sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "6465a3c5-8a25-4064-a4ef-0239ee29f898", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan parameter pemotongan mesin gerinda datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:33", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:33"}, {"kompetensi_dasar_id": "6466be94-a1e6-4991-89b7-1937e526f688", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 832020100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sketsa sebagai catatan visual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "6466d2e7-e7d9-4ac8-a146-ba24cfd7b3a4", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 827090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prinsip-prinsip sistem HACCP", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "6467e2ae-3f01-4da5-ad4e-c1fec5bc80e5", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi output gambar CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "6468da58-4169-48e6-9ad1-6943adcc0170", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 825270100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengujian mutu produk olahan dari hasil perikanan selain ikan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "6469298b-5371-448c-8079-de35423cb925", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:33"}, {"kompetensi_dasar_id": "646ab8f2-98ba-4440-b12e-6163532a81b0", "id_kompetensi": "3.42", "kompetensi_id": 1, "mata_pelajaran_id": 814031800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan prosedur penerimaan barang dari pengirim/ pemilik barang kepada perusahaan angkutan perposan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:06", "updated_at": "2019-11-27 00:30:06", "deleted_at": null, "last_sync": "2019-11-27 00:30:06"}, {"kompetensi_dasar_id": "646bbd40-3bce-4293-8f49-dc9d267a6dd2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:45"}, {"kompetensi_dasar_id": "646bcb47-3bd5-4e88-93b3-a2ca4c5ae1bb", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300310100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengungkapkan teks sederhana terkait nama hari (as<PERSON> <PERSON>-a<PERSON><PERSON>), b<PERSON><PERSON> (syuhur al- hijriyah/miladiyah), nama waktu dalam hari (shobah, nahar, masa, lailah), waktu dalam bentuk angka (sa’ah), tangg<PERSON> (tarikh), dan tahun (sanah hijriyah/miladiyah), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "646caa50-299e-4197-9719-09fbbb1f70f3", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 816010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil proses pada mesin tenun tanpa teropong", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:50", "updated_at": "2019-11-27 00:28:50", "deleted_at": null, "last_sync": "2019-11-27 00:28:50"}, {"kompetensi_dasar_id": "646cc941-f94d-4e6d-8d82-dd20ca26dbd4", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:11"}, {"kompetensi_dasar_id": "646f41f3-f253-4a3d-8581-4c21433fe393", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami sistem perawatan dan perbaikan jaringan air bersih, air kotor, dan saniter.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:19", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:19"}, {"kompetensi_dasar_id": "646f962c-96c2-4bef-873c-60eced71bf10", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 807021920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON> bahan tambah dan gas pelindung pada proses las GTAW sesuai dengan material", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:47", "updated_at": "2019-11-27 00:29:47", "deleted_at": null, "last_sync": "2019-11-27 00:29:47"}, {"kompetensi_dasar_id": "647082e0-0b07-47e9-8832-68d8277888f5", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 839080100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis produk dengan teknik jahit tindas pengisi lembaran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "6470caef-4e0a-43f2-9b4e-617fe8461053", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827391000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain  electrical motors", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "6471b5ff-2732-4036-8609-c29367eea769", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar penggu-naan kontrol suhu dengan sistem mekanik, elektrik serta  hubungannya dengan interface dalam pembuatan sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:18"}, {"kompetensi_dasar_id": "64722951-e668-48ce-a6cb-388599af5d99", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 801040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membimbing lanjut usia yang mengalami gangguan proses berfikir", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "64728cea-ab29-47c1-9fd7-55dc43151660", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kemajemukan bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:47", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:47"}, {"kompetensi_dasar_id": "6472b303-14d1-46d0-be62-c623dba0fcd4", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825062000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   <PERSON><PERSON><PERSON><PERSON> laporan hasil pemasaran produksi aneka ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "647352db-ed10-4002-a0b0-264671fc2184", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800030100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis alat-alat di laboratorium dasar kefarmasian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:10:19", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 15:10:19"}, {"kompetensi_dasar_id": "6474954e-1f48-4480-bad4-5117c1b89d76", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> prinsi<PERSON>, konsep dan prosedur khromatogra<PERSON> kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:38", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:27:38"}, {"kompetensi_dasar_id": "6474af3c-a8a6-49ed-83cc-c2b48559e297", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 831080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melak<PERSON><PERSON> per<PERSON>ungan dan penetapan harga jual busana rumah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "6476122c-8e2b-416c-b1b9-cd9e7adbfa77", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825250600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan rekayasa wadah pendederan untuk daerah perkotaan yang ramah lingkungan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "64767408-ce33-4900-82ac-52668e762a15", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 825200100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan milk borne disease", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "6476ddae-2c77-45ce-9583-ddae810c8d28", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 807021720, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merangkai alat control pesawat udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "6477099a-e8c3-4768-aef0-9f1679b2d475", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemrograman programmable logic controller (PLC) pada sistem kendali otomatis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "6477d427-bc2a-4f27-a5cf-c0c12d2a3de9", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401130910, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil pengujian fisika bahan tekstil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "6478632f-5af3-4009-a2f0-ba3c11188086", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:36", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:36"}, {"kompetensi_dasar_id": "647b18b2-99cf-4527-aefa-2b27c9ff72fb", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 825230200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Merancang kegiatan unit produksi produk baru hewani yang bernilai jual dan berdaya saing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "647b222f-9330-42d0-9140-429739a48459", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 826110100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi strategi pengel<PERSON><PERSON><PERSON> (DAS)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "647b3bb7-b53a-40f9-bdab-d349211b669f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:32", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:32"}, {"kompetensi_dasar_id": "647b6f17-5a5d-4989-8c47-d6a18af55a9b", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis kapasitas kerja alat mesin pengendali hama dan penyakit (duster) disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:49", "updated_at": "2019-11-27 00:28:49", "deleted_at": null, "last_sync": "2019-11-27 00:28:49"}, {"kompetensi_dasar_id": "647bb2d9-08c5-4e9b-b14e-45e177ca1e25", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 817140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan  proses pemisahan gas pada scruber dan frak<PERSON>asi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "647becae-cdf2-428b-a582-d378994160a7", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 831030300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik mengukur tubuh", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "647bfa85-072a-4949-a69a-39ad8f0f6297", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dan prinsip titrasi dalam proses  titrimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:07"}, {"kompetensi_dasar_id": "647c3551-9510-4203-8570-a099e6278bfe", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825061200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan tentang penanganan limbah hasil pembibitan ternak unggas.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:07", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:07"}, {"kompetensi_dasar_id": "647d341a-5060-4732-81db-2f8a42a49679", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis transaksi jurnal penutup antara lain menutup biaya overhead pabrik, menutup selisih biaya overhead pabrik ke rekening Harga Pokok Penjualan, menutup rekening nominal dan rekening lainnya (Harga Pokok Pesanan/Harga Pokok Proses)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "647e4845-65ae-4648-b79e-958cb67a59d8", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 830090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan sanggul Fantasi sesuai k<PERSON>si", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "647fa10e-cca4-41b1-94b2-23ef6ff5291a", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 807020520, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemakaian material untuk pengelasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "6480b8d7-3c3d-4719-8d92-81ef9aceadb8", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 834020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan proses pembuatan patung dengan teknik konstruksi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2019-06-15 15:07:25", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "6481ac0e-6e5b-4032-8c81-9a0d5fafab55", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis standar operasio nal prosedur Traktor Pertanian Roda 2", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "64829007-f00d-441d-b0df-2cb64ee56176", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 838040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menelaah ergonomi desain furniture ruang publik komersial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2019-06-15 15:07:25", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "6482f1dc-3c3e-4b0d-9de7-2d8dcf9e8336", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 821170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pelat baja dengan proses las GTAW pada posisi over head/atas kepala (4G)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:22", "updated_at": "2019-11-27 00:29:22", "deleted_at": null, "last_sync": "2019-11-27 00:29:22"}, {"kompetensi_dasar_id": "648312c4-fda8-431a-bd0a-549167784c1f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 828090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Membuat uraian tugas setiap tingkatan jabatan pada kegiatan administrasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "64832917-b59f-4979-ac8a-3efeca0b0157", "id_kompetensi": "3.7", "kompetensi_id": 2, "mata_pelajaran_id": 827150200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hubungan antara lingkungan (media air), komoditas dan penyakit pada biota air", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "6483a8e8-49ea-4710-8bc8-bb27c2a108b2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401251060, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pajak penghasilan yang bersifat final berdasarkan ketentuan perundang-undangan tertentu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "6483bde2-9589-40c9-82bb-80453303e6af", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran Indonesia dalam hubungan Internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:17"}, {"kompetensi_dasar_id": "6483c0bc-5a30-4a21-904b-55daac5e120a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:30"}, {"kompetensi_dasar_id": "6483d0c3-ac72-49c6-87c8-5e9843ab3d02", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan workshop equipment", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:34", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:34"}, {"kompetensi_dasar_id": "64866162-b3ac-42e3-8f67-6acca1e8bd19", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis konsep desain/\r\nprototype dan kemasan produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:17"}, {"kompetensi_dasar_id": "6486fecf-e460-4089-843f-a670fa20a0f4", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 820130200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki gangguan pada miror", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "64896452-4423-4f9f-aa71-45abf0836aff", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:00", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:00"}, {"kompetensi_dasar_id": "648a6937-c1d7-4537-ad77-5f3aa935a8ca", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami system koordinat pada gambar CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:37", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:37"}, {"kompetensi_dasar_id": "648bb074-7327-4aae-b14b-1785e5bdf887", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis macam-macam budaya politik di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:19"}, {"kompetensi_dasar_id": "648cdef9-fc01-4102-9bc6-c28005a830e7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar proyeksi orthogonal dan isometrik dari objek gambar atau benda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "648d4d5a-fef9-496e-9b20-1946f9de1379", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, konsep dan prosedur khr<PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:38", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:27:38"}, {"kompetensi_dasar_id": "648dd53a-1824-4a16-ab5d-c10cda066372", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 802040500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyu<PERSON>an huruf dan ilustrasi newsletter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2019-11-27 00:27:36", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "648dee7b-de58-44a5-bb5b-0b4d24492fe0", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843060410, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi teknik bentuk dan isi gerak tari putri berdasarkan tingkatan karater", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "648e26c5-7b9b-4087-b0a3-b7914417de6e", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemeliharaan sistem kontrol pada unit pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "648e94cd-ba9e-45bd-8e97-7769edf0c3fd", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> rencana pengambilan contoh benih tanaman hortikultura/ perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:47", "updated_at": "2019-11-27 00:27:47", "deleted_at": null, "last_sync": "2019-11-27 00:27:47"}, {"kompetensi_dasar_id": "648ec055-d67e-4afa-9819-f72f0fad5ab0", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate arrangements for towing and being towed", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "648f295a-7086-403f-a68f-1b2e1f5555b4", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825020400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> pemasaran hasil tanaman hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "64906dc3-af78-4d68-b3eb-4d6c8c7fb7a3", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis k<PERSON>aka<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "6491aa1b-5169-45a1-a64c-1a93dbdd2f56", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 802031400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> tata cahaya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:00", "updated_at": "2019-06-15 15:07:00", "deleted_at": null, "last_sync": "2019-06-15 15:07:00"}, {"kompetensi_dasar_id": "6492b6ea-9064-48ff-9347-e16a686df4a8", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON>h komponen kendali pnumatik yang akan dirakit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "64937635-82b8-45c4-9521-7185f8619ec1", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "64937e81-6666-4adf-a42a-dbfd3801c8ac", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa sistem kontrol pada unit pembangkit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "64938d17-a978-48cf-a09e-9da1efd83004", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat fine cut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "6497291f-d811-42d5-9d16-84bd701483bd", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan PLTM On-Grid pada kondisi padam (Black Out)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "6497a842-af1b-4f85-a2c2-92f502723560", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik analisis secara polarimetri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:47"}, {"kompetensi_dasar_id": "6497cbbe-9c6d-494c-880c-f7c6978b274c", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 803071100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknis perawatan perangkat penunjang telepon seluler/BTS", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "64981002-244b-449e-9869-cd2f78338122", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan Differential", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2019-11-27 00:27:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "64985d2b-419e-4c37-bcf6-bc87a67604d1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827350500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain squat and shallow-water and similar effects", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "6498f1ce-7395-47e4-8464-f582bc0d6b83", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan sterilisasi peralatan dan bahan di laboratorium medik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "64990d94-4920-44b5-b8dc-b79b1b63d17d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:01", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:01"}, {"kompetensi_dasar_id": "6499127d-d198-4184-b2b8-bb5bde6b22f3", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 827080400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bahaya-bahaya pada perairan sempit.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "649a1825-4800-40c1-9ef0-bfd89976a5b5", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251090, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur penye<PERSON>aian transaksi kartu kredit dan penagihan kartu kredit melalui visa internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2019-06-15 15:07:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:15"}, {"kompetensi_dasar_id": "649b716c-30d0-4b01-812b-9e97d71dcb90", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami / Membedakan alat ukur mekanik dan jenisnya dengan berbagai tingkatan ketelitian sesuai dengan peker<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "649d183b-ce5f-449b-b18e-0b5a4ba395b0", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843080400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendalang utuh dalam cerita <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:32", "updated_at": "2019-06-15 15:07:32", "deleted_at": null, "last_sync": "2019-06-15 15:07:32"}, {"kompetensi_dasar_id": "649dc47b-7a65-4094-931e-1bc39740e39e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825210700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan persiapan media pendederan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2019-11-27 00:29:23", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "649e3932-ec30-40e0-8fd5-bc3f35a14b36", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401141600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Me<PERSON>ami prosedur <PERSON> standar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "649f9af2-9819-4277-a824-a7b77aad19cd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 814150100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>las<PERSON> pengendalian proses produksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "649fd680-a1bc-41be-9bea-84e5275580b2", "id_kompetensi": "Profil technopreneur, job profile, peluang usaha dan pek<PERSON>/profesi di bidang animasi ", "kompetensi_id": 3, "mata_pelajaran_id": 800000149, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu mendeskripsikan technopreneur/pelaku wirausaha dalam bidang animasi, peluang usaha di bidang seni dan ekonomi kreatif yang mampu membaca peluang pasar dan usaha, untuk  membangun visi dan passion, serta melakukan pembelajaran berbasis projek nyata sebagai simulasi projek kewira<PERSON>. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:24", "updated_at": "2022-11-10 19:57:00", "deleted_at": null, "last_sync": "2022-11-10 19:57:00"}, {"kompetensi_dasar_id": "64a1f7a0-f764-4e56-a7e5-b29a89dea149", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 825250400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi cara pemeliharaan ikan di tempat karantina jalur udara dan laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "64a2fc6b-b62e-460a-8d6d-6889a1a1b375", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan ta<PERSON>pan <PERSON> naskah program televisi drama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "64a31177-4298-4bd1-8da6-24eee3d6b2de", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 804011300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar perspektif interior dengan teknik komputer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:38", "updated_at": "2019-11-27 00:28:38", "deleted_at": null, "last_sync": "2019-11-27 00:28:38"}, {"kompetensi_dasar_id": "64a3b76f-f88d-4b53-8e90-2ddded5ebf79", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat dekorasi clay body plastis dengan teknik combing pada benda hasil pembentukan teknik lempeng (slab)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "64a3e58b-6ee6-4e18-8ce0-63ce5083571c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 807021900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis maintenence and repair aircraft parts", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "64a54431-e21d-4b4e-aef2-97df48d9c3d7", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 828190130, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penerbitan invoice tiket penerbangan domestik dan sistem pembayaran pelanggan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "64a633c9-bf87-4c4d-b5a7-22e1e30f93b2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:11"}, {"kompetensi_dasar_id": "64a6885f-a9b1-4578-b101-7dd43db5ef1d", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara hidup sadar dalam kehidupan sehari-hari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "64a8b662-f347-4988-83de-1bad4c527fb5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "64aaf38c-db75-4fb4-8b45-de4614e1adad", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memresentasikan sikap dan\r\nperilaku wirausahawan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:45", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:28:45"}, {"kompetensi_dasar_id": "64abcfbd-1230-44b2-9e95-cb17e1b38d80", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 822090110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki Tire pressure monitoring.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "64abe905-0f2c-4e11-beb8-17fc31efa189", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsip-prinsi<PERSON> dan <PERSON> (K3)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:27", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:01:27"}, {"kompetensi_dasar_id": "64abf86b-6f11-4401-a3e5-bf279b933d79", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> macam-macam kalibrator komponen sistem otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "64ac0ebb-7885-494f-864b-f8c9b0697caf", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 805010500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginput data dalam pembangunan basis data SIG", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "64ac7cec-0d61-43ee-a37b-aaed8a4ed245", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 825020800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Men<PERSON><PERSON><PERSON><PERSON> hasil penanganan limbah tanaman pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2019-11-27 00:27:40", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "64ac867c-1f19-4bbc-8f95-11c494223689", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 814030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan berbagai type layout dalam tata letak fasilitas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "64adcd74-e572-4bd6-af3a-a360455685cc", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 827210400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan treatment pengelolaan kualitas air pada pemeliharaan larva komoditas perikanan hasil pemijahan buatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "64ae002c-27ab-4d60-9cba-2f906cee91bd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi batu gerinda untuk penggerindaan datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "64ae7d6d-95a4-474a-aeee-d62ee72d5788", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membuat sistem koordinat pada gambar CAD 3D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:07:08"}, {"kompetensi_dasar_id": "64af14d8-67b2-4964-8aaf-8d3a3279c3a4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:05", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:05"}, {"kompetensi_dasar_id": "64b04a9b-6f9a-47e1-8315-ed81daa16aa3", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang mencerminkan kesadaran beriman kepada <PERSON>ha dan <PERSON><PERSON> Allah SWT", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:35", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:35"}, {"kompetensi_dasar_id": "64b16029-9f4a-47c9-8cbb-88e109360d16", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "mengevaluasi perencanaan ESP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "64b1cdcb-c3ed-4c5f-b258-f82c4da83270", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> konsep <PERSON>ar sejarah (berp<PERSON><PERSON> kronologis, diakronik, sinkronik, ruang dan waktu serta perubahan dan keberlanju<PERSON>)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "64b4a8bf-e656-4222-9925-e07a7f836cb7", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kemajemukan budaya bangsa Indonesia sebagai anugerah Allah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:44", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:44"}, {"kompetensi_dasar_id": "64b5abd8-38ec-4d47-b2bc-c5bf91d91f46", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil evaluasi dari berbagai media massa tentang peran Indonesia dalam hubungan internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:31"}, {"kompetensi_dasar_id": "64b5e235-ca3f-4116-8bdc-b7d8a9c6e31d", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 805010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membangun data raster", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "64b62010-9790-4f5f-8f99-b068e3db5e60", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami fungsi dan symbol pipa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2022-10-18 06:44:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "64b80456-d952-40da-85f2-a1632151af8a", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan macam-macam dapur/ furnace", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "64b9110c-2eec-4227-9b05-03935ec235f4", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengidentifikasi potensi dan resiko kecela<PERSON> kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:14", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:14"}, {"kompetensi_dasar_id": "64ba1f71-e075-407e-93a1-04283cd2adc6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804131600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON>an Alat dan Bahan teknik cetak pasir, tapel, dan logam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "64bb1d0b-7189-4c88-8462-c2679d65c590", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is macam-macam gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "64bb777f-0996-4807-9982-e5153823b186", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825062400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> siklus reproduksi hewan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "64bbce98-f7dd-46ac-9537-0ba1c1c92ca3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 842040200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses finishing produk kriya kayu dengan bahan politur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "64bcc2bb-495a-4c37-97b0-7a86a47d2c5d", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengendalian numerik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "64bd31a7-c59d-4973-9368-c3e15799ba45", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 832030200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melukis realis objek sosial media cat akrilik dan cat minyak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "64be0820-72c4-438b-b3c6-a604a2a55c7f", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827390700, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain grinding machine", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "64c066fb-c204-4688-83b5-3e579bea589e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 801030800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pelayanan Rehabilitasi Sosial korban NAPZA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "64c0988c-cc1b-4d90-9299-73df02e83f81", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 814032100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "      Mengklasifikasi transportasi multimoda", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:10", "updated_at": "2019-11-27 00:30:10", "deleted_at": null, "last_sync": "2019-11-27 00:30:10"}, {"kompetensi_dasar_id": "64c0a543-2e81-4f40-852f-e0a37783a137", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik analisis secara polarimetri,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "64c1b50e-ecea-49fa-92a1-c2f6b7dee8fc", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 808060620, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar tempat penyimpanan bahan- bahan material komposit ( Storage kepper material )", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "64c268f7-2d52-4ca0-b2aa-8a732f809930", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 827290200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kelayakan usaha penanaman rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "64c3601d-371a-4366-8d1c-4ff3d5c7230b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401251130, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penilaian atas permohonan  kredit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "64c5148a-97f5-46ce-87b1-e3409bb4cd24", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang turunan fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:58"}, {"kompetensi_dasar_id": "64c6032f-1579-457f-9583-8e47ebdf2bc7", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan kontrol <PERSON>, te<PERSON><PERSON>, k<PERSON>mbaban pada proses industri kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:19"}, {"kompetensi_dasar_id": "64c670b1-adbc-44a8-a46d-fc2db24e51e3", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan kurva lingkaran dengan titik pusat tertentu dan menurunkan persamaan umum lingkaran dengan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:58"}, {"kompetensi_dasar_id": "64c6db44-a13f-4987-9b78-676c8340fe67", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 821090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>an pada mesin sekrap", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "64c72eaf-0b2b-4cf2-9d71-301e58430b31", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 828200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Men<PERSON>sun proposal pameran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "64c7e4da-7030-46d4-9996-860cf7e547aa", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil ternak ruminansia pedaging.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:06", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:06"}, {"kompetensi_dasar_id": "64c8ec8d-9951-4471-b8b4-96be17058a30", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 801031100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan relasi dengan anak dan lingkungan sosialnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-12-13 08:07:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "64c90077-4683-4f35-90d2-65778172da28", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 839050100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pelekatan malam pada kain dengan alat cap", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "64c921d7-efe9-4acb-b6e5-4ecfe58dc61e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 831101000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar ilustrasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "64cad899-deae-4f17-9b01-d501dc5fe015", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 843060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis tari <PERSON>t  k<PERSON>ra<PERSON>/ pola gara<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "64cb2446-bdb8-4e8a-815f-cf924b9a9c29", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengendalian proses  pada sitem proses terbuka  dan  sistem tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:44"}, {"kompetensi_dasar_id": "64cc5987-3987-49f0-a1d5-ced8024e1003", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan tentang senyawa organik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "64cd05df-ad18-4ca8-8805-c7055d0ff3e2", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 802032400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan Proses Pek<PERSON> dan <PERSON><PERSON> (K3)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:38", "updated_at": "2019-11-27 00:28:38", "deleted_at": null, "last_sync": "2019-11-27 00:28:38"}, {"kompetensi_dasar_id": "64cdb3c1-70ec-4bc0-aa1e-f4c5a4064603", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:17"}, {"kompetensi_dasar_id": "64ceeb82-9916-4bdc-a136-704f0ea79d0b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:04"}, {"kompetensi_dasar_id": "64cf0a7b-2282-4b5f-846c-3316b290abd1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825280200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengevaluasi peralatan laboratorium dasar mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "64cf7810-4ea4-40c2-97fb-1aa6143e2d32", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jumlah bahan, tata letak dan biaya pada instalasi Perlengkapan Hubung Bagi (PHB) Bangunan Industri Kecil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "64d0bdb5-ca0b-43b0-a5c8-ab2ce45af09d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804101400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis-jenis gangguan pada unit pembangkit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "64d58fcc-f2af-44b7-b3ee-f52ec9ccf1fa", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 839120200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur menyungging produk kulit mentah tatah sungging tiga dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "64d59829-2255-4df7-902b-55e2da3fd90d", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 825050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik kastrasi dan polinasi dalam produksi benih tanaman hortikultura/ perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:47", "updated_at": "2019-11-27 00:27:47", "deleted_at": null, "last_sync": "2019-11-27 00:27:47"}, {"kompetensi_dasar_id": "64d5ceba-0d8c-4187-9172-677b90629f8e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik-teknik Promosi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:17", "updated_at": "2019-06-15 15:07:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "64d66459-2e6d-43d5-a003-2383eb0dddb7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 800020700, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menganalisis pertumbuhan dan dan tugas-tugas perkembangan bayi (0-1 tahun)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:10", "updated_at": "2019-11-27 00:30:10", "deleted_at": null, "last_sync": "2019-11-27 00:30:10"}, {"kompetensi_dasar_id": "64d6bd5f-2133-4df6-b0c9-f6d8a4247af3", "id_kompetensi": "3.83", "kompetensi_id": 1, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai impedansi transformator frekuensi tinggi dan frekuensi rendah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:51", "deleted_at": null, "last_sync": "2019-06-15 15:06:51"}, {"kompetensi_dasar_id": "64d6bf62-8bee-4f2c-8aa2-4ae3e2a85e99", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:48"}, {"kompetensi_dasar_id": "64d71e7f-df9c-4f60-8f69-4e6468d27585", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "64d72da1-c9ee-4bb7-881b-9a8d1137f929", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "64d82afe-ac82-4175-a813-7313d4e34065", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan persiapan kandang dan peralatan agribisnis ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "64d9026b-1455-4ce6-9b29-e2c4d092a3ee", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "64d91459-f685-48be-8811-2532c8339d65", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor yang mempeng<PERSON>hi pembentukan karang gigi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "64dab546-cbc5-47eb-bcc7-186cd1dcb966", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 829010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai jenis dan bentuk rang<PERSON>an bunga", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "64dba697-a806-438a-a785-b95c3ae1dfc7", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 817150110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis program Pengawasan K3LH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "64dbf389-ad1f-4236-a9f4-8c4687736ecb", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan keselamatan dan kesehatan kerja serta lingkungan hidup K3LH  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:04", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:04"}, {"kompetensi_dasar_id": "64dca41a-db6d-4ef0-86f2-55daf06cb3c6", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung tegangan pada struktur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:29", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:29"}, {"kompetensi_dasar_id": "64dda04f-a239-4c9d-8480-6d37091757c9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil susunan acuan cetak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "64ddf4af-3ddc-498a-8fe0-a4d2c7529d37", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:51"}, {"kompetensi_dasar_id": "64df5ee9-6f29-40be-a568-105238dee961", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply precautions when beaching a vessel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "64e0d5c0-9920-48a9-8789-57841d2d81b8", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 808020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami metoda yang tepat dalam menggambar bentangan benda geometri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "64e19137-c5ab-4ab5-936a-ff8eb7814c16", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:46"}, {"kompetensi_dasar_id": "64e19ecb-02b0-439c-b433-916cd2010622", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 843011100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik dasar permainan ansambel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "64e27d12-2207-4e8d-ab84-61a6537e7678", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401131800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur  pen<PERSON>n kanji pada kain  selulosa (desizing) secara manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "64e29812-7db1-4b16-ac63-d5933c424169", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 831080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis marker layout busana industri (busana rumah ) berdasarkan desain dan ukuran", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "64e5b0ef-4438-4b89-83f7-c049980c49ea", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 802010600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah proyeksi <PERSON>, pro<PERSON><PERSON><PERSON>, pandangan stereo dantak sonomi proyeksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "64e7b125-4624-46d7-b7d7-b42302a8238f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> sikap dan perilaku\r\nwira<PERSON><PERSON>an", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "64e80df5-3e36-4061-9b2a-e048f57a34b2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan penghimpunan dana bank syariah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "64e8470c-e61c-4027-b774-3be9c2fde780", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembuatan gambar denah jalan dan jembatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "64e91324-d3e3-4735-b812-f610d09e27d2", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk menyatakan dan menanyakan tentang keharusan, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:28"}, {"kompetensi_dasar_id": "64e962b7-9089-4efe-81a1-57972bc8f959", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 807020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan inspect and adjust flap", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "64e9ff8b-5853-47f2-8285-943283c0e643", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401251170, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelompokkan tahapan siklus akunta<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "64ebb86e-59ff-46a9-8ae3-302bd852aaed", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 401251104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan penghitungan zakat dalam kegiatan muamalah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "64ebe060-9aac-4eac-b0a6-88fcc4fba75d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 824051400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dasar-dasar pen<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "64eceda1-0fad-4af0-99b5-79d3fea2d0b3", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "64ed0ab2-8a5a-469b-98ba-d476c150cab2", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401130610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur sampling  limbah cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "64f01822-d302-4f87-ac78-6667b236a436", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa sistem dan komponen Variable Speed drive (VSD)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:40", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:31:40"}, {"kompetensi_dasar_id": "64f12701-332f-468a-9013-ae1c741b1f27", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401130610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sampling limbah cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "64f1ea5e-2067-4a8e-8ba3-7c7899208d9b", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 800061400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan asistensi berkaitan dengan penyakit Saluran pernafasan pada tindakan medis gigi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "64f2fddf-7e2a-4438-b301-a5a9e333fb2b", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 820040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki ystem bahan bakar diesel pompa injeksi In-Line", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2019-11-27 00:27:26", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "64f35172-befe-40c5-9b32-47aa4687f333", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung besaran listrik pada jaringan transmisi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "64f4a1cf-c733-44b0-81ee-dc3e55515de9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyelesaikan masalah yang berkaitan dengan perbandingan trigonometri pada segitiga siku-siku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:07"}, {"kompetensi_dasar_id": "64f576f3-2d0e-4076-8b08-1e2e61ebf4b1", "id_kompetensi": "3.42", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi fitur-fitur perangkat lunak untuk menggambar turbin dan kelengkapan mekanik pico hydro", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "64f73d7f-e3af-4e2f-9356-b3459b424897", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825021200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan penanaman tanaman <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "64f7cad5-21ff-4091-9038-246600ea6d60", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:36"}, {"kompetensi_dasar_id": "64faa2c3-a799-4f69-885a-164339892007", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 816010600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengobservasi peralatan gerakan tambahan pada mesin tenun tanpa teropong", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:51", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 14:56:32"}, {"kompetensi_dasar_id": "64fac629-6ddd-4838-82a5-9f64b162c320", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 800020600, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menerapkan pengumpulan data", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:10", "updated_at": "2019-11-27 00:30:10", "deleted_at": null, "last_sync": "2019-11-27 00:30:10"}, {"kompetensi_dasar_id": "64fadf94-5754-4125-966f-874770349b01", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 828200110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pendaftaran dan menyed<PERSON>kan materi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "64fc2b9c-194d-47ea-96fd-7088163ee4de", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 808020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  benda geometri gabungan bentuk selinder dengan diameter yang berbeda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "64fd2fc1-024d-4317-8cf9-c793cd1b9c20", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 807021910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kes<PERSON>han yang terjadi dalam proses pembentukan sebuah komponen pesawat berbentuk kombinasi/komplit dari logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "64fe9138-f554-4f16-aed2-b3a224a0d98a", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor-faktor kema<PERSON>an dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:02", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:02"}, {"kompetensi_dasar_id": "64ff3bb3-71dc-4c65-8770-afc1d5993d57", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan komputerisasi file data akuntansi untuk bank", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "64ff6a6f-9009-4b1b-88be-46d92c2e464b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:41", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:41"}, {"kompetensi_dasar_id": "64ffeaf6-8bbc-4da9-90d5-bdcb1db31026", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengidentifikasi potensi dan resiko kecela<PERSON> kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:26"}, {"kompetensi_dasar_id": "6501bfb3-0f5f-486a-b02d-9c5269d587a4", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki ban dan peleg", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "6502fc3f-c973-47ac-aa2d-adde87cff002", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 825220300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengendalikan serangan hama dan penyakit dalam penyimpanan dan penggudangan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "650346f3-4e79-4bdd-873c-f8de57637fc6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan posting jurnal umum ke dalam buku besar untuk perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "6503a249-0e3d-4873-95ec-b5791f99ee43", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 804110600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pengefraisan untuk pembuatan rack dan roda gigi lurus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "6503c892-bf3b-4e3a-805a-607f0ea60222", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:09"}, {"kompetensi_dasar_id": "6504d204-87eb-412b-9a8c-c460c81df501", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi dalam simulasi perlombaan salah satu nomor atletik (jalan cepat, lari, lompat dan lempar)yang disusun sesuai peraturan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:59", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:59"}, {"kompetensi_dasar_id": "65057076-f759-479c-80aa-9078c0213b29", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is macam-macam gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:18", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:18"}, {"kompetensi_dasar_id": "6505bac1-69e9-4ff2-87d8-63e215698430", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> nilai-<PERSON><PERSON>: k<PERSON><PERSON><PERSON>, ka<PERSON>h dan keadilan dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:07:55", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:57"}, {"kompetensi_dasar_id": "6506751c-2612-44d8-b556-0f783ac8bad7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaitkan  data topografi dan ketentuan teknis sebagai dasar perencanaan jalan dan jembatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "6506993d-f0e8-4543-8739-ba3a3c9cabe1", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses fisika dan proses kimia dalam industri asam klorida, sulphur dan senyawa dari sulphur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:10"}, {"kompetensi_dasar_id": "6506f97b-c56c-489c-9a08-ce5e297cba39", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan efisiensi penggunaan alat dan mesin produksi pertanian, laboratorium, klimatologi, penyimpanan dan prosesing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "6507958b-0627-4622-b73d-6fd2ea1f279a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:19"}, {"kompetensi_dasar_id": "6507f2be-c809-4996-9a99-e83c02e48988", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 800050420, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menyusun diit pasien dengan penyakit infeksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "65090a8b-7784-455c-ac3f-df601c2b993a", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 801030800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun program rehabilitasi sosial korban NAPZA yang bersifat rek<PERSON>tif, ed<PERSON><PERSON><PERSON>, dan kont<PERSON>tif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "6509a0e0-c896-4f89-a7d7-30b21efcc229", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 802040700, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasikan segmentasi audien", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "650a46ee-6e62-49a4-bb3c-93bdd01a913c", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sistem operasi sublimasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:36"}, {"kompetensi_dasar_id": "650b11ef-8f42-46ab-9624-26c821e182fb", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 807020520, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> peralatan pengelasan untuk pembuatan jig", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "650d59e7-27d6-44f3-9174-d57fd2ba5074", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804070100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan Pengendalian kualitas bahan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "650e148d-2b92-4ed4-9c15-1f2d81517fcc", "id_kompetensi": "3.30", "kompetensi_id": 1, "mata_pelajaran_id": 820030600, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem turbin gas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "650e2efc-121a-4c26-beb8-c7aa1848e73f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan identifikasi tanaman dan persyaratan tumbuh tanaman sayuran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "650e3a76-1560-49ce-84d2-c1abcb811c31", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "65101bb6-db5f-42f1-88a8-495ef60eb475", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "6510a7e0-f60b-42d1-92bc-0635fb90ba65", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804131700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan problem proses Finishing logam (<PERSON><PERSON> dan lapis <PERSON>) dan per<PERSON><PERSON>nya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "65116ce1-bfd8-4409-bd83-cce0acd2927c", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 802030310, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat dokumen tahap pra-produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "65118bd2-d78a-4118-9074-400375276f38", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 820090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakuka<PERSON> pengu<PERSON>an kinerja pompa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "6511e958-666d-4d49-93e1-fe6e4e23902c", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 839070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi produk dilihat dari nilai teknik, alternat<PERSON> bahan, est<PERSON><PERSON>, budaya dan ekonomi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "6512296d-0cb8-448f-9c5b-0d25b7063735", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:21"}, {"kompetensi_dasar_id": "65126447-c794-48f5-a544-1bfd7c57df82", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem engine manajemen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:58", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:58"}, {"kompetensi_dasar_id": "6514d8d9-f9ec-4485-9e31-2d45d6826e78", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan bedah <PERSON> (nekropsi) pada unggas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "65152e8d-5a31-45c2-9877-bdac7203bc2c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 827210300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis dan fungsi alat dan bahan untuk penanganan hasil panen karamba laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:51", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:51"}, {"kompetensi_dasar_id": "65165759-9ef0-45ea-88a7-5a2e75d5a509", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi sett lokatif, ekspresif dan aktraktif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "65169696-50f2-4f7e-9947-3e0c5dad1f21", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:51"}, {"kompetensi_dasar_id": "6516ae83-8f82-49e2-9f97-21a1230832e0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan panggilan hidupnya sebagai umat Allah (Gereja) dengan menentukan langkah yang tepat dalam  menjawab panggilan hidup tersebut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:03", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:03"}, {"kompetensi_dasar_id": "6516b187-d83d-407b-8890-d5730d0418ae", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 827060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>,tali dan tali baja yang memenuhi persy<PERSON>tan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:04", "updated_at": "2019-11-27 00:29:04", "deleted_at": null, "last_sync": "2019-11-27 00:29:04"}, {"kompetensi_dasar_id": "65185efa-0335-4ecf-aabb-1099bca0b4da", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804101200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistempendingin pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "6519a9d6-49dc-49fb-be4a-3b084f3a0b15", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks il<PERSON>h fak<PERSON> (factual report), lisan dan tulis, se<PERSON><PERSON>, tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, terkait dengan Mata pelajaran lain di <PERSON>las XII, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:28"}, {"kompetensi_dasar_id": "651afd7b-ce58-484f-b73e-6ec9cec03219", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi batu gerinda untuk penggerindaan datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:08", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:01:08"}, {"kompetensi_dasar_id": "651b7265-b6f4-4d96-a341-43e53e994ecc", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 829060600, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dan mengan<PERSON>is rancanganmenu seimbang untuk wanita hamil dan menyusui setelah diolah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2019-06-15 15:07:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "651c84fe-7453-4cb4-a1be-982fa84657ac", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan faktor-faktor kemajuan dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:55"}, {"kompetensi_dasar_id": "651cb3a9-0a38-4427-9d28-0499222c6d3c", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 800020410, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan penyakit sistem persarafan berdasarkan manifestasi klinis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "651d74c2-4917-4776-a87b-d0fa050f8e3a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:30", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:30"}, {"kompetensi_dasar_id": "651dc6b1-9fe6-42ca-b7ad-486fb660e98b", "id_kompetensi": "3.30", "kompetensi_id": 1, "mata_pelajaran_id": 820030500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi sistem kelengkapan boiler/ketel uap", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "651e3806-d21e-4430-b737-8d5d31628af3", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:59", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:59"}, {"kompetensi_dasar_id": "651ea4fc-5927-4228-ad60-af62233d8f6a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:06"}, {"kompetensi_dasar_id": "652066f2-596b-4805-834a-5690c4d8af87", "id_kompetensi": "3.66", "kompetensi_id": 1, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode pencarian kesalahan transistor sebagai penguat daya akibat pergeseran titik kerja DC transistor.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "6521cb46-3ef2-4271-b867-a565995f5c2e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 831060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Membuat desain busana rumah sesuai dengan konsep colase", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:36", "updated_at": "2019-11-27 00:30:36", "deleted_at": null, "last_sync": "2019-11-27 00:30:36"}, {"kompetensi_dasar_id": "65224962-9b66-4998-bc82-39932ec862fb", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821080100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil penggambaran konstruksi memanjang kapal kayu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "65225176-6b5a-4858-8223-49426233e0a3", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 828140100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan pemeriksaan kesehatan lingkungan kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "6524c646-a6d1-48bd-918a-7ac58d4c66a1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804080500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifi<PERSON><PERSON><PERSON>  jenis-jenis bahan pipa untuk peker<PERSON>an plambing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "6524d82b-d15b-45a4-87dd-0a9e0f5e227e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:24"}, {"kompetensi_dasar_id": "65260189-d970-4f66-8c87-d39e25850449", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gambar konstruksi bangunan air dan bangunan pertanian dan komponen alat mesin pertanian.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:02:47"}, {"kompetensi_dasar_id": "6526cda2-e30d-40bf-b114-b2399f3629c9", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON>gg<PERSON>bar proyeksi dan\r\nisome<PERSON> plumbing\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:21", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:21"}, {"kompetensi_dasar_id": "6528778d-3be1-46c7-8d52-a9f2c62f3b55", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 800070100, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis anatomi fisiologi sistem reproduksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "6528f62b-b9d4-40a3-8ba1-3101566f1abf", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "652a1462-4b18-4b0d-9624-66e270aac193", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami fungsi perintah dalam perangkat lunak CAD untuk membuat dan memodifikasi gambar CAD 3D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:07", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:07:07"}, {"kompetensi_dasar_id": "652a9564-2b6c-46ed-a23f-595bc926cca7", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 824060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>ji<PERSON> (sound bite) Berita Bersisipan yang menguatkan Laporan Berita", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "652ae30e-4d32-469c-9516-409d2a1f602f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804111000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami bagian-bagian mesin bubut CNC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "652aff34-67eb-455d-b357-13af85afce62", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 809021000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencampuran warna tinta cetak offset gulungan warna khusus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "652c16c4-5f30-4f2b-9bfb-c17c80f74585", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800050420, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan penyusunan diit pasien sehat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "652d91db-c4f3-4a57-9982-4fc6f776042a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817080100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian sifat fisik <PERSON>ur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "652de954-66c6-4dd0-9832-fc06ca67146b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 826140100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan Aplikasi Sistem Informasi Geografis (SIG) Untuk Pemetaan  ITSP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "652e151a-c27d-4223-8f0e-c04d6a2e4ecd", "id_kompetensi": "Dasar sistem mekanik  ", "kompetensi_id": 3, "mata_pelajaran_id": 800000106, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jenis samb<PERSON>, t<PERSON><PERSON> (bushing & bearing), trans<PERSON><PERSON> mesin, dan pem<PERSON>. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:18", "updated_at": "2022-11-10 19:56:55", "deleted_at": null, "last_sync": "2022-11-10 19:56:55"}, {"kompetensi_dasar_id": "652e39bc-8c38-4593-b7c6-aaba4f2129d0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825060200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengevaluasi kualitas bahan pakan ternak (ruminansia, unggas, aneka ternak) secara fisik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "6530010c-d8c1-4bd0-be70-b22d4fb25c6f", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja pemasangan instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:53", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:53"}, {"kompetensi_dasar_id": "65330d3f-08cc-4559-b400-937cd2563e58", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:05"}, {"kompetensi_dasar_id": "65333501-0d08-4141-afe0-db3ab511abcb", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> gambar kerja yangb \r\nakan dikerjakan pada mesin \r\n<PERSON>ais", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "65355bec-1953-458d-9318-24bc577cf270", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 823040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem pengamanan pada PLTBm", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:37", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:37"}, {"kompetensi_dasar_id": "6535fa93-e08d-452f-951c-50c487f3dd46", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> alat potong mesin frais", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "6536ba55-de74-4cd7-964a-6b8be65e2be6", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 800090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON> k<PERSON>, alat kesehatan dan PKRT berdasarkan undang-undang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "6536bac0-eb0f-4044-a8e0-aac402e9b21b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:36", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:31:36"}, {"kompetensi_dasar_id": "6536d193-21d0-4651-a269-0712f3977d39", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 823050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis konsentrasi sumber gula terhadap produksi bioetanol dari tetes tebu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:37", "updated_at": "2019-11-27 00:27:37", "deleted_at": null, "last_sync": "2019-11-27 00:27:37"}, {"kompetensi_dasar_id": "65371409-7c74-4aa9-b9f6-882488a7c7d5", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat  Fuels system sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-10-18 06:43:59", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "65375f0f-af66-48ee-aa1e-05ce8ca2734a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja k<PERSON>, te<PERSON><PERSON>, dan kele<PERSON>ban dalam proses industri kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:19", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:19"}, {"kompetensi_dasar_id": "6537e9bd-9e1a-4713-a721-f5bc5912b320", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:43", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:43"}, {"kompetensi_dasar_id": "653952e5-1e4a-4176-a052-976d229f0f96", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401251060, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengisian <PERSON><PERSON> (SSP)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "653a0b6f-91c7-4108-ad0b-53366d3321c0", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kecepatan putaran mesin berdasarkan tabel yang tersedia dan perhitungan untuk proses pemotongan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "653a50a2-ba92-4df2-8f03-b7d712373ba3", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "653b3596-7830-4480-af6b-f42a107f7587", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung kebutuhan bahan baku dan bahan penunjang dalam suatu industri kimia berdasarkan azaz sto<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:45"}, {"kompetensi_dasar_id": "653beead-f553-4d15-bb5c-0800c368ed84", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah  tentang tokoh nasional dan daerah yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:02", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:59:02"}, {"kompetensi_dasar_id": "653c6485-c284-447b-9228-b1f1c3debf33", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami cara merawat instrumentasi laboratorium medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "653cbcba-4bb2-4ced-8d20-7661780d91bf", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemesinan bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan  teknik pemesinan bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:06", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:06"}, {"kompetensi_dasar_id": "653dde7b-453a-4a89-b67e-44c97288df20", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 803080800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan pada sistem kontrol pen<PERSON>n, memba<PERSON> putaran, interlocking berb<PERSON> kontaktor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "653ef7f9-6570-4b6d-aef2-06db87ce0fde", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi dan aplikasi Displacement / Measurement sensor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "6541c1d5-6bec-471f-abea-9de688297663", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:55"}, {"kompetensi_dasar_id": "65426b37-de1a-4ecf-a68d-2d5f9e77da24", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 814100100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan  proses pelumasan heat setting sub tow", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "654296ca-b994-4351-80ca-d59f6c3bc5f6", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah tentang peran pelajar, ma<PERSON><PERSON><PERSON> dan tokoh masyarakat dalam perubahan politik dan ketatanegaraan Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:35", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:30:35"}, {"kompetensi_dasar_id": "6543a74f-8814-4833-9acc-41ea51b3409e", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 824050600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan peran dan tugas penata cahaya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "6544254e-34c7-4cbb-ab39-26b5476d4204", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827350400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Identified structure of depressions", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "6544aae6-7f03-4f73-be17-1c62d42b9c7d", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bagian-bagian lagu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "65453ee5-eda3-446b-bbe9-236fdfb870dd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827350600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part A. Rule 1-3", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:12", "updated_at": "2019-11-27 00:29:12", "deleted_at": null, "last_sync": "2019-11-27 00:29:12"}, {"kompetensi_dasar_id": "654738fa-e0c3-44e0-9f71-226f49e3ed3c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:57", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:59:57"}, {"kompetensi_dasar_id": "65473dc2-50be-4add-a088-5752a225fb41", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:00", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:00"}, {"kompetensi_dasar_id": "6547b028-c301-4fe7-832c-2b31359b11ac", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan  benda kerja rakitan pada mesin frais, dengan menggunakan berbagai cara/ teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "6547c717-3209-404d-9689-b049f7c02447", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 827390800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain selection materials in construction of equipment", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "65493fa0-9313-4076-832f-89d443c990bc", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pasar produk ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "654a682a-68ad-4960-8520-22c8b7c2df24", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Alat Pemadam Api Ringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:50", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:50"}, {"kompetensi_dasar_id": "654a6f41-49c9-465b-bf18-27b8b10609e5", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 827290400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik penanganan rumput laut kering asin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "654b0499-a0a7-464b-a0a0-9d22efa420fa", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820130300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara melepas dan memasang carpet body", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:21", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:21"}, {"kompetensi_dasar_id": "654ba8c6-afb4-4a24-a95e-3e4d9b16848a", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 820070200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan pada rantai penggerak roda belakang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:21", "updated_at": "2019-11-27 00:27:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:21"}, {"kompetensi_dasar_id": "654cd01c-ea3e-44ab-9152-06132fe08b5d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:13", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:01:13"}, {"kompetensi_dasar_id": "654cf469-6e10-4a4f-9328-99b191db0528", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802032300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip-prinsip Corporate identity", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "654d5766-5fda-4fae-a545-c0a06de1dd4d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mensimulasikan fungsi alat-alat kerja  kayu, logam, las dan pengecatan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "654e26ee-f491-4175-b342-98250883d43a", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang Instalasi Penerangan Jalan Umumsesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "654f4537-8742-4091-8983-e98d2242ac07", "id_kompetensi": "2.3.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami musik kreasi berdasarkan jenis dan fungsi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:52", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:58:52"}, {"kompetensi_dasar_id": "654fbd13-48a1-4a16-a781-bf79596046cb", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804100820, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "erangkai sirkuit pneumatik sistem cascada", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "65529a76-f22d-461c-aff7-1ef474517376", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 843050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis genre musik Latin dalam format band akustik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "655401dc-dbc8-441c-a65b-fd0cb5884b4c", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 824060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is rencana produksi features", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "6554a00a-15e6-47ec-a76e-161cab88aafd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 817150110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami Peraturan Perundang-undangan K3LH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "6555ad21-eb46-45a5-befd-1e4be0c29d8a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:51"}, {"kompetensi_dasar_id": "65565677-bf9a-492b-bd22-f0e77a6d31fe", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:53", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:53"}, {"kompetensi_dasar_id": "65569345-def3-48c9-b7c6-c7c1b056dc53", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 803070700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> uji coba komponen -komponen elektronik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "6556e771-a663-4c4f-bed6-42e4d6aa2809", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi ban dan peleg\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:03", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 16:03:03"}, {"kompetensi_dasar_id": "6557b0c3-0d27-46cf-8785-1493740b42c1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:23"}, {"kompetensi_dasar_id": "65585dca-b08a-42ca-8384-cdb322267ffd", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON>", "kompetensi_id": 3, "mata_pelajaran_id": 700121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "peserta didik mampu membuat karya sendiri atas dasar perasaan,\nminat, nalar dan sesuai akar budaya pada ma<PERSON>arakatnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:25", "updated_at": "2022-11-10 19:57:02", "deleted_at": null, "last_sync": "2022-11-10 19:57:02"}, {"kompetensi_dasar_id": "655927e7-40da-4dee-b135-0977dcded256", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 828190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menerapkan pemanduan wisata minat khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "6559f439-6872-444d-859a-34475cbe35c5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkatagorikan spesifikasi teknis  saluran irigasi berdasarkan fungsinya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "655a3d34-1d31-49c7-a57b-aa3f8dec07b3", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:32", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:32"}, {"kompetensi_dasar_id": "655ae676-5d5c-4cde-8b3c-32de88798308", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 822130100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat benda tiruan menggunakan peralatan kerja bangku pemesinan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:15"}, {"kompetensi_dasar_id": "655c9b37-6b40-4410-8f14-35a1798087d3", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi instalasi listrik kawasan be<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:53", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:32:53"}, {"kompetensi_dasar_id": "655cbc1e-f260-4c9d-a842-6cc1be45f9bd", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:50", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:50"}, {"kompetensi_dasar_id": "655d941d-866f-4a13-808e-1d0dc9145a4f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:18"}, {"kompetensi_dasar_id": "655e6c8b-8eb4-4492-bf1e-c759d08f6db6", "id_kompetensi": "3.31", "kompetensi_id": 1, "mata_pelajaran_id": 823170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> algoritma dan rumus pengaturan pengaturan PID", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "655f6fd5-3e98-4852-9923-61a3934765f9", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401251190, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menganalisis pencatatan transaksi kredit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "656197ba-f4c4-4d9c-b5f5-9806dfebd049", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> alam-alam kehidupan dalam perspektif agama Buddha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:13:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:13:52"}, {"kompetensi_dasar_id": "6562b762-8cb0-4331-99b3-c97105a6acef", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat struktur organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:12"}, {"kompetensi_dasar_id": "65632587-3416-426c-b199-b74a606e1b67", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:19"}, {"kompetensi_dasar_id": "65637fca-3f71-4110-aaeb-e0d6885fc67e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843100100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan gerak keseimbangan tubuh.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "656483b7-0f75-49de-b320-d7fbcf680c1a", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur keb<PERSON>an teks interaksi transaksional lisan yang melibatkan tindakan memberi dan meminta informasi terkait jati diri dalam konteks pekerjaan (wawancara peker<PERSON>an)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "6564eae8-e0c1-43d5-8a5f-1e94128f854d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemesinan gerinda datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:31", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:31"}, {"kompetensi_dasar_id": "6564fab4-a2c7-4419-b4d2-bd78e35807f1", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan keselamatan \r\nkerja pada pek<PERSON><PERSON><PERSON>, \r\nbaju pelindung dan kaca mata \r\npengaman", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:17"}, {"kompetensi_dasar_id": "65666a63-52a2-41f9-93a8-a06ea3b9b658", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknik pemesinan  bubut CNC", "kompetensi_dasar_alias": "Dapat menerapkan  teknik pemesinan  bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "6566ba97-f7d2-40f9-85a8-97f116341774", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 825010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik penanganan pasca panen.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "6566de31-078e-4dd7-8f6c-8c9ca7b2ac5e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 814110200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses pemintalan serat buatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "656811b4-5421-4a3b-bfd2-218f5cba9579", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:06"}, {"kompetensi_dasar_id": "6568dd87-9482-4b09-9444-e9bc22a9e9cd", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 875150020, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Induction, Exhaust and Cooling Systems", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "656a16e5-c30b-4fba-864a-e6c15ad21013", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 801031100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON> anak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "656aa006-735b-4d5d-8d59-1259ef4d937d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik  pemesinan frais kompleks", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:10", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:10"}, {"kompetensi_dasar_id": "656b5773-0b3c-4dbf-9ea9-566e6a892f85", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:46"}, {"kompetensi_dasar_id": "656bb45d-b192-430b-9a0f-98a8eb0f64d5", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 808060610, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan laminasi pada peker<PERSON>an komposit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "656bc9ac-5332-463a-96ab-3316b778630f", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat benda manufaktur bertingkat sesuai spesifikasi dan prosedur standar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "656bf026-ebf6-4f46-90f8-214228c4f320", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:58"}, {"kompetensi_dasar_id": "656c8248-2192-4096-affb-70e4d7aa8c1a", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 804131510, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan <PERSON> dan <PERSON><PERSON><PERSON> SOP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "656cc048-44a6-4fa4-89bf-d47b89b66021", "id_kompetensi": "3.10.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> substansi dan strategi da<PERSON><PERSON><PERSON> <PERSON><PERSON> saw. di <PERSON><PERSON>h", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:52", "updated_at": "2019-06-15 14:49:52", "deleted_at": null, "last_sync": "2019-06-15 14:49:52"}, {"kompetensi_dasar_id": "656d504a-c180-4076-8cf4-bdc9bbe271dd", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804100700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan komponen sipil dan mekanik gardu induk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "656e1761-393c-4c56-a3ac-541424eb808f", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 800061400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan triage dalam kegawat daruratan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "656e972a-0b88-459a-b940-38eee753b831", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 803061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan instalasi audio pada sistem hiburan audio video mobil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "656ee8bb-28e5-4b2c-80f9-bfc8c3eabdcd", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 100016010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan perilaku yang berlandaskan <PERSON><PERSON><PERSON> (Ren) sebagai hakikat kemanusiaan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "656f4124-9d2b-4d1d-a91b-4e5d74507e31", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:28", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:28"}, {"kompetensi_dasar_id": "6570a770-4b8d-4a6d-95b4-8150fad591fd", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan strategi pemasaran bibit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "6570ca00-0697-46d7-ae98-2920f4e9bee7", "id_kompetensi": "2.4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan  partitur lagu karya sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:46", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:46"}, {"kompetensi_dasar_id": "65726382-6e82-4a97-905e-5812c1de791c", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem bahan bakar diesel Common Rail", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2019-06-15 15:03:20", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "657266a6-a6c0-439f-aeaa-aef2c46e9eea", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:40", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:05:40"}, {"kompetensi_dasar_id": "6572942c-91c0-4950-b746-9bb176a8a49e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804101400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan peralatan proteksi Over Heat sistem pembangkit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:45", "updated_at": "2019-11-27 00:28:45", "deleted_at": null, "last_sync": "2019-11-27 00:28:45"}, {"kompetensi_dasar_id": "6572c45d-7a36-43cc-94bd-072cdbc24af2", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 830090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menerapkan penataan sanggul kreatif dengan teknik sasakan dan tanpa sasakan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:33", "updated_at": "2019-11-27 00:30:33", "deleted_at": null, "last_sync": "2019-11-27 00:30:33"}, {"kompetensi_dasar_id": "6575c283-8cb8-4b36-8947-0374e6326282", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 820070500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kerja system penerangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:22", "updated_at": "2019-11-27 00:27:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:22"}, {"kompetensi_dasar_id": "6576f807-e92e-4f18-b31e-02a778f7915c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825063400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis pemeliharaan fase starter ternak unggas pedaging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:08", "updated_at": "2019-11-27 00:28:08", "deleted_at": null, "last_sync": "2019-11-27 00:28:08"}, {"kompetensi_dasar_id": "6577b8bd-f4d3-4013-bdf0-1d35625a4344", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 803071400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses pemisahan informasi pada Detektor FM", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2019-11-27 00:27:46", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}]