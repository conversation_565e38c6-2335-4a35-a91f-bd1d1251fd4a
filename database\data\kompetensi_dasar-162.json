[{"kompetensi_dasar_id": "f4e487cd-86cf-4040-823b-946e8bd25fe1", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401131900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil proses colour matching pada kain selulosa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "f4e54ac7-af67-4dcb-852b-bcb69ad781a4", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 823050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalis<PERSON> proses pre-treatment molase (tetes tebu sebelum fermentasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:37", "updated_at": "2019-11-27 00:27:37", "deleted_at": null, "last_sync": "2019-11-27 00:27:37"}, {"kompetensi_dasar_id": "f4e75c68-44ca-4b23-b4a2-8932760ae710", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:08"}, {"kompetensi_dasar_id": "f4e89f57-044a-48b5-bff1-13a0806a5a42", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kondisi batere PLTS tipe terpusat (komunal)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2019-11-27 00:27:34", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "f4e949a4-ad30-47cd-a102-08dd2ee553c4", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 804040120, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki RAB p<PERSON><PERSON><PERSON><PERSON> dinding, kusen pintu dan jendela sesuai hasil evaluasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:15", "updated_at": "2019-11-27 00:30:15", "deleted_at": null, "last_sync": "2019-11-27 00:30:15"}, {"kompetensi_dasar_id": "f4e98be9-03d9-4571-be3a-f6d779f3dc25", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 807022600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyempurnakan program G Codes bubut CNC dalam pembuatan benda kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "f4e992ba-59ea-4ad2-bd5a-f806d153b3ec", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja instalasi kontrol motor dengan VSD", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "f4e9cf69-8ac2-415f-bf52-54418fce0758", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON>n dan melakukan penye<PERSON>ikan ", "kompetensi_id": 3, "mata_pelajaran_id": 401901000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "peserta didik merencanakan dan melakukan langkah-langkah operasional untuk menjawab pertanyaan yang diajukan. Menggunakan alat dan bahan yang sesuai dengan mengutamakan keselamatan. Peserta didik menggunakan alat bantu pengukuran untuk mendapatkan data yang akurat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:25", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "f4ea32c4-1c40-4ae8-83ae-f41b739b37d6", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengh<PERSON><PERSON> beban tekan, tarik, punter lengkung dan material pendukung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "f4eab89c-1390-470a-8ae1-75e76c8f2808", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan alat-alat laboratorium dan bahan sesuai kaidah GLP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:04"}, {"kompetensi_dasar_id": "f4eb0e76-5058-4c10-b2ec-23f7c9a00899", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang dan mengajukan masalah nyata serta menggunakan konsep dan sifat turunan fungsi terkait dalam titik stasioner (titik maximum,titik minimum dan titik belok)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:15"}, {"kompetensi_dasar_id": "f4eb1487-6856-4aa6-b49a-daa3f0afc445", "id_kompetensi": "4.33", "kompetensi_id": 2, "mata_pelajaran_id": 820030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginspeksi alat pemadam kebakaran.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "f4eb6c86-d35b-47da-9999-30fb370d7b0a", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:23"}, {"kompetensi_dasar_id": "f4eb8708-9331-43d5-8c7e-cb75e299e49c", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 820100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami sistem bahan bakar engine jenis Electronic Unit Injection (EUI)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:45", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:45"}, {"kompetensi_dasar_id": "f4ece876-de82-4483-9515-14c1b7789f40", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 808040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Bearing and Seals", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "f4ee397c-8680-448f-a476-2fccca2499db", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan posting jurnal-jurnal ke dalam buku besar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f4ee814b-3430-4507-98c3-bad4adbbecd1", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 817040110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan penanggulangan limbah produksi minyak bumi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2019-11-27 00:27:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "f4ee9792-addd-4ef5-82a3-241c9f273c9d", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 826060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> dasar komposisi tari tunggal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f4eeb688-ef38-45c4-930e-7bb816360b19", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 802020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil konfigu<PERSON>i DNS Server", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "f4ef0fa2-5d3d-48e4-934a-22f5c82cd6d3", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827351200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply basic principles of hyperbolic navigation systems (menerapkan dasar hyperbolis pada sistem navigasi)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:12", "updated_at": "2019-11-27 00:29:12", "deleted_at": null, "last_sync": "2019-11-27 00:29:12"}, {"kompetensi_dasar_id": "f4ef2826-2da9-4f08-a3e3-d65cedfa3b09", "id_kompetensi": "Proses bisnis di bidang teknik geospasial ", "kompetensi_id": 3, "mata_pelajaran_id": 800000118, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> akhir fase E, peserta didik mampu memahami proses bisnis bidang pekerjaan teknik geospasial secara menyeluruh meliputi menerima order, membuat rencana dan proposal kegiatan berdasarkan persyaratan order, mempresentasikan rencana dan proposal kegiatan kepada pemberi order untuk mendapat perset<PERSON>, melak<PERSON><PERSON>n peker<PERSON>an, dan menyer<PERSON><PERSON> hasil peker<PERSON>. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:57", "deleted_at": null, "last_sync": "2022-11-10 19:56:57"}, {"kompetensi_dasar_id": "f4ef781e-800a-4c1c-baeb-38c6e15929b4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 842010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil produk teknik etsa sablon", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "f4efc40d-47e1-403b-9d71-4198c5b53764", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 839080100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> produk dengan teknik jahit tindas pengisi tali, susulan dan efek bayangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "f4f058d9-139b-4422-8397-ab9fc537a650", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 801030700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan bimbingan konseling pada lanjut usia penderita penyakit persendian dan tulang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "f4f0cee9-4800-4914-a502-437645a65674", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>pta karya pengolahan dari bahan nabati dan hewani menjadi produk kesehatan yang berkembang di wilayah setempat dan lainnya sesuai  teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:32"}, {"kompetensi_dasar_id": "f4f17324-2896-4d09-854a-c75f02b0de0a", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bagian-bagian measurement of fow and volume of blood", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "f4f4742f-4056-4123-ac5f-aa200c54bec1", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan  teknik pengoperasian alat sipat datar (leveling) dan alat sipat ruang (theodolit). ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:05", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:05"}, {"kompetensi_dasar_id": "f4f4815c-2dc3-4bb5-a977-cb993e5c1d96", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penataan alat dan bahan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "f4f5f1d8-fd9c-4961-982e-3f464f9f2798", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:11"}, {"kompetensi_dasar_id": "f4f7bb1e-76da-48a2-9dd0-218c7543d83a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:49", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:49"}, {"kompetensi_dasar_id": "f4f7e2db-8d25-49de-a8c4-5d13c742610b", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> keb<PERSON>uhan rasa nyaman, tidur dan is<PERSON>at", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "f4f8b0ce-db02-4491-a61c-99d658f81879", "id_kompetensi": "3.32", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi laporan laba/rugi untuk perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f4f90845-ec32-4d50-8157-b5564e6c39f6", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:56", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:56"}, {"kompetensi_dasar_id": "f4f97b04-65d8-44a7-8f77-fda7e4e36df4", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gangguan pada sistem bahan bakar injeksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "f4fb5353-4d7b-4fa8-adbb-aa9f5f1c60c0", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:33:19"}, {"kompetensi_dasar_id": "f4fc2e68-e842-486b-9e94-13e7b9a4bb5d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803010300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan konsep listrik dan elektronika (gejala fisik arus listrik dan potensial listrik)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:15", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:15"}, {"kompetensi_dasar_id": "f4fc3ac1-118c-4fb4-b358-8cf8b38a2cf6", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 401141400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengu<PERSON>an kualitatif senyawa obat berdasarkan prinsip kerja kef<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:11", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:11"}, {"kompetensi_dasar_id": "f4fcb341-e14c-4e09-a819-670ca6b3032f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola  menyerang dan bertahan) salah satu permainan bola besar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:09", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:58:09"}, {"kompetensi_dasar_id": "f4fe6309-7289-4093-bddb-839c1acffede", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 814032000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melaksanakan pengawasan pasca antaran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "f4fe8774-2bab-4e67-8e76-17b08cc5e795", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengajukan masalah nyata dan mengidentikasi sifat fundamental kalkulus dalam integral tentu fungsi sederhana serta menerapkannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:10"}, {"kompetensi_dasar_id": "f4ff428e-563f-46d4-b69a-195be91c74a9", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 830060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pewar<PERSON> rambut artistik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:07", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:10"}, {"kompetensi_dasar_id": "f4ff9c87-b906-48c8-88d3-dbdc98182d18", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803070310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan operational amplifier (op-amp) sebagai pengatur P (proportion)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "f501e099-8ebd-439f-bdf1-cb2486bf8c72", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:14"}, {"kompetensi_dasar_id": "f50271a0-4b6c-4e4d-b51a-38df152e9b76", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 839120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha kerajinan fungsi pakai dari berbagai bahan limbah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "f5030387-9176-419b-b86b-a5159a52339b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803071100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistem pengkodean pada komunikasi data", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "f5031945-a11d-4005-8920-ad9d8c612837", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827240100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, menalar dan menyaji pemasaran produk kustacea", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "f504ec4d-a13c-491f-b88c-2cdc7ba73f19", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem pencatatan dan pendokumentasian pada pemeriksaan hematologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:20", "updated_at": "2019-11-27 00:30:20", "deleted_at": null, "last_sync": "2019-11-27 00:30:20"}, {"kompetensi_dasar_id": "f5051896-b533-41f8-a49c-59577d043bca", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 843062000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penggabungan garap instrumen dan vokal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "f506a1ea-d413-47d4-b61e-91c420f7ed46", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:28", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:28"}, {"kompetensi_dasar_id": "f506af82-ee30-4df1-85de-71dfade03acb", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik pelapisan dinding kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "f506f63b-63a8-4096-97d5-09b820638cd9", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 824050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan tahapan pembuatan treatment", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "f509492a-50f0-48c6-bdb7-f21e97838f8d", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843070110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan orkestrasi repertoar lagu vokal  ritmis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "f50b04f3-f36d-4775-ab98-0e4f50ba69a6", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821080400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami perakitan komponen-komponen konstruksi kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "f50c4dce-881b-4b11-8944-451589a4ca76", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem perbaikan dan penggantian kompresor pada unit tata udara domestik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f510ddc0-2c4d-4bd2-bd3d-33ae1524fbaf", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401251230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemasaran dalam E-Commerce.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:17", "updated_at": "2019-06-15 15:07:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "f5113f86-5a55-4b20-a50e-dbd97b239b40", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:43", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:43"}, {"kompetensi_dasar_id": "f5131a3e-0b54-44fa-99bf-62c97b18eb68", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 803090400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat bagian mekanik arm dari sistem robotik pada instrumentasi medik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "f51421f8-a793-44da-a31e-4770f2e4bed8", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> beberapa faktor yang dapat mencegah perilaku terkait yang menjurus kepada STDS (Sexually Transmitted Disease), AIDS dan kehamilan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:39", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:39"}, {"kompetensi_dasar_id": "f514f03d-52c4-4cca-9687-f73e7bebbda4", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukanperhitunganberbagai proses berda<PERSON><PERSON><PERSON><PERSON>ter<PERSON>dinami<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:11"}, {"kompetensi_dasar_id": "f5152362-9e65-4475-a6e6-075ce652b5b9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:38"}, {"kompetensi_dasar_id": "f5152715-9da3-40f6-8627-91bf15923550", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 827060700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan cara pengoperasian Sonar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "f517017c-6b20-40ba-93d4-5da74dafa36f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "f517a542-9ef9-4f49-811f-53bbf744abe7", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 831102000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat desain dasar dua dimensional dengan prinsip <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "f5185dda-980b-4b81-9d36-ee8949e3ee52", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 822190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis konfigurasi instalasi Electronic Control System  PLTH", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "f5190b39-60d1-489a-8e7b-02bed3d8a70b", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 818010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan Analisis <PERSON> (AMDAL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:50", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:00:07"}, {"kompetensi_dasar_id": "f5194b0d-59ec-47cf-a880-6aeb8c329661", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna berdialog serta bekerjasama dengan umat beragama lain", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "f519d62a-47e6-4768-bba0-03b1fc922ffb", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis rangkaian pengalih digital ke analag (DAC: Digital to Analog Converter) dan analog ke digital (ADC: Analog to Digital Converter) menggunakan prinsip dasar rangkaian listrik dan rangkaian terpadu (IC)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "f51ae05e-34ea-4b28-b80d-4f2b91d78444", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengumpulan data non spasial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "f51b3b4a-86c0-4624-8c79-1ae0a95fe529", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 823170600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pemrograman HMI dan SCADA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:14", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "f51b43e7-6ae0-4588-bfb6-b56103f42662", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 828120200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengklasifikasikan organisasi kepariwisataan (nasional, regional dan internasional)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "f51bdb4b-8e0a-4f0a-b7ed-82dd243e715e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip pen<PERSON>nan bahan berdasarkan tanda bahaya bahan kimia sesuai MSDS", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:41"}, {"kompetensi_dasar_id": "f51c1fb1-5e83-4a15-9149-6a0db9d6ebc0", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teks anekdot dari aspek makna tersirat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:28"}, {"kompetensi_dasar_id": "f51cd2dd-8d07-4454-99c7-c44f074b70f0", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401251040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat file backup untuk data akuntansi perusahaan dagang.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "f51ce823-3f3a-47ad-9581-f351a88be911", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:00", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:03:00"}, {"kompetensi_dasar_id": "f51d7168-36eb-448a-a958-217917c19854", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825020520, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan teknik penanaman tanaman naungan/pelindung dan tajar untuk tanaman perkebunan tahunan penghasil minyak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "f51da22b-6e0d-4582-95a2-540f9b1596be", "id_kompetensi": "3.6", "kompetensi_id": 2, "mata_pelajaran_id": 814031100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses data stok barang dengan menggunakan sistem informasi gudang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:38", "updated_at": "2019-11-27 00:29:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:38"}, {"kompetensi_dasar_id": "f51e4ef1-52b5-4918-a054-2ab17ed749d4", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON>ami dasar-dasar system hidraulik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:25"}, {"kompetensi_dasar_id": "f51ea024-9769-42c3-bcb1-3a82823d9f42", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 843050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>inkan ornamentasi pada bentuk harmoni konvensional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "f51f8206-ab2d-4fd1-ba19-d84444b2b85d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825270700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganali sis standar mutu bahan sumber lemak/minyak pangan dan hasil olahnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "f51fa80d-d4d9-4fdf-b4ca-aee625582d31", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 825250700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemijahan ikan hias secara semi buatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "f52062da-087d-4ed2-8f7f-e5e097d22e27", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan ka<PERSON>h teks anekdot, e<PERSON><PERSON><PERSON><PERSON>, lap<PERSON> hasil observasi, <PERSON><PERSON><PERSON> kom<PERSON>, dan negos<PERSON>i baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "f520bfea-f252-42f2-a144-44874e7fbf45", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan alal-alat ukur elektrik", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:40", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:06:40"}, {"kompetensi_dasar_id": "f520d226-7613-4567-90cd-78ba0e8fd0bb", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401251102, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komputerisasi file data akuntansi untuk perusahaan dagang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "f522a8e6-6d73-4687-bf5d-235a89c998b6", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode pencarian kerus<PERSON>n, perbaikan & perawatan macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "f5236acd-d90a-4762-9d02-a91a59a48815", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:45"}, {"kompetensi_dasar_id": "f5239f07-9a9f-4cd7-987f-513c7d3eaa43", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:29"}, {"kompetensi_dasar_id": "f5248294-c790-4a86-8b40-91684d472fbc", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804040110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> jeni<PERSON> per<PERSON> peker<PERSON> konstr<PERSON> gedung, jalan dan jem<PERSON>an", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "f524c8cd-9794-4b4a-bbbf-c354d2933302", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:39"}, {"kompetensi_dasar_id": "f524dd79-a91e-42b8-af7b-af2a15345394", "id_kompetensi": ".", "kompetensi_id": 2, "mata_pelajaran_id": 827140100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "f526c3e7-129c-4a72-a522-fd5f2056f8d7", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 843070110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontrasikan repertoar lagu vokal non ritmis dengan orkestrasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "f526fd26-5ee3-4eab-8a91-f1539a578242", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi batu gerinda untuk penggerindaan datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:56", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:27:56"}, {"kompetensi_dasar_id": "f527550d-6fb6-433c-aaee-7d03099e2f73", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil evaluasi dari berbagai media massa tentang peran Indonesia dalam hubungan internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:51"}, {"kompetensi_dasar_id": "f52788dc-4a7d-46a0-8470-510517717bac", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:43"}, {"kompetensi_dasar_id": "f527b398-56a4-408c-9d4a-e6c0b734796b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:23"}, {"kompetensi_dasar_id": "f528a139-bb86-49c1-b7e4-7887343ff38d", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401141100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengidentifikasi obat berdasarkan cara pemberian obat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "f52973ed-90c3-4da8-9bda-a619ff1a5a19", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827350300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Make fast tugs on towing hawsers or lashed up alongside", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "f52a4619-9396-4d92-8e57-7508bc68af0c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:35", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:35"}, {"kompetensi_dasar_id": "f52aca29-de8c-4cd8-9498-3e65011a6439", "id_kompetensi": "Profesi dan <PERSON> (job-profile dan technopreneurship) serta ", "kompetensi_id": 3, "mata_pelajaran_id": 800000117, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami jenis profesi dan kew<PERSON> , (job-profile dan technopreneurship), serta peluang pasar dan usaha di bidang energi terbarukan, ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:57", "deleted_at": null, "last_sync": "2022-11-10 19:56:57"}, {"kompetensi_dasar_id": "f52b5287-e539-4fca-8415-5b2c42cb63ea", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 817040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penanggulangan limbah produksi minyak bumi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "f52c2eb6-5695-45b5-b3f6-0356a47a0748", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804131102, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan K3 pada proses perawatan instrumentasi logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "f52c5973-982f-4f92-bc2d-fbc51835aaaa", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 807020900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat alat bantu dan melaksanakan fabrikasi part pesawat sederhana dari bahan material composite", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "f52cac66-be95-4cde-bab4-5fdb3fe81598", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804180200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar metrologi industri pada pekerjaan mekanik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "f52ce7e8-ebba-4f8a-9a28-79d87f9c2892", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi perancangan dan perhitungan instalasi air bersih, air panas, udara tekan dan gas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:55", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:33:55"}, {"kompetensi_dasar_id": "f52cf6db-c3dc-461d-9c6e-014d7d6f946a", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 823170610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprogram kontrol yang berurutan (sequence)pada PLC dengan bahasa LAD/STL/FBD/GRAFCET berdasarkan hasil rancangan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:43", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:32"}, {"kompetensi_dasar_id": "f52f5981-1345-44ec-819a-03f4a584a6f0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami arti HAM dan hubung<PERSON>ya dengan tuntutan keadilan yang <PERSON> kehendaki.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:26"}, {"kompetensi_dasar_id": "f530192d-b8b2-4226-b52c-1bb2cdcfedb5", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 821061300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami toolbar CAD dalam menggambar 3 Dimensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "f5329cd0-64be-47a7-a0e3-9a7e410a03fe", "id_kompetensi": "4.31", "kompetensi_id": 2, "mata_pelajaran_id": 803070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membangun robot mobile untuk aplikasi industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "f5333d21-f8f0-4cce-b004-e7b173059543", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827290300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembibitan rumput laut dengan metode fragmentasi/stek", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "f533f179-5825-4cb1-aca5-6fe53116a14a", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kinerja pada sistem transmisi manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "f5347133-ab25-45ab-9316-6989d2bf84fb", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 817050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menunjukan berbagai macam Fungsi Casing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "f53507db-3827-4f95-9269-298f228f14c3", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan Pengecekan dokumen sumber dan dokumen pendukung pada perusa<PERSON>an Dagang.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "f53522df-7f2d-48ab-bd5f-c28cff2f31f8", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hal-hal yang dapat diteladani dari tokoh yang terdapat dalam teks biografi berkaitan dengan bidang pekerjaan yang dibaca secara tertulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:40"}, {"kompetensi_dasar_id": "f5365400-5fc5-4626-bd8f-f0f243c82ff0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 839130200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pemotongan bahan rompi sebagai produk busana kulit dan imitasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "f536b7bb-3d62-4c14-bd96-a712610766db", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistim kestim<PERSON> phase, sistim k<PERSON>, dan si<PERSON>t kimia fisika bahan dalam proses industri kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:02"}, {"kompetensi_dasar_id": "f537db67-31a8-4ba7-b233-91faf90e64bf", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 843011742, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengorkestrasi nada-nada dari part piano ke dalam bentuk <PERSON>win<PERSON>t", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:06", "updated_at": "2019-11-27 00:28:06", "deleted_at": null, "last_sync": "2019-11-27 00:28:06"}, {"kompetensi_dasar_id": "f538af00-1060-4c7b-b8b2-2325b3d11a3a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804140300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengaplikasikan tuntutan peker<PERSON>an se<PERSON>ai dengan gambar, instruksi dan spesifikasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "f5394a80-18d0-426d-90e5-0b13eb0e146a", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 843080520, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> naskah lakon pakeliran ringkas dalam cerita Mahabharata.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "f53a0429-a008-446b-bd43-ded338d23956", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan  akuntansi bank dengan akuntansi keuangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "f53ad43c-7873-46ec-81eb-4f7a2d970bf2", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan tentang pengertian penjualan konsinyasi, <PERSON><PERSON><PERSON><PERSON> kons<PERSON>, proses pencatatan untuk komisioner dan pengamanat serta penyiapan laporan laba-rugi dalam penjualan konsinyasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "f53bca76-ed97-4269-b6ad-4e5e4effcbbd", "id_kompetensi": "4.7", "kompetensi_id": 1, "mata_pelajaran_id": 804150600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki gangguan rangkaian sistem pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "f53d6526-2fef-4777-9829-a42890adff34", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan jenis-jenis pek<PERSON>an pada pelaks<PERSON>an\r\nkonstruksi bangunan gedung\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:29", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:29"}, {"kompetensi_dasar_id": "f53de6b9-2351-424e-af1c-2e239e7abb0f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis volume pekerjaan pada pelaksanaan konstruksi bangunan gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:47", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:47"}, {"kompetensi_dasar_id": "f53e68aa-3ee9-4203-a0c4-29450cdffbb3", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:41", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:31:41"}, {"kompetensi_dasar_id": "f53fa8cb-e558-42d6-958a-bc322c1725f7", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:05"}, {"kompetensi_dasar_id": "f5403a96-a0d9-4a32-83c5-7cd168d5265b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827360200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate sends and receives the distress signal <PERSON><PERSON> by flashing light", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "f540969c-5d77-49e6-a06b-beb63f1246cf", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:28", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:28"}, {"kompetensi_dasar_id": "f5412835-16cb-45f3-8a76-ac042b95ce5f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:35"}, {"kompetensi_dasar_id": "f5427ece-5c74-477c-8e02-2a6fd3f55ecd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 826110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan teknik konservasi tanah dan air dengan metode sipil teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "f5436e00-ee67-4d31-9141-79284dc08821", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:55"}, {"kompetensi_dasar_id": "f54415f2-f405-40c8-a2c9-7d2a0efc8b0b", "id_kompetensi": "2.4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat tulisan tentang musik berdasarkan jenisnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "f5444cf1-eec5-4663-a858-cd75d99ad0b7", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis din<PERSON><PERSON> pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945 dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:31"}, {"kompetensi_dasar_id": "f54485f3-2414-4fc5-909e-b1754c82b556", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pemasangan instalasi listrik dengan pelindung saluran kabel sesuai dengan PUIL", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "f544d9ab-0710-40f6-99f5-bc8484b345c6", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843090800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan teknik garap gerak berdasarkan tema", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2019-06-15 14:58:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "f546e44b-3a18-48fc-9ff6-1d08427832ec", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukanpemasangan komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:41", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:29:41"}, {"kompetensi_dasar_id": "f547bc5d-a7f6-4389-be20-94fdeb153547", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON>ji perbedaan normalisasi system gambar terlevisi (CCIR, PAL, NTSC dan SECAM)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "f547effb-85e2-4d5e-8332-3bdbf8f415f1", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks il<PERSON>h fak<PERSON> (factual report), lisan dan tulis, se<PERSON><PERSON>, tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, terkait dengan Mata pelajaran lain di <PERSON>las XII, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:24"}, {"kompetensi_dasar_id": "f547f1bd-0678-4744-a509-ba4a4fb01384", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan pertun<PERSON>kkan rumah (home theater)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "f54b254c-e889-4a64-877b-aeed04f98793", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami panggilan hidupnya sebagai umat Allah (Gereja) dengan menentukan langkah yang tepat dalam  menjawab panggilan hidup tersebut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:06", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:06"}, {"kompetensi_dasar_id": "f54c22b6-f560-45e8-a221-8bd2e96c8fcc", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifikasi Alat Pemadam Api ringan (APAR)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:38", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:38"}, {"kompetensi_dasar_id": "f54cb1b3-fa96-4a36-ad5b-cbf9b4768b77", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:21"}, {"kompetensi_dasar_id": "f54dbc53-ad44-4712-8ab6-c81ca9091150", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengklasifi<PERSON><PERSON> jenis-jenis hand tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:43", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:43"}, {"kompetensi_dasar_id": "f54e4cfc-4490-4aa8-a4cf-62a2b59462aa", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:11", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:11"}, {"kompetensi_dasar_id": "f54f726f-5288-4d31-9ae0-50e1b8ec8118", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis sampel dengan kromatografi kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:58", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:58"}, {"kompetensi_dasar_id": "f5501690-4212-4c88-88e7-62966c995afd", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:31"}, {"kompetensi_dasar_id": "f550da51-1783-4184-a3cd-04b0a23c4409", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 817120110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembuatan gambar instalasi ducting air condition (AC)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "f5526e11-a4a0-46d8-af05-d3d1efbed810", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang transmisi mekanik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "f55318ed-d983-41e0-84a7-5a0292492399", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perkembangan Islam pada masa modern (1800-sekarang)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:20", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:20"}, {"kompetensi_dasar_id": "f554a3dc-aad3-4e3a-b365-5bfc5d0b85c7", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804131520, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang Pembuatan produk furniture perpaduan teknik las dan bubut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "f5575fd9-0438-4a24-b6be-0e50c937bef6", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikan teknik dasar salah satu aktifitas olahraga permainan bola kecil untuk menghasilkan koordinasi gerak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:05", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:09"}, {"kompetensi_dasar_id": "f5579065-8372-42d4-ae23-5d5dc5705a99", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 842040500, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses pembuatan komponen ukir pada produk mebel kriya kayu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "f5581dbe-93e2-4187-add6-9b0f3a56c0e8", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401251040, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis pencatatan transaksi pembelian bahan-bahan perleng<PERSON>pan (supplies), barang dagangan, aset tetap dan transaksi pembayaran utang pada perusahaan dagang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "f5586515-1617-4005-a109-a1f4114feb5e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 826070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengolahan data hasil pengukuran digital dengan komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "f558950b-a1ca-4618-9b33-cbc9c2c14e5d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:41", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:31:41"}, {"kompetensi_dasar_id": "f55a9649-9ed2-4fd3-8139-1db718a95c77", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 821171000, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perakitan rangkaian diode sebagai pengaman dan sebagai penyearah (catu daya)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f55aba14-7788-4933-986e-fbc12eee3a17", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:16", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:16"}, {"kompetensi_dasar_id": "f55b812d-0424-429c-87ca-246db37fd047", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang komposisi gambar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "f55dac65-3879-4bc0-932b-61f4a542b592", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyusun teks interaksi transaksional lisan dan tulis yang melibatkan tindakan memberi dan meminta informasi terkait hubungan sebab aki<PERSON>, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "f55f7089-8cec-4f49-ac29-aa71733b3de7", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 800081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriks<PERSON> patogenitas bakteri dari berbagai jenis bakteri patogen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:19", "updated_at": "2019-11-27 00:30:19", "deleted_at": null, "last_sync": "2019-11-27 00:30:19"}, {"kompetensi_dasar_id": "f55fa77f-d537-4cfe-9747-2decfed97e4d", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 815010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil pengujian benang mesin Ring Spinning", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "f560d9e4-c683-4d9a-8fa5-d345f82b0784", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:35", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:27:35"}, {"kompetensi_dasar_id": "f561421b-e00b-4c4c-83b3-265bc4bb26d2", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401251040, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan komputerisasi file data akuntansi untuk perusahaan jasa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "f562e22e-d8b9-4b93-8b90-578991a91c32", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem bahan bakar engine jenis Mechanical Fuel System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "f563a3e3-6d8b-4704-9dc7-30a891c18977", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:15"}, {"kompetensi_dasar_id": "f56411d1-c5c0-4458-941e-72c1eb0ab369", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> prinsi<PERSON>, konsep dan prosedur khromatogra<PERSON> kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:47", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:47"}, {"kompetensi_dasar_id": "f5657a0f-9d64-4157-a314-6bed781dff9e", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis teknik transplanting", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "f565a7e0-3fba-4a40-92a5-6c285f2eca46", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 827350300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrates how to put a stopper on a rope or wire rope", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "f5680754-9b02-4981-8162-838d88bce3f6", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 807020520, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip-prinsip pembuatan desain jig, fixture", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "f56b1c19-5b02-4728-89e0-a5459d31691d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:31"}, {"kompetensi_dasar_id": "f56cc3cb-b2dd-4463-a011-496d57db2aed", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil evaluasi dari berbagai media massa tentang peran Indonesia dalam hubungan internasional.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:51"}, {"kompetensi_dasar_id": "f56d574c-d6cf-42bc-933d-140d4e291a6e", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat program kontrol untuk programmable logic control (PLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:59:00"}, {"kompetensi_dasar_id": "f56da879-d2ef-4f8e-a72b-a9d4f537d92a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 825250300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan potensi dan prinsip-prinsip <PERSON>/Aquascape", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:30", "updated_at": "2019-11-27 00:29:30", "deleted_at": null, "last_sync": "2019-11-27 00:29:30"}, {"kompetensi_dasar_id": "f56e0dfb-2183-4005-bf4c-3c0151f3ddc3", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 825100300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sitem kendali otomatis pada alat mesin pemindah panas/ pengering", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "f56f0b4d-d65e-49ae-881b-06ca4188b2b3", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 827090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifika-sikan faktor-faktor yang mempeng<PERSON>hi kualitas hasil tangkapan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "f56f3e32-bc1c-4ef6-bf13-86a3bc587c39", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 817030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> faktor produksi sumur flowing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "f56f4a77-ed23-4a21-bd7a-f4981cd055b5", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 802030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan desain grafis surat kabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-10-19 23:19:22", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "f56f8f89-ffdc-49e0-a7e6-451fc3a69996", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "f5707c70-c190-4978-bd6a-5b085001d61d", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian pembangkit gelombang pulsa witdh mudolation dengan menggunakan penguat operasional (operational amplifier), untuk pengontrol kecepatan motor ac.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "f5707f77-a049-46fc-839b-1e05de51b248", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 804190200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang  Lay Out of instrument supply pada sistem instrumentasi kontrol proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "f570f1dc-4e99-4182-87c0-cff59bc2396c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "f57127d3-a6c4-4462-92d6-c0e28c44c913", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:16"}, {"kompetensi_dasar_id": "f571744e-7d5f-49ba-80c7-b10eb1a943d6", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 800020410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pemeriksaan penyakit infeksi dan penyakit menular", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "f572b915-e841-4406-8f1b-c36432737b95", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:25", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:25"}, {"kompetensi_dasar_id": "f572f8ea-11a4-4cf3-8985-723036c71ef1", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Merancang perbaikan konstruksi bangunan gedung yang tergolong renovasi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:53", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:33:53"}, {"kompetensi_dasar_id": "f5737df5-7565-4f2d-b306-041da439d323", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:48", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:48"}, {"kompetensi_dasar_id": "f5739904-6d18-49f0-b6aa-5a91dda557c0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401141200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis sediaan obat tradisional", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2019-11-27 00:30:21", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "f57588b9-75ff-4025-abba-764789915a71", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "f575daeb-b5e1-42ab-8628-3017dc33fef6", "id_kompetensi": "<PERSON><PERSON><PERSON>", "kompetensi_id": 3, "mata_pelajaran_id": 100016010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis karya dan nilai keteladanan para Nabi dan Raja\n<PERSON> Menganalisis sejarah masuknya agama <PERSON>,\n<PERSON><PERSON><PERSON><PERSON><PERSON>, dan eks<PERSON><PERSON>i agama <PERSON> di Indonesia.\nMenceritakan kisah <PERSON>, <PERSON><PERSON><PERSON>, dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:46", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "f576b754-119d-44e8-a45b-501d2cf072d5", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 401130800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan peralatan distilasi jenis menara isian berukuran kecil sesuai SOP", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "f57761cf-f442-42ab-88b3-ac070f701baa", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 827320110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Demonstrate on List and Its Correction", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "f577a4f8-6966-419e-86f2-a008d9dfaa04", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:25", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:25"}, {"kompetensi_dasar_id": "f578ef57-82e8-4dfd-94b2-ef3016d35ecf", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804080200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki Peralatan Portable", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "f57939b6-2185-4c59-b321-9e43a7c61246", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 843080520, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> lakon", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:21", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "f5793b59-4ff5-43e2-8987-4e6aadd5e269", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan sanggul (Up style)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "f579954e-f892-46d2-849a-d2f5b517706c", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:56:58", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:56:58"}, {"kompetensi_dasar_id": "f579a4fb-a55d-4825-bfb1-79e2e62e5d25", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:03", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:03"}, {"kompetensi_dasar_id": "f579b5fe-c69d-4565-a1a3-d218ead82e91", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencana dan menginstal CCTV untuk sistem keamanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:55", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:59:55"}, {"kompetensi_dasar_id": "f57a61c4-3276-4d41-beee-8622f14e0fec", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 826080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkatagorikan   kriteria tumbuhan dan satwaliar yang dilindungi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "f57affd7-0001-4210-86c3-df0194f26653", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:06"}, {"kompetensi_dasar_id": "f57b064c-c73d-4b3b-95d6-78052f37dd01", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan tabel periodik untuk memodelkan struktur atom berdasarkan kelompok material elektronika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 15:06:51", "deleted_at": null, "last_sync": "2019-06-15 15:06:51"}, {"kompetensi_dasar_id": "f57b2efc-54d4-42d8-8f78-e9e342d36ec9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> perilaku bertanggung-jaw<PERSON>, peduli, santun dan cinta damai untuk menciptakan keluarga yang rukun,bahagia dan sejahtera sesuai ajaran <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:06:35", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:06:36"}, {"kompetensi_dasar_id": "f57bb599-c929-42e4-a088-78b899ad6a89", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:28", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:28"}, {"kompetensi_dasar_id": "f57ec0ce-5df8-47b3-89fb-8f5c1253bffb", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 829030300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pelayanan makanan dan minuman di kamar tamu (Room Service)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "f57fe926-e222-48f4-aa89-9be8752cf69a", "id_kompetensi": "3.2.", "kompetensi_id": 1, "mata_pelajaran_id": 804101100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mendeskripsikan penggunaan peralatan berten<PERSON> (power tools)", "kompetensi_dasar_alias": "", "user_id": "1dba9466-2d08-4a56-95ec-b4e08328e1ff", "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:11", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:11"}, {"kompetensi_dasar_id": "f581feb9-c555-41a6-95e1-07c32dad378c", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 825100300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan perawatan alat mesin pembersih/pencuci", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "f582d39b-a6da-4f71-a3b2-7b012e530e09", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:06", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:06"}, {"kompetensi_dasar_id": "f583520a-be72-46c4-86c7-686c100877a5", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan bagian dari \r\nproses dan memilih alat bantu \r\nuntuk menghasilkan \r\nkomponen yang spesifik sesuai \r\ngambar kerja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:17"}, {"kompetensi_dasar_id": "f583b990-1828-4056-9352-210da49bf137", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 840020130, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil analisis proses pembakaran benda keramik biskuit dan glasir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "f584224b-3ca3-49bd-b208-5eb97e88e5fd", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:50", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:50"}, {"kompetensi_dasar_id": "f584a77b-70c4-460e-bd13-077cf1ba5476", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803040800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan instalasi approach link menggunakan serat optik,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "f586b397-0f2d-4c18-8277-8480ba8f1095", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 840030100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara mendekorasi keramik clay body plastis dengan teknik relief", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "f586cfca-0ac5-4b99-9603-6eaad0f38b80", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:23"}, {"kompetensi_dasar_id": "f588574e-a635-4b14-b88f-6f32be2b2cc2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 831101400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gambar model manusia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "f588e5ef-9f77-4b7c-a606-a598e2e9ba25", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengartikulasi sensor suhu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "f58974ef-57fa-49f9-9560-1a8f147171eb", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:29"}, {"kompetensi_dasar_id": "f58a83a1-cb11-46af-aff7-93cbe535f8a1", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:52", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:58:52"}, {"kompetensi_dasar_id": "f58b695d-acfd-461d-a3a6-7165c6008fd4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:34", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:34"}, {"kompetensi_dasar_id": "f58bc04b-8c02-42c3-a36c-bd2c775ca2f2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:39"}, {"kompetensi_dasar_id": "f58e0c8e-3fba-46f9-b8ad-532c90c91459", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan Ke<PERSON>ama<PERSON> dan <PERSON><PERSON> serta <PERSON>n Hidup dalam pelaksanaan pekerjaan <PERSON> Bangunan Gedung", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:19", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:19"}, {"kompetensi_dasar_id": "f58e742b-98cd-4dff-9110-eadfbebc0b7d", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pengaruh kalor terhadap zat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:58"}, {"kompetensi_dasar_id": "f58eeb21-76eb-4871-926b-a29976e27b57", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:27"}, {"kompetensi_dasar_id": "f58f87db-8b4e-4724-b050-4245b8aa1fc7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.1 Menerapkan kaidah gambar proyeksi dalam membuat gambar proyeksi bangunan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "f590bc26-9492-4fb1-a6d5-857fb5379207", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pencatatan transaksi pengeluaran kas untuk pem<PERSON>ian bahan, membayar biaya tenaga kerja langsung, biaya overhead pabrik, biaya administrasi umum dan pema<PERSON>, melu<PERSON><PERSON> utang dagang, dan utang lainnya ke dalam buku jurnal khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f591215f-979c-4cfd-bd42-12654728e89a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara kerja poros roda", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "f591b5ad-5921-49af-89b5-f0d6b1fa54a8", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401251500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menggolongkan jenis jenis bisnis online", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "f5920d9c-f7f6-424f-a8e9-dd4ab9d02d33", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401131900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil proses pencampuran warna pada bahan tekstil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "f5940ffd-163e-4113-91b4-70ce0f16a177", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem audio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:30", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:07:30"}, {"kompetensi_dasar_id": "f5951e5d-838a-467f-9d62-b9c4dafcbb1f", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengobatan benih  ikan sakit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "f597bfe8-4033-430e-b55d-d6d1aca3ff76", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna teks penyerta gambar (caption).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:32"}, {"kompetensi_dasar_id": "f597d9b0-78f4-4fda-a632-071abb421302", "id_kompetensi": "3.36", "kompetensi_id": 1, "mata_pelajaran_id": 822190400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevalusi desain bagian-bagian sistem aplikasi PLTS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:34", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:34"}, {"kompetensi_dasar_id": "f59881a9-9142-4b76-98ea-6d5787debfcb", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 825020610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengendalian penyakit tanaman perkebunan semusim penghasil minyak atsiri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "f5989e95-3336-4352-9141-c99693e02e41", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:41", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:41"}, {"kompetensi_dasar_id": "f598d612-abd0-40db-b227-14bd2bc199bc", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pencatatan pengeluaran kas bank", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:01", "updated_at": "2019-11-27 00:30:01", "deleted_at": null, "last_sync": "2019-11-27 00:30:01"}, {"kompetensi_dasar_id": "f599174c-90dc-4555-bf20-4beff4ad51a2", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> pemasaran hasil  sesuai prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "f5999471-c74b-48dc-84ad-a31040ac0165", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan  prosedur \r\nk<PERSON>n <PERSON> dalam \r\npeker<PERSON>an memb<PERSON>ut", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:09", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:01:09"}, {"kompetensi_dasar_id": "f59b3608-e291-4dc3-b146-b7dc91ae5ef5", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam kecela<PERSON>an kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "f59b9546-6590-4567-8612-6b913fdef3f1", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 825020520, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan teknik penanaman dan pemeliharaan tanaman penutup tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "f59baeed-bca8-42d2-893e-102841decd68", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk menyatakan dan menanyakan tentang pengandaian diikuti perintah/saran, dengan memperhatikan fungsisosial, struktu rteks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:38", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:33:38"}, {"kompetensi_dasar_id": "f59cfe98-da57-4d73-b36a-bdda9f37702f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:26", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:26"}, {"kompetensi_dasar_id": "f59f869a-8618-4fa7-ba6d-c24f1d3d5649", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:59:02"}, {"kompetensi_dasar_id": "f59f93f9-5f4d-4730-930d-0e17d452ebcf", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 807021800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeksperimen Water and waste (ATA38)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "f59fb31f-da26-4314-a9bb-c9b252fd6acb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 820030700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan trasformator", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:18"}, {"kompetensi_dasar_id": "f5a2643f-85e9-44d1-a527-cef44cb2c019", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 843062300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan repertoar gending/lagu kategori dasar pada karawitan etnis nusantara mandiri dan iringan dalam berbagai irama", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:28", "updated_at": "2019-11-27 00:28:28", "deleted_at": null, "last_sync": "2019-11-27 00:28:28"}, {"kompetensi_dasar_id": "f5a3257d-cbec-489f-9b11-4a0ca3b78b3a", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 805010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan manfaat SIG bagi kehutanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "f5a35cad-65c8-42b8-b90e-5919dcac12bb", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 814110200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses pembuatan kain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "f5a5b45d-96b8-4825-b5c2-cf54f9342d85", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar bukaan bidang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:54", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:54"}, {"kompetensi_dasar_id": "f5a5f50b-2c21-4d77-ba91-5fa0ad01f797", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 843070110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi tata irama sajian vokal  ritmis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "f5a6042a-8b65-406a-b7a6-c2e77de7a69a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyunting teks cerita sejarah, be<PERSON>, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "f5a643ea-ee76-40e9-86cc-6f7bea7cb53c", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401251040, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat daftar akun dan mengisi saldo awal akun buku besar perusahaan manufaktur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:15"}, {"kompetensi_dasar_id": "f5a75766-22bd-44b8-891a-3a546d0fce86", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 808060610, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan proses pembuatan cetakan (mould) dari master produk menjadi catakan negatif dari beberapa material", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "f5a80462-e6ad-44d1-996a-a50a3ea2da49", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 802031110, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan kombinasi warna berdasarkan suasana atau tema", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "f5a814af-700b-4b3e-8cdf-1ba30e6b3026", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843061310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan melodi dalam tangga nada mayor 1#- 3#,1b - 3b", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "f5a84fe0-5f06-4210-96c0-ad4af5af8082", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi cara meminta sesuatu sesuai dengan konteks penggunaannya, dengan memperhatikan fungsi social, struktur teks, dan unsur kebah<PERSON>an pada teks interaksi transaksional lisan dan tulis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:40"}, {"kompetensi_dasar_id": "f5a9513a-4aec-4713-a3c9-1f09d2670432", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "f5a96393-6763-4633-ac2a-a3162050a8c0", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401251210, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses perencanaan produk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:17", "updated_at": "2019-06-15 15:07:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "f5a9d35f-4516-48cf-b0fd-c608d6082a69", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 824051400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami struktur organisasi departemen kamera", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "f5aa2f60-a5ba-41b9-aa27-4b9e2102321e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 802020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil rancangan antar muka pengguna (user interface).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "f5aa7609-299d-4e9b-98a9-23fc168f28e3", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:37"}, {"kompetensi_dasar_id": "f5ab481d-36dc-49c0-9a2a-6854f8d70fe0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:42"}, {"kompetensi_dasar_id": "f5ab58e2-0e2b-48ba-8dd1-6a5317e4c779", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 828010104, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pencatatan transaksi pada bukti transaksi internet", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:48", "updated_at": "2019-11-27 00:29:48", "deleted_at": null, "last_sync": "2019-11-27 00:29:48"}, {"kompetensi_dasar_id": "f5ac327c-911c-4cc3-9156-10c513edbd37", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 821180100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat macam-macam bentuk dan symbol spot weld, seam weld, dan surfacing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:22", "updated_at": "2019-11-27 00:29:22", "deleted_at": null, "last_sync": "2019-11-27 00:29:22"}, {"kompetensi_dasar_id": "f5acb2fc-6c79-4656-b2d3-ed3419ed6fb9", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemesinan frais  kompleks untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:09", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:29:09"}, {"kompetensi_dasar_id": "f5ae21a4-4166-4061-a317-71ab4842a585", "id_kompetensi": "4.7.1", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Berinteraksi dengan teman dan orang lain berdasarkan prinsip saling mengh<PERSON>, dan menghargai dalam keberagaman suku, agama, ras, budaya, dan gender.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:01", "updated_at": "2022-10-18 06:43:48", "deleted_at": null, "last_sync": "2019-06-15 15:31:01"}, {"kompetensi_dasar_id": "f5af7b60-16b2-4810-a166-34533808c181", "id_kompetensi": "4.35", "kompetensi_id": 2, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar komponen transformator Daya 1 fasa dan 3 fasa sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "f5af9681-4d11-4df9-afcb-56936feaec32", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis gaya batang pada konstruksi rangka sederhana", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:53", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:53"}, {"kompetensi_dasar_id": "f5b02fcb-071c-402e-97dc-ab56778801f6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami aplikasi metode reduksi digital menggunakan diagram state dan tabel flow", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "f5b03121-f11d-437c-9e66-8a8137499c1c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pendapat dan pikiran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:26:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:26:50"}, {"kompetensi_dasar_id": "f5b0d4e4-a0b4-41f9-86dc-f6e25fdaff1c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:23", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:23"}, {"kompetensi_dasar_id": "f5b2dae2-208e-436b-939b-a50511c0ca03", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 817090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penyelesaian problem loss", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:47", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "f5b3cb5f-4f2b-4b43-8610-ad099f36cf5c", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengumpulan Data Perancangan jaringan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:24", "updated_at": "2019-11-27 00:28:24", "deleted_at": null, "last_sync": "2019-11-27 00:28:24"}, {"kompetensi_dasar_id": "f5b4263d-b69b-46fe-b641-68577a5729da", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 825100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan alat mesin pemupukan disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "f5b55498-b586-4e9d-b337-503118eb9fee", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:07", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:07"}, {"kompetensi_dasar_id": "f5b61150-63e7-4632-be4c-5f33b3800550", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pembuatan neraca saldo untuk perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f5b65e5b-8550-4bc5-acac-9deec8913839", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "f5ba099a-d111-40ef-86a3-965f3cc923f0", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843080200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan karakter wayang.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "f5bbff3b-9e0c-441f-b317-c566ae21fd11", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:07", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:07"}, {"kompetensi_dasar_id": "f5bc3ce5-007c-4842-a8a3-7ab9e406b539", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Q<PERSON><PERSON> (31): 13-14 dan Q.S<PERSON> (2): 83, serta hadits tentang saling menasihati dan berbuat baik (ihsan).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:01", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:01"}, {"kompetensi_dasar_id": "f5bcec44-756c-495a-acce-56346cde66d6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "f5be9868-7e0e-4003-adb3-9020910040fd", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 843090500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan greenery dan floor cloth pada setting", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "f5c15347-d3bd-4315-92e4-1e3ee5e2b644", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 802030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses pemotretan model fotografi dengan kamera digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "f5c18915-e614-4b57-8917-718e616e03d5", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:03"}, {"kompetensi_dasar_id": "f5c1f47b-85d8-43e8-b7c9-e163f384bed5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>n <PERSON> ( K3LH ) dalam kegiatan laboratorium", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:45"}, {"kompetensi_dasar_id": "f5c20fd0-6f96-4104-8bc1-b8dd5fe15faa", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827390600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain ideal gas cycle", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:15", "updated_at": "2019-11-27 00:29:15", "deleted_at": null, "last_sync": "2019-11-27 00:29:15"}, {"kompetensi_dasar_id": "f5c31de6-45b8-48e0-a2d0-501c4539e726", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyiapan sampel dan standar analisis kromatografi kertas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:46", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:46"}, {"kompetensi_dasar_id": "f5c4520e-fb52-4d5e-9eb0-b17d25a741b8", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda silinder (cylindrical grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:57", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:27:57"}, {"kompetensi_dasar_id": "f5c5759e-26a8-47db-8db3-c4ea83c7c607", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami arti HAM dan hubung<PERSON>ya dengan tuntutan keadilan yang <PERSON> kehendaki.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:50"}, {"kompetensi_dasar_id": "f5c5a093-d55e-4476-8f99-a0fdf69922ef", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 827130200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan motor diesel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "f5c60cb5-1f3f-4cfe-997e-3ca0bce73725", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843070500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan sejarah dan perkembangan titilaras", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "f5c74133-6fb0-4711-9e58-fcc73df1bc31", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan rangkaian pemilih sinyal analog(analog multiplexer) menggunakan rangkaian terpadu (IC) multiplexer/demultiplexer digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "f5c79356-75b7-4e0b-b86c-5e9202a7255f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:43"}, {"kompetensi_dasar_id": "f5c92de6-9ddc-4a22-827b-8dc6b674a34b", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825063200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan penyusunan formulasi pakan ternak ruminansia", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:14", "updated_at": "2019-11-27 00:28:14", "deleted_at": null, "last_sync": "2019-11-27 00:28:14"}, {"kompetensi_dasar_id": "f5c98e37-2d93-484f-9b51-e538b44d4e5d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 822140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perhitungan daya dan energi air", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "f5c98ef3-8c30-4fb5-a56f-6ee43d5efb68", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:29"}, {"kompetensi_dasar_id": "f5cadc67-0d7d-4e90-9002-a62451d88e33", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 825230110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis kebutuhan alat serta cara penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "f5cb83d5-6c35-418d-9c52-950e69451fc3", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "f5cb955b-63ef-4bb3-b18f-5cffb5fdcaa6", "id_kompetensi": "Dasar pergerakan buatan untuk diterapkan berdasarkan instruksi kerja ", "kompetensi_id": 3, "mata_pelajaran_id": 800000149, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, p<PERSON><PERSON> didik mampu men<PERSON> pengetahu<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, dan sikap kerja yang dibutuhkan dalam membuat gerak objek digital non-character, dasar pergerakan buatan sesuai instruksi kerja pergerakan objek, melip<PERSON> unsur gerak dalam kehidupan, suara, waktu, masa dan sifat objek yang akan digerakan. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:24", "updated_at": "2022-11-10 19:57:00", "deleted_at": null, "last_sync": "2022-11-10 19:57:00"}, {"kompetensi_dasar_id": "f5cc1765-6baf-438f-928a-7e96668ee4df", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengoperasian alat mesin perawatan dan perbaikan alat mesin  pertanian (las karbit, Las TIG/MIG, bubut, frais)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "f5ccc862-c653-487c-9aea-a7ed2feed43b", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 800061400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan triage dalam kegawat daruratan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "f5cd7619-835e-4b79-b02a-ed9fa5e7c196", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 814080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menentukan jenis-jenis hama dan penyakit dalam penyimpanan penggudangan hasil panen, olahan pangan dan non pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "f5cdf4b0-29be-46de-b0bc-4810fcd1719a", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  kehidupan politik dan ekonomi  bangsa Indonesia pada masa awal Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:02:38"}, {"kompetensi_dasar_id": "f5ceb3bc-9f57-42f0-aed5-eaf4d846e930", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis karakteristik unsur musikal repertoar ansambel/orkestra jaman Romantik level pemula", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "f5d128c6-f654-4e4a-9e42-cb07d29e9c45", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan nilai maksimum dan minimum permasalahan kontekstual yang berkaitan dengan program linear dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:32", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:32"}, {"kompetensi_dasar_id": "f5d1d980-58a2-4cf9-9159-3659e7307648", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:24"}, {"kompetensi_dasar_id": "f5d2be1a-ea0e-4d78-96a0-c3ccbb6e0891", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:22", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:22"}, {"kompetensi_dasar_id": "f5d42123-0f72-448d-9df1-0d2d6206958f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dalam penggunaan peralatan dasar laboratorium    (alat-alat gelas dan non gelas)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "f5d46608-a67c-490b-b68e-69f77863e93b", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:27"}, {"kompetensi_dasar_id": "f5d5a46e-b81a-4b50-87f3-65d81fcab276", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:48"}, {"kompetensi_dasar_id": "f5d64a9a-ed8e-4197-bd16-48b927edd301", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827390500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Show the heat treatment processes and the types of steel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:18", "updated_at": "2019-11-27 00:29:18", "deleted_at": null, "last_sync": "2019-11-27 00:29:18"}, {"kompetensi_dasar_id": "f5d7a802-8da9-41f6-9b26-5a36bad26dcd", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:48"}, {"kompetensi_dasar_id": "f5d7ab60-c3e7-469a-a6d9-b581d69d29b9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 843011730, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan cara memberi aba-aba dalam formasi koor/ansambel/orkes", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:30", "updated_at": "2019-06-15 15:07:30", "deleted_at": null, "last_sync": "2019-06-15 15:07:30"}, {"kompetensi_dasar_id": "f5d86820-a7b7-4be0-8bd0-44d1f3d76e9e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mengendalikan  dan merawat alat  industri kimia (pengecilan ukuran, tempat penyimpanan gas, alat pengalir padatan, alat untuk pembesaran, alat unit utilitas (boiler)  dan alat pencampuran", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "f5d9cd33-6e0b-4a38-8b99-0904b8909126", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemasangan dan penyambungan kabel daya pada terminal generator dan switch gear", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2019-06-15 14:49:58", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "f5db08fa-7fac-430c-8cb3-c2f804397b88", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi gambar hasil ukur tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2019-11-27 00:27:46", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "f5db39e7-c46c-46a9-bbbc-807f763f764f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem engine manajemen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:30", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:30"}, {"kompetensi_dasar_id": "f5db6efe-dd9d-4256-9cb8-7bc803b03bc8", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804011200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan ukuran berantai, seja<PERSON>, k<PERSON><PERSON><PERSON>, be<PERSON><PERSON>, koordinat dan ukuran khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:25", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:25"}, {"kompetensi_dasar_id": "f5dba8ad-af82-4c53-a182-9d740ccbbde3", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 831030300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pola dasar rok secara konstruksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "f5dce205-a93c-4bee-9193-664567df1a7c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802032700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar obyek 3 dimensi dengan perangkat lunak secara tepat dan efektif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:34:00", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:34:00"}, {"kompetensi_dasar_id": "f5dcfb4d-8ae3-416e-bedd-3b523e144fed", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perilaku hormat dan patuh kepada orangtua dan guru", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:39", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:39"}, {"kompetensi_dasar_id": "f5de79c6-84c2-40cc-9553-51efa6641c96", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821190400, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar sistem elektropneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:20", "updated_at": "2019-11-27 00:28:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:20"}, {"kompetensi_dasar_id": "f5e05466-0b93-4522-b094-b51ef14fa347", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 825061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengepakan telur konsumsi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "f5e0f219-fea3-4980-ac92-36c3ef14ff06", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memilih substrat se<PERSON>ai jenis ikan hias yang akan dipijahkan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "f5e214b7-b316-4a77-802e-07c8a2fe7349", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja dan kaidah peralatan dalam analisis gravimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:07"}, {"kompetensi_dasar_id": "f5e38fbc-7d19-4e28-ad54-4d14952a9689", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:31", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:07:31"}, {"kompetensi_dasar_id": "f5e43127-e429-444e-905f-71b8748a6ec1", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 803081300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil kekentalan zat cair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "f5e49395-4e7f-4c38-82db-dc582dfc19ec", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 600090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis persyaratan untuk hotel dan industri perjalanan untuk berkomitmen pada praktik yang mencegah eksploitasi seksual anak oleh wisatawan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:30", "updated_at": "2019-11-27 00:30:30", "deleted_at": null, "last_sync": "2019-11-27 00:30:30"}, {"kompetensi_dasar_id": "f5e5c83a-c56e-4857-9469-83dc92e0eb15", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis gravimetri sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "f5e6f589-973f-4ec2-a227-9486097adcd6", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 803071400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengatasi masalah perangkat input pemancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:58", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "f5e818bc-938b-43ce-8a12-c50df8439fc0", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:46", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:32:46"}, {"kompetensi_dasar_id": "f5e84307-fd8b-47a7-ada3-f5d477dbc8d9", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 821190300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi penerangan dan tenaga di industri dan kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f5e97547-cd06-49bf-8f7f-83c8a8d5790f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> hukum-hukum yang berkaitan dengan fluida statis dan dinamis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:20", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 14:59:15"}, {"kompetensi_dasar_id": "f5e9c717-d8c6-44b1-9bc0-50460dc2a281", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 800080220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembuatan dan penggunaan larutan kerja dan larutan standar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:18", "updated_at": "2019-11-27 00:30:18", "deleted_at": null, "last_sync": "2019-11-27 00:30:18"}, {"kompetensi_dasar_id": "f5ea75c7-32b5-4150-9a46-b3219b04049a", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:24"}, {"kompetensi_dasar_id": "f5ea8499-9354-402c-af73-ea052ce8e242", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "f5eb1c4a-85e7-416c-8f06-32b9fe94036a", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perakitan komponen  konstruksi kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "f5ebb5a4-98cf-4a21-86fb-8db4c752218b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:38", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:38"}, {"kompetensi_dasar_id": "f5ed0936-b396-4427-84b8-8811746ac125", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804010210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis material dan ornament dekorasi interior", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2019-11-27 00:27:39", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "f5ed54ae-6932-4f64-b697-49b11e3f1a2f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>k<PERSON><PERSON><PERSON> hukum-hukum term<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "f5ed9d90-18bd-4a30-8792-6914670e649b", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis teknik komunikasi secara tertulis dalam kegiatan perbenihan tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "f5edde36-ed9a-4cf1-b5ba-9cfdc5ba993d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804150500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> rinsip kerja berb<PERSON>i jenis <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "f5ef9375-b087-46a0-bb79-afd56641a16a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401141500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pelayanan farmasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-10-18 06:43:50", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "f5f0a570-bdf7-4181-a4c2-24339e65a2ef", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 800020500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan laporan hasil penyu<PERSON>han kesehatan masyarakat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "f5f0b825-6f8c-4bc8-8bc3-9c20ea1657d0", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, konsep dan prosedur khr<PERSON><PERSON><PERSON><PERSON> (TLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:47", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:47"}, {"kompetensi_dasar_id": "f5f10c9b-2234-4166-b6d9-71ffcf23a216", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 830120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> berbagai desain dan tema rias wajah karakter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "f5f4c67f-f1b9-4fd8-9a6f-0adba80a4be9", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:43", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:43"}, {"kompetensi_dasar_id": "f5f69342-9dbe-4d3d-b385-36f0372ac596", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 843050600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pembuatan karya musik mengunakan program MIDI", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:03", "updated_at": "2019-11-27 00:28:03", "deleted_at": null, "last_sync": "2019-11-27 00:28:03"}, {"kompetensi_dasar_id": "f5f6f936-1441-4537-b60f-d8c2cc9e452c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengujian dan pengukuran peralatan elektronika konsumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "f5f79e2a-caa8-4d38-8efa-e8aec73dbcc4", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 818010200, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> bahan galian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:31", "updated_at": "2019-11-27 00:27:31", "deleted_at": null, "last_sync": "2019-11-27 00:27:31"}, {"kompetensi_dasar_id": "f5fa1e6a-cfae-4d9e-a33a-6ab86d98c17b", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S. Al<PERSON> (8): 72), <PERSON><PERSON><PERSON><PERSON> (49): 12, dan <PERSON><PERSON><PERSON><PERSON> (49) : 10, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makhrajul huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:02", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:02"}, {"kompetensi_dasar_id": "f5fa3171-9859-4564-815a-87c5fccf8f3c", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 839100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil usaha kerajinan fungsi pakai dari berbagai bahan limbah berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "f5fac451-41e0-4400-8bd0-e31ff95a5e6e", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 827200110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengelolaan kualitas air pada pembesaran komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "f5fac57c-236f-4278-ad3b-e15f06814fa2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:18"}, {"kompetensi_dasar_id": "f5fb1d4f-e564-471d-a87e-396156e30fe4", "id_kompetensi": "4.42", "kompetensi_id": 2, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengartikulasi aplikasi motor 1 fasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "f5fbb41d-3ec9-43eb-9df7-95a5d9d18025", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi papan hubung bagi utama tegangan rendah (Low Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:26", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:32:26"}, {"kompetensi_dasar_id": "f5fd9e29-44fa-4a18-876f-062f44f67d5c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843090300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan fungsi unsur pendukung teater dalam pementasan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "f5fe5b4a-cd23-4777-99de-4a5ee920f1b6", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan percobaan aplikasi konsep dasar ilmu kimia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "f5feaed9-4571-4c54-b283-3916d0b07f43", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820110100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami wiring diagram sistem asessories alat berat sesuai dengan buku literatur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "f5fec6af-9234-40f3-841e-da4aae578d89", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisa luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:08", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:07:08"}, {"kompetensi_dasar_id": "f6003435-f835-41d5-b23a-64aac690e763", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 802030310, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik penelusuran Search Engine", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:18", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-06-15 14:50:18"}, {"kompetensi_dasar_id": "f6005338-de19-46ec-9aa5-138a487a9623", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengembangan produk hasil perkebunan tanaman rempah dan bahan penyegar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "f601603b-8a2d-4fc0-90d1-4edad1343efc", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis aspek makna dan kebahasaan dalam teks biografi berkaitan dengan bidang pekerjaan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:15"}, {"kompetensi_dasar_id": "f6016538-60ef-491b-af5a-88bc91dd92f1", "id_kompetensi": "<PERSON><PERSON>\n suci\n <PERSON>", "kompetensi_id": 3, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON>\nfase, peserta\ndidik dapat\nmen<PERSON>,\nmengan<PERSON><PERSON>\nmenilai kitab\nsuci Hindu\nbagian\nupanisad dan\nkodifikasi\nWeda dalam\nHindu dengan\npenerapan tri\nkerangka\nHindu (tattwa,\nsusila dan\nacara) sebagai\npedoman\nkehidupan\npada lingkup\nberbangsa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-18 06:43:46", "updated_at": "2022-11-10 19:57:01", "deleted_at": null, "last_sync": "2022-11-10 19:57:01"}, {"kompetensi_dasar_id": "f601d219-3d78-4e88-a4bb-0a84f7765a07", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:24"}, {"kompetensi_dasar_id": "f6020232-a314-49e8-a502-868a22335344", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan & menerapkan instalasi sistem audio paging", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "f602a681-169d-43f7-b2ef-00c693b5ac1a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan kelayakan ekonomis berbagai produk kimia industri : biaya bahan baku, biaya produksi, harga jual, k<PERSON><PERSON><PERSON><PERSON>, B/C rasio, R/C rasio dan kelayakan ekonomis lain  berbagai jenis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:20", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:20"}, {"kompetensi_dasar_id": "f6034343-f7b9-4925-9fc9-2d7c12187ea3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kehidupan politik dan ekonomi  bangsa Indonesia pada masa Orde Baru.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "f603ed82-8d5e-49f7-81ef-000164fe3300", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 840020140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembentukan benda keramik teknik putar pilin bentuk tabung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "f6045cf9-b3b4-41a3-baf5-e4243be3edbb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instalasi sistem hiburan audio mobil", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:54", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:54"}, {"kompetensi_dasar_id": "f60461aa-9cca-4a29-b3d2-642bce9de75a", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 828210100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan transaksi k<PERSON>angan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:29", "updated_at": "2019-11-27 00:30:29", "deleted_at": null, "last_sync": "2019-11-27 00:30:29"}, {"kompetensi_dasar_id": "f604fa69-a273-4119-a495-b7583e4cd761", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip penggunaan material dan bahan kimia sesuai SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:44", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:44"}, {"kompetensi_dasar_id": "f605e5de-e15a-48c4-bda2-29e156e75545", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis struktur teks, unsu<PERSON> <PERSON><PERSON>, dan fungsi sosial dari teks factual report berbentuk teks ilmiah faktual tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, sesuai dengan konteks pembelajaran di pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:08"}, {"kompetensi_dasar_id": "f607384f-d3c4-46d3-aa01-92b4dfcd3cd1", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 802020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil manajemen backup dan recovery pada linux", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "f608eb6f-6fda-49ea-8c1c-94ddaf5e0328", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan terkait dengan tugas penggambaran di bidang konstruksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "f6092685-838a-4297-bcc9-e8e63b606ae2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 843120500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan piranti tangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:36", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "f60ad96a-22b5-4414-82eb-e3842df1d680", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 800060920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "menerap<PERSON> manipulasi bahan praktik bidang orthodonti", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "f60b3fc0-43ea-4286-a3f2-862f56c2c178", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:19"}, {"kompetensi_dasar_id": "f60bdd6c-5159-4166-8d1d-f0fadfa18675", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur p<PERSON> k<PERSON> baja", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:46"}, {"kompetensi_dasar_id": "f60cc8b3-589d-4467-b6f2-9c4dc5d692c8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 826060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkanpengembangan motif gerak tari", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f60e0f19-07e1-4b0a-aa92-c0f56a7651db", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 842040200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan hasil evaluasi finishing produk kriya kayu dengan teknik bakar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:09", "updated_at": "2019-11-27 00:29:09", "deleted_at": null, "last_sync": "2019-11-27 00:29:09"}, {"kompetensi_dasar_id": "f60e3a70-4d0f-4ac5-bee7-3b3da8abfa29", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 826070100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengukuran areal hutan dengan alat ukur digital", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "f60e58b9-fc62-4709-bc6d-546a5d0a2d8c", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 821200300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> bahan pembuatan kusen, pintu atau jendela", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "f60e768d-8d18-4dfb-a517-8acfdc3f5647", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan pengecekan dokumen sumber dan dokumen pendukung pada perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f610d12b-675d-49e2-a1df-6b8a29c2ea19", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda datar (survace grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:03", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:03:03"}, {"kompetensi_dasar_id": "f6112867-0944-42f3-a85f-91dec1b98378", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan bahan tambahan makannan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:37"}, {"kompetensi_dasar_id": "f6116a34-32f6-448f-a502-30406e9620f8", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 830050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan nail art 2 dimensi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "f6138780-71f2-4367-aca3-939ffe5672a2", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an beberapa teks prosedur lisan dan tulis dengan memberi dan meminta informasi terkait manual penggunaan teknologi dan kiat-kiat (tips), pendek dan se<PERSON><PERSON>, sesuai dengan bidang keahlian dan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "f6149521-a5fb-40ce-8759-c543d01d2491", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 800020600, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memberikan informasi kesehatan kepada masyarakat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "f614bcd6-bb2c-46c3-9778-5fe52483769d", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401131110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengendalian proses program menggunakan computer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "f615fa11-12a0-4dcc-bd33-6da0294881dd", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengembangkan prosedur pem<PERSON>an kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2019-06-15 14:52:55", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "f6161d70-d7d9-4e7d-ab12-0dd86aa6e4ff", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis transaksi penyesuaian antara lain pemakaian bahan untuk proses produksi, pembebanan biaya overhead pabrik, transfer harga pokok produk selesai, penyesuaian biaya-biaya akrual/deferal, dan alokasi biaya oberhead pabrik ke departemen terkait (Harga Pokok Pesanan/Harga Pokok Proses)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:55", "updated_at": "2019-11-27 00:29:55", "deleted_at": null, "last_sync": "2019-11-27 00:29:55"}, {"kompetensi_dasar_id": "f616a862-237e-4967-9e40-c05af68ce729", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (K3LH)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:58"}, {"kompetensi_dasar_id": "f61795b2-71a4-4280-8b6a-ae98299cd7f7", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 815010700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil laporan pemeli<PERSON>an mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:26", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:26"}, {"kompetensi_dasar_id": "f618f93c-6af0-49a0-9794-e046fedb3740", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis ma<PERSON>na <PERSON> (10): 40-41 dan <PERSON><PERSON> (5): 32, serta <PERSON> tentang toler<PERSON>, r<PERSON><PERSON>, dan men<PERSON><PERSON>rkan diri dari tindak kekerasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "f619af11-f2e1-4616-937f-7365f415f081", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 802032200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>em<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "f619c3e4-76e2-4c92-b3c4-ac8014b469ec", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dan aturan menggambar proyeksi dan isometri dalam peker<PERSON>an plumbing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "f61a9fef-bb91-46a6-96dd-89f45d4f99ac", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 800081300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> siklus hidup nematoda", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "f61ad7f5-e4e6-41d2-8964-ffe27116deec", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 827391000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Show insulation tester", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "f61b0fe1-f065-45aa-8c64-c26ba6ffcc15", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 828180120, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> paket perjalanan wisata bahari dan ekowisata lebih dari satu hari dan overland tour", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2019-06-15 14:58:27", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "f61b14e9-1d23-4150-9baa-d65253682a84", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 826160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menetapkan kualitas kayu gergajian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "f61d75b8-807b-4906-9432-acc1dd909cff", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 824060300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan evaluasi terhadap isi program Siaran Online  secara periodik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "f61db545-e20d-432a-ac3f-8de6ba4709ea", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan tari bentuk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "f61e539c-a160-4796-9c5c-a65451d92b37", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis konsep desain/\r\nprototype dan kemasan produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:24"}, {"kompetensi_dasar_id": "f62064fa-f84f-46e2-9336-e7f6098ead6c", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat perencanaan kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "f62140a6-5110-43b5-8048-9f8fe77bd88f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 808010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar karakteristik dan jenis lapisan-lapisan atmos<PERSON>r bumi (Physics of the Atmosphere)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "f62145ec-3a2d-4161-b8a7-8dca846ea4d3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825020500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik persiapan lahan produksi tanaman perkebunan tahunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "f621d25c-3a1e-4aa3-ab5e-fd95e46f9bc8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "f6223db3-a8e7-4808-9c4c-6037d563b973", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 839020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengggambar huruf, logo, inisial, dan slogan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2019-06-15 15:07:26", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "f6229347-d018-47ed-bf0f-a1933063d240", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 821010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "f622f19f-a008-4e41-9bd2-c1285e043f69", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827350800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara menggunakan cat dan peralatan cat sesuai SOP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "f623ea7a-2062-4997-bae2-d9d46d04767e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip dasar penggu-naan kontrol suhu dengan sistem mekanik, elektrik serta  hubungannya dengan interface dalam pembuatan sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:05", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:58:05"}, {"kompetensi_dasar_id": "f6246a12-b479-45b9-ae03-56e60ab67ce0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> gambar kerja yangb \r\nakan dikerjakan pada mesin \r\n<PERSON>ais", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "f626519d-2fd0-4c52-afbb-5eb11602b254", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika pengelolaan kekuasaan negara di pusat dan daerah berdasarkan Undang-Undang Dasar Negara Republik Indonesia Tahun 1945dalam mewujudkan tujuan negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:34"}, {"kompetensi_dasar_id": "f6267a81-e42d-4f61-8808-869cec6fb90e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 823170610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami Analog to Digital Converter (ADC) dan Digital to Analog Converter (DAC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:58", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "f62690b7-9968-4212-af09-c11e50b357d2", "id_kompetensi": "3.60", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis usaha-usaha produktif bagi masyarakat dalam memanfaatkan energi listrik dari PLTMH", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:33", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:33"}, {"kompetensi_dasar_id": "f62776e3-1bab-4228-bb98-15085ebb820c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna surat lamaran kerja.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:05", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:05"}, {"kompetensi_dasar_id": "f629fe0f-6b8c-460d-a30c-2ec118cea15e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 822010110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan kikir pada benda kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "f62adc5d-0ea0-412a-a8cd-231e8bf32f02", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  elemen utama eksterior berdasarkan konsep dan gaya eksterior yang ditentukan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "f62b13d4-40f4-43b2-afe5-1566073b9efc", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami makna keterlibatan aktif  umat Katolik dalam membangun bangsa dan negara Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:51"}, {"kompetensi_dasar_id": "f62b5900-7bab-4b51-925d-a4e5fed86f93", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 826120100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penggunaan teknologi Sistem Informasi Geografis(SIG) dalam bidang reklamasi hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "f62b9d21-a325-4b0a-877d-80e4dfa1c5de", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:49", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:49"}, {"kompetensi_dasar_id": "f62c08af-a443-4c95-8354-bc94e58376ad", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kebutuhan bermain dan rek<PERSON>si", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "f62c2de7-faff-4e81-b23f-2e47bea15741", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825250400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan monitoring dan evaluasi terhadap proses transportasi dan distribusi ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "f62c40bc-966a-4963-9dbd-f4adb2e34f63", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:01"}, {"kompetensi_dasar_id": "f62d6cd7-2d0c-4a88-bc1a-50048b115575", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827350600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain the content, application and Intent of International regulation for preventing collisions at sea, 1972 as amended part D. Rule 32 - 37", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "f62ea2e5-e918-4071-a84d-d7f006912b00", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> peran Allah dalam kehidupan keluarga", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:07:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:41"}, {"kompetensi_dasar_id": "f630780a-0b67-4cdf-bbbc-5f50522ef64a", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan konsep suhu dan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:06"}, {"kompetensi_dasar_id": "f6307bdf-f985-4111-bb08-f3c786b3666a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan faktor yang mempengaruhi struktur bangunan berdasarkan kriteria desain dan pembebanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "f631f862-5406-4ea0-b2c8-3f03ee06c601", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 842010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan evaluasi produk ketok pembentukan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2019-11-27 00:29:05", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "f6336b8a-49b2-49a1-b5ff-2b08d86eff2b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Be<PERSON><PERSON> aktif dalam menjunjung kehidupan yang multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:52", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:53"}, {"kompetensi_dasar_id": "f6336e17-9ac8-4a6d-827d-a314a5af5979", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:57"}, {"kompetensi_dasar_id": "f634da72-dfde-4b99-8746-d7d4658ec021", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 802031920, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perorganisasian aset gambar dan suara digital sesuai storyboard da<PERSON> aplik<PERSON> composing animasi dan perkembangan software animasi 2D (bitmap dan vector)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "f635dfe2-b6c1-4726-b95b-9eaeba021a0f", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 807021320, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Attitude indicator", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:41", "updated_at": "2019-11-27 00:29:41", "deleted_at": null, "last_sync": "2019-11-27 00:29:41"}, {"kompetensi_dasar_id": "f6374437-2631-4e13-9ef8-dc0455bbb260", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 802010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan sistem informasi web", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "f637b468-9ca4-49ac-ab20-0bad1d4ee0c2", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825021100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menerapkan pemeli<PERSON>an kesuburan tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "f637d8b1-7027-4bd5-88cf-fb572c3870f2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:10"}, {"kompetensi_dasar_id": "f6383c23-b4b4-491a-a816-686a4e3642fc", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisa luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:17", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "f638cad4-134d-4bfc-be23-b23a8959bb1c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "f63bcf02-a878-4df0-b85c-ce2d44188dd3", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 825220200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prinsi<PERSON> bahan hasil pertanian secara refraktometri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "f63cf8dd-8bc7-4f7c-b8d5-a99b433d6b2d", "id_kompetensi": "3.45", "kompetensi_id": 1, "mata_pelajaran_id": 822190300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi desain bagian-bagian sipil pico hydro", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:15", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:15"}, {"kompetensi_dasar_id": "f63d1781-c85c-48d1-91f8-8276ba57615b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:05", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:05"}, {"kompetensi_dasar_id": "f63e15fb-104a-484a-b07c-abf620258139", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur kerja terstandar dalam perawatan sistim kontrol yang terhubung komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:13", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:13"}, {"kompetensi_dasar_id": "f63e553a-b1de-43ce-91b9-138ce676da0b", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja sistem kontrol berbasis kontaktor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "f63e86ea-bede-471d-bc2c-b73e41d40e74", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 804100920, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis system jaringan distribusi tenaga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:43", "updated_at": "2019-11-27 00:29:43", "deleted_at": null, "last_sync": "2019-11-27 00:29:43"}, {"kompetensi_dasar_id": "f63fc73a-8a58-42f8-9f71-218a48cc446d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:12"}, {"kompetensi_dasar_id": "f641c80e-12d3-4913-a37b-29907ad30551", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, menyajikan model mate<PERSON>ika dan menye<PERSON><PERSON><PERSON> masalah keseharian yang berkaitan dengan barisan dan deret aritmetika, geometri dan yang la<PERSON>ya.", "kompetensi_dasar_alias": "<p><PERSON>y<span>elesaikan\r\nmasalah yang&nbsp;</span>berkaitan dengan\r\npenyajian\r\ndata hasil pengukuran\r\ndan\r\npencacahan dalam tabel distribusi frekuensi\r\ndan histogram</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:37"}, {"kompetensi_dasar_id": "f642552e-2f29-4906-a590-a0360a3fbe88", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan berbagai jenis tumbukan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "f64308d5-b85b-4786-b448-573f63e5e040", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi kondisi bahan/per<PERSON>si kimia", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:08"}, {"kompetensi_dasar_id": "f6445955-59dc-478f-b1ae-889af7ded611", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:15"}, {"kompetensi_dasar_id": "f6446fdd-25de-43aa-8a11-b26858e7c1c5", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:16"}, {"kompetensi_dasar_id": "f645c96e-cc6f-425f-ab71-1c85b6cd80a5", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 826060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan Inventarisasi Hutan Menyeluruh <PERSON>la (IHMB)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "f645d3da-fb12-4fca-a623-587442276de1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:22"}, {"kompetensi_dasar_id": "f6466958-c37e-465c-9784-983f788d2ef3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan masker pada badan system tradisional dan SPA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "f6471438-2233-43db-82fd-ea760310e2d0", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 822100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>at, men<PERSON>gnosa, memperbaiki CSIT Fail safe  atau On Board Diagnostic system", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "f64770e6-ce28-4d98-bfd4-abf2b537d8f1", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:04:27"}, {"kompetensi_dasar_id": "f6491600-6cc5-4954-80f9-873dcacb8329", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip pengendalian proses  dalam pengoperasian sitem proses terbuka  dan sistem proses tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:25", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:01:25"}, {"kompetensi_dasar_id": "f64a8e05-afdf-41df-8346-34bb469705f2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825061000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan tentang pemberian pakan dan air minum  dalam agribisnis unggas petelur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:07", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:07"}, {"kompetensi_dasar_id": "f64abbff-f8fa-4770-a54b-750df678ecc5", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 843011741, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis unsur musikal karakteristik repertoar ansambel/orkestra jaman <PERSON>ok level menengah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2019-11-27 00:28:09", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "f64b2cc0-903a-4500-bb39-0a59da4c1388", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 807021510, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambarkan skema rangkaian kelistrikan pesawat udara berdasarkan gambar sketsa dengan modifikasi posisi komponen dalam gambar agar lebih mudah diba<PERSON>, menggunakan aplikasi CAD dengan Stencil Simbol baku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2019-11-27 00:29:57", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "f64b45b1-0db9-4426-8965-a8e7b277ba6e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi kotak kontak  biasa(KKB) dan kotak kontak khusus (KKK) sistem satu fasa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:20"}, {"kompetensi_dasar_id": "f64bb3cd-a1db-4e36-b935-6fa26f9d3540", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807022700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> benda kerja <PERSON> Ekor Burung Luar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "f64ccb71-2191-40f3-acb8-363094b25f01", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 825100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis perawatan alat mesin penanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "f64ddfc9-800c-4d76-9d6c-7910033ee08d", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 816050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil proses penyemp<PERSON> khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "f64e24e6-64bb-4c85-be37-2c84e0362d0c", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:40", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:40"}, {"kompetensi_dasar_id": "f64f049a-f690-4dd1-8417-48489f21ba61", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja CAD 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "f64f0a84-c5ab-4f74-a7a3-e97519b61109", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai pelanggaran HAM di Indonesia yang merusak kehidupan dan kesejahteraan manusia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:53"}, {"kompetensi_dasar_id": "f650708b-068e-4f2b-82e9-29a24c5766f6", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 840050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan tahap-tahap pembakaran glasir", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "f650b4a8-a0df-405d-94c0-b18533b9863e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 831030300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>ka jahit dan tubuh model", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:23", "updated_at": "2019-06-15 15:07:23", "deleted_at": null, "last_sync": "2019-06-15 15:07:23"}, {"kompetensi_dasar_id": "f6518ea1-9632-449c-acad-c4599abe1a7f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804160600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> aturan-aturan dan cara-cara pemberian simbol pengerjaan gambar konstruksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "f651993f-f49f-4cdc-ae5b-8cfa212ac9a5", "id_kompetensi": "3.44", "kompetensi_id": 1, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami prinsip dasar metode pencarian kesalahan akibat pergeseran titik kerja DC transistor.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "f6529ef0-3c9f-44ac-a0ce-3738618e688f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 800100100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kepribadian manusia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "f652e978-5d9f-4ddb-ae0d-2be5875a3ef9", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> proses pengecatan warna metalik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "f652f6ff-6866-4f7d-b74f-f7b9e7155033", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825220200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengident<PERSON><PERSON><PERSON> s<PERSON> p<PERSON> uji organoleptik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "f65328f8-5bb5-4ebd-8c96-4ba5b1bd695c", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 807020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> “ lap patch repair of aircraft skin “", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "f653d405-e933-4196-80b7-c5eafaa8073e", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara Kerja Engine Management  System (EMS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "f654d55c-4464-4bad-bf5e-42412a8307d4", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiagnosis kerusakan sistem bahan bakar bensin konvensional/karburator", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "f6562f06-a5a1-4cee-b5d0-842a5cbd0d01", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:54"}, {"kompetensi_dasar_id": "f656f8e6-bdd4-4287-bbf2-1b3114c8f123", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:32", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:32"}, {"kompetensi_dasar_id": "f65707fd-c6cc-4eee-baac-855fed43c256", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perhitungan kebutuhan energi  dan bahan penunjang dalam suatu industri kimia berdasarkan azas kekekalan energi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:24"}, {"kompetensi_dasar_id": "f657b2fe-d54e-4aee-87f6-23526866b8fa", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:29"}, {"kompetensi_dasar_id": "f657f2df-902d-419e-bb07-7f18aa5f72a4", "id_kompetensi": "4.69", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprogram mikrokontroller untuk proses pengendalian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "f6580945-ce33-4a6c-8d66-f3edcb1c0651", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sistim managemen mutu dan produksi (Quality control dan Quality assurance).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:49"}, {"kompetensi_dasar_id": "f65866d1-3a7b-4dbd-93ed-7f239b0c5af9", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari surat lamaran kerja, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:21", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:21"}, {"kompetensi_dasar_id": "f659b7b4-931e-488c-9825-deaac3a58490", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 808020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar bentangan dengan metoda garis segi tiga bentuk benda geometri dengan penampang berbeda bentuk alas bujur sangkar dengan puncak ellip dengan titik pusat semestris", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "f65a0332-0c18-4272-93b3-23c951331e7a", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401132100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan analisis khromatografi gas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "f65a71e5-2bf5-441e-a9c4-f9c99164dc76", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis anatomi dan fisiologi tubuh.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:50", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:12:22"}, {"kompetensi_dasar_id": "f65ac012-0781-40e4-8aad-8a45012ea80c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821060111, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> cara Menggambar dengan program Sketch up", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "f65b6797-75b2-47de-9762-51f87d63b2c5", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan Penerangan Jalan Umum sesuai dengan PUIL", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "f65d0a2e-2258-49a8-9234-1b318d0736fd", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 825010220, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan mesin bor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:40", "updated_at": "2019-11-27 00:28:40", "deleted_at": null, "last_sync": "2019-11-27 00:28:40"}, {"kompetensi_dasar_id": "f65d11e9-1a6a-45fe-9396-18681437ea32", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:40"}, {"kompetensi_dasar_id": "f65f702c-e845-438b-941b-6e2e3e1e5e0c", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami cara  kerja suspensi unit alat berat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "f65f704d-0913-41fa-a91d-0d7d25e56e4e", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 821090300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan menci<PERSON> konstr<PERSON><PERSON> kapal <PERSON>las dengan  sistem modul 1", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "f660690e-eae4-4ac1-8ce1-f5ccdaaa7834", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi penggelapan kulit badan dengan heliotherapy teknologi suntan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "f6616578-fba6-4b51-8ee6-9484e01dc28a", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/ prototype dan\r\nkemasan produk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:32"}, {"kompetensi_dasar_id": "f662e2f0-92f0-47b4-a6e2-4701bf44085a", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memformulasikan cara mengemukakan hubungan sebab akibat dan hubungan kebalikan serta hubungan perbandingan, se<PERSON>ai dengan konteks penggunaannya, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulis yang melibatkan tindakan menyatakan dan bertanya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:58", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:56:38"}, {"kompetensi_dasar_id": "f662e485-0c91-423e-ac9e-ebbaadbd8a6b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis persyaratan tumbuh tanaman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "f663d4db-5561-41ce-b692-6ff7159c2b8b", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 803081200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang cara mengkalibrasi macam-macam komponen sistem otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:24", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:03:24"}, {"kompetensi_dasar_id": "f66626c9-12dc-4546-ae9b-2c872ff3408b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:56"}, {"kompetensi_dasar_id": "f6672ff7-92e1-4e6b-9572-2291357387c6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sortasi dan grading ikan yang akan di panen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:25", "updated_at": "2019-11-27 00:29:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:25"}, {"kompetensi_dasar_id": "f668a92c-a957-41af-9fda-2d5492a5c8c7", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang perbaikan konstruksi bangunan gedung yang tergolong restorasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "f6691838-b7ae-402d-b1a9-11c2d195cac9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an pada ungkapan meminta perhatian bersayap (extended), serta responnya, sesuaidengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "f66a0d24-3fbd-47b3-8eed-0196af744a58", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 825100200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur traktor pertanian roda dua sebagai alat angkutan bidang pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:46", "updated_at": "2019-11-27 00:28:46", "deleted_at": null, "last_sync": "2019-11-27 00:28:46"}, {"kompetensi_dasar_id": "f66a5624-4f70-47b1-a076-7325610d60b9", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "memahami  alat dan bahan kebutuhan artistic tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}]