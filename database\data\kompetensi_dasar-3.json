[{"kompetensi_dasar_id": "03360e9a-f67b-40a1-b903-5ff47f020940", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 800061300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan disinfeksi alat dalam pengendalian infeksi silang di klinik gigi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "03362ee6-53f9-4acb-aae5-66c4b362fb57", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, menyajikan model mate<PERSON>ika dan menye<PERSON><PERSON><PERSON> masalah keseharian yang berkaitan dengan barisan dan deret aritmetika, geometri dan yang la<PERSON>ya.", "kompetensi_dasar_alias": "<p><PERSON>y<span>elesaikan\r\nmasalah yang&nbsp;</span>berkaitan dengan\r\npenyajian\r\ndata hasil pengukuran\r\ndan\r\npencacahan dalam tabel distribusi frekuensi\r\ndan histogram</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:57"}, {"kompetensi_dasar_id": "03374007-16c3-4efb-a653-92080d8d87e4", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil penyelidikan mengenai cara perpindahan kalor", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:41"}, {"kompetensi_dasar_id": "033a1eab-758a-4df7-95e1-5bf100545867", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan sifat turunan fungsi terkaitdan menerapkannya untuk menentukan titik stasioner (titik maximum, titik minimum dan titik belok).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:02"}, {"kompetensi_dasar_id": "033ab855-2cd6-434e-b7da-4513e19cc523", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistem mesin 2 Tax", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "033b3c0b-7cf0-4720-b9b9-d02ba159be93", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelas pipa baja dengan proses las SMAW pada posisi horizontal/mendatar (2G pipa tidak diputar)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "033c7897-ea6f-4637-a61b-5a8eee8830d0", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:00", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:07:00"}, {"kompetensi_dasar_id": "033d2031-c372-4a39-8e06-d07edecbd688", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:57:26", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:26"}, {"kompetensi_dasar_id": "033e212a-bb1c-43c8-897a-98bc0b5ad059", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820140300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil perawatan berkala <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:20", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:03:20"}, {"kompetensi_dasar_id": "034105ab-d2c3-4e3b-b8c9-11ba0e836ac3", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804210200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan identifikasi penyebab kecelakaan di lingkungan kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:27", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "03413044-a3e2-4264-8058-0f34c43ec45e", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 816010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi nyaman dasar dan turunan anyaman polos", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "03415dc7-9379-413d-bdbd-dffd5e5fe861", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:55", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:55"}, {"kompetensi_dasar_id": "0343f9a2-5c7e-41da-b179-888dfc426bea", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:37"}, {"kompetensi_dasar_id": "03459808-8107-40a3-8ee6-0d0b3893acce", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat dan karakteristik limbah dalam penanganan limbah B3 dan non B3", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:33", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:33"}, {"kompetensi_dasar_id": "0345d634-0e33-4d99-af8b-054c8e20372d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 817160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kesehatan dan keselamatan kerja dalam pekerjaan peledakan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "03462fba-18c7-4e13-8ed0-03bd99cd956d", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827260100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>, menyaji dan menalar laju pertum<PERSON>han spat kekerangan (semi intensif, intensif dan monoculture integrated)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "034647a0-4668-47b5-a8ea-ca887be23684", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 824050900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> konsep penyutradaraan televisi dan film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "0348de88-01e7-412c-aebf-ef230294a40a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843062000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memformulasikan teknik pengembangan melodi instrumen dan vokal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "03491d21-3d93-4a66-8156-eff1df4f26fb", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 835010200, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik cetak ganda relief", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "034a55b1-d0db-4133-84a2-072e498f8c2a", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 800081100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan laporan hasil evaluasi pemeriksaan serologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "034cbd65-2abd-4ad7-88b6-f0169bcf8089", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804132500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur pengoperasian mesin CNC (bubut dan frais)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2019-11-27 00:27:54", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "034d3e8b-7767-40b1-93f6-b8c5100056fd", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825250600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan rekayasa pakan untuk meningkatkan kualitas ikan hias", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "034d6d09-bbf1-49d6-996c-578f005caa70", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 843090600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis nilai estetik artistic tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "034d74c5-6338-482d-adcb-94970674c9b9", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktekan fungsi panel tegangan menengah 20 kV dan trafo daya off line", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "034e54ad-d120-4e67-b17a-ceeb9911c4d1", "id_kompetensi": "4.29", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menyelesaikan masalah yang berkaitan dengan ukuran penyebaran data tunggal dan data kelompok", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:07", "updated_at": "2019-11-27 00:30:07", "deleted_at": null, "last_sync": "2019-11-27 00:30:07"}, {"kompetensi_dasar_id": "034fa03b-6a91-4474-b389-d7129fa4e6f4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain precautions when beaching a vessel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "03502eee-07ed-4fd4-8bf6-f6c97ae0ec3f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan alat ukur elektrik serta fungsinya", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:39", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:06:39"}, {"kompetensi_dasar_id": "03519eff-503f-4b03-b7f9-8f9627fdbacd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai pelanggaran HAM di Indonesia yang merusak kehidupan dan kesejahteraan manusia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:27"}, {"kompetensi_dasar_id": "03523ad9-d09c-415b-a816-70c62b3d9e26", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:52", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:52"}, {"kompetensi_dasar_id": "0352a919-671c-4547-a6eb-8b048b208bf5", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 800020410, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi tindakan triage", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "0353d81a-84bf-4bd2-924c-185271d12391", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:36", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:36"}, {"kompetensi_dasar_id": "0354ce44-e6b7-4be0-b525-1415aec2d70e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820030600, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan proses operasi turbin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "0358d346-ffa4-4569-ac28-7b758acc8a8a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 802040500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat pola tata letak isi tabloid.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "0359918b-a49d-4e80-9483-6441f125faa0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan dan merawat measurement of fow and volume of blood", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "035a3566-3d6b-47b9-bcae-8b45ae80942b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804100820, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "enggambar simbol-simbol katup pneumatik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:44", "updated_at": "2019-11-27 00:29:44", "deleted_at": null, "last_sync": "2019-11-27 00:29:44"}, {"kompetensi_dasar_id": "035b2008-8c1b-41ec-b4f3-a6f6fb87754e", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan dari teks news item berbentuk berita sederhana dari koran/radio/TV, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:01", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:01"}, {"kompetensi_dasar_id": "035b99a9-0cde-4ea2-a414-2caa304d7ab2", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksana<PERSON> proposal usaha tanaman herbal/atsiri.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "035bc8aa-3fa7-4d2c-baad-871175240657", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:41"}, {"kompetensi_dasar_id": "035c3c8a-7287-4c0c-b79c-e15d700c8c67", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827040200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Describe initial assessment of damage and damage control", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "035ce139-cb77-46b0-a13e-35278c5213d7", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 821010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data hasil analisis berdasarkan pengamatan tentang gambar layout galangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "035d1b5f-a6b7-4f0f-b835-47519df2b7b8", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 825250600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengendalian hama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "035d25bb-c655-4525-a798-0107d5ddac9b", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:48", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:48"}, {"kompetensi_dasar_id": "035ec0de-4fbe-450e-9014-6b181b478144", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan teknik analisis secara potensiometri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:36"}, {"kompetensi_dasar_id": "035f560d-f61a-48f9-bfda-f4a82675138b", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan prosedur pengoperasian macam-macam system hidrolik melalui gambar rangkaian sirkuit hidrolik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "035fed51-4520-4ef7-9e34-83d9c57637e1", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola menyerang dan bertahan) salah satu permainan bola kecil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:46", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:46"}, {"kompetensi_dasar_id": "0360b2f7-cea5-46a9-9eb1-c02c7ef729d2", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401260000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis norma dan nilai yang berlaku di masyarakat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:24", "updated_at": "2019-11-27 00:30:24", "deleted_at": null, "last_sync": "2019-11-27 00:30:24"}, {"kompetensi_dasar_id": "0361d27e-4e8f-41b1-89cb-4203ed35c832", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>   dan Menerapkan konstruksi penegar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "0362877e-07d1-4275-a45b-657c802f1be8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825240110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan usaha produksi hasil perikanan tradisional (pen<PERSON><PERSON><PERSON>, pen<PERSON><PERSON>, pem<PERSON>ngan, pengasapan dan fermentasi)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "0365510b-622c-45a2-85dc-19cea5daf786", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:50", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:26:50"}, {"kompetensi_dasar_id": "036581c0-3281-4e60-ad11-09b3482373ff", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 300310100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Membandingkan tindak tutur yang menyatakan dan menanyakan perbandingan jumlah (muqa<PERSON>h al<PERSON>), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan dari teks interaksi transaksional lisan dan tulis, sesuai dengan konteks penggunaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "03658513-3b88-435b-9a17-e50f2c4c4b0d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 805010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalis koreksi kualitas data.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "0365918d-700d-4052-b267-178635712b93", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:52"}, {"kompetensi_dasar_id": "036618f2-f0b0-460e-8d8a-8c04d13556e2", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  mesin gerinda silinder/cylindrical grinding machine  untuk berbagai jeni<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:53", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:53"}, {"kompetensi_dasar_id": "03668d9d-ec50-475f-a358-a6d35af1f99e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perangkat lunak CAD 2D", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "03685f79-901e-4141-bd60-863e308ac033", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 816050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil proses penyemp<PERSON>aan fisika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "0369c06e-aad7-4371-9091-d168ca4eccb4", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 817110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi gangguan operasi pada proses pengolahan migas dan petro kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "036a506d-b45a-4798-84a2-ab34b888d493", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 825250500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang inovasi teknologi pembesaran ikan hias dengan teknik resirkulasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "036a9c2b-799c-4e02-a984-dc7f7416fa4e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghubungkan prinsip mekanika teknik dan kondisi tanah pada gambar bendungan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "036aa802-fcfc-40c4-bb75-0eb533bf3212", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonst<PERSON><PERSON> per<PERSON>/isu, sudut pandang dan argumen beberapa pihak, dan simpulan dari debat berkaitan dengan bidang pekerjaan secara lisan untuk menunjukkan esensi dari debat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:53"}, {"kompetensi_dasar_id": "036b9d26-936f-4b84-ad1c-08b11489e62a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is macam-macam gaya dalam struktur bangunan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "036c8774-3896-4591-b67d-a3f34409129c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "036e143e-e247-4d74-8ff9-cc998cbe56ed", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 824060200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengulas kegiatan reportase sebagai siaran laporan langsung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "036e7798-825b-4d59-93a0-813c064c2465", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memproduksi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel yang koheren sesuai dengan karakteristik teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:17"}, {"kompetensi_dasar_id": "036ec62f-662e-4566-8279-5494a8b62bfd", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:30:19"}, {"kompetensi_dasar_id": "036f7310-4420-4fa5-a123-033209dd0bcc", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan   teknik  pembuatan benda kerja   pada mesin bubut, dengan su<PERSON>n/toleransi khusus.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:00"}, {"kompetensi_dasar_id": "036fe565-3cf1-4b96-8229-a99838b1e5aa", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:52", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:52"}, {"kompetensi_dasar_id": "0371ca8b-0fb9-4814-bc78-c35db00ff675", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi taktik dan strategi permainan (menyerang dan bertahan) salah satu permainan bola besar dengan peraturan terstandar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:23", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:23"}, {"kompetensi_dasar_id": "03723ade-4e36-4b20-9ed9-3295bb3297b3", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 820070200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki rantai penggerak roda belakang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2019-06-15 15:03:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "03748f87-7b58-49d3-864a-aac77f572390", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses fisika dan proses kimia dalam industri phospat dan boron.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "0374908b-e5cd-4f7c-9347-0b17783a9984", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan hakekat ajaran wariga dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "03753e4d-c567-4d71-827f-6178bd64b995", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:30", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:30"}, {"kompetensi_dasar_id": "037546eb-aa6a-4153-8bad-efe8ab2f9a95", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 820070200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengi<PERSON><PERSON><PERSON><PERSON><PERSON> jenis-jeni<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2019-06-15 15:03:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "0376155b-87e7-4a3e-81c4-056a3bfc84a2", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, dan merancang koreografi aktivitas gerak ritmik, serta mengevaluasi kualitas gerakan (execution).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:21", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:21"}, {"kompetensi_dasar_id": "037871af-1314-40dd-94e4-881b6071d244", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>pta karya pengolahan dari bahan nabati dan hewani menjadi produk kesehatan yang berkembang di wilayah setempat dan lainnya sesuai  teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:11", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:11"}, {"kompetensi_dasar_id": "0378ab41-0808-4fc8-b2ae-b1da92c187b6", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401251230, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontrasikan pemasaran dengan situs mobile", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "0379e275-a8a8-418f-b0e9-f622f7038ef5", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 822050110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON> kecepatan silinder pneumatik untuk gerak maju dan mundur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:29", "updated_at": "2019-11-27 00:28:29", "deleted_at": null, "last_sync": "2019-11-27 00:28:29"}, {"kompetensi_dasar_id": "037a9aad-dfc7-4b84-bfdd-7aec52f9ae65", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821050100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah dan menyajikan gambar lay out", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "037b2c72-38cc-4075-b6b4-24dc6c907d4e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:04"}, {"kompetensi_dasar_id": "037b92aa-dc5e-492c-b2b3-9541c228b3c0", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar konstruksi saluran irigasi sesuai spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "037bd0c7-e48a-4e93-84c9-ff652c8d90b6", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 802031501, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis cara <PERSON><PERSON><PERSON> ruangan dan lingkungan kerja (5S - Shine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "037c9c83-179b-41ff-b3d0-5d50bdc56e33", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis dan penggunaan potongan kerangka besi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "037e7421-927f-40a9-87f9-c0e76267008c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802040800, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur k<PERSON>, kese<PERSON><PERSON>n dan keamanan kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "03811185-1f34-465d-99ff-edc739cf58b8", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825250200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganali sis kandungan minyak atsiri secara volumetri/refraktometri pada bahan hasil pertanian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "03818a88-7988-4735-ab70-0e37e2bd3893", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:27", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:27"}, {"kompetensi_dasar_id": "03833250-4ecc-41df-9d08-05ade167be27", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804100700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi <PERSON>i pada gardu induk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "03838614-cdb2-476e-bc50-126dc6b881e7", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 820070400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON>  kerja tim (team work)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:18", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:18"}, {"kompetensi_dasar_id": "0385241b-d34f-4f56-9a2c-36a2a8de841c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:24", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:00:24"}, {"kompetensi_dasar_id": "03855464-c5c2-41b6-b257-7125099f6776", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 828080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Mengevaluasi kegiatan administrasi keuangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "03865afa-b375-4563-8526-316e44d078df", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802031920, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan perangkat pemindaian digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "038690ea-6d02-4585-894e-5a61be4d45f5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825210500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip Teknik Laboratorium", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "0386e97d-4166-46d2-b2a1-33e2b6617ab4", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:11:33", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:11:35"}, {"kompetensi_dasar_id": "03872248-e5c0-4e51-8abe-5d5c78bfd2ff", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:48", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:48"}, {"kompetensi_dasar_id": "03876b3a-190f-48b7-b931-6f6f8d02d1b9", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 817130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa karakteristik berbagai macam jenis tangki", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "0388d351-b92d-402e-9ea0-90c0fb31185c", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:15"}, {"kompetensi_dasar_id": "038931fe-c203-4bce-bd46-d1a53d1a1434", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401130200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dan karakteristik jenis kebakaran dalam prosedur pengg<PERSON>an <PERSON>at Pemadam <PERSON> (APAR)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:59:56", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:59:56"}, {"kompetensi_dasar_id": "0389b93b-6543-4016-a468-e77155d911f7", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan nilai moralitas berdasarkan agama Buddha.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:01:33", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:13:36"}, {"kompetensi_dasar_id": "0389f380-50cb-4fe1-8e59-55871b524a0c", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 830050040, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis perawatan telinga", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "038a3877-85ed-4cd3-bf4c-3c2ce0e70efd", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827140200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasian ikan berdasarkan sistem pencernaannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "038a3dc4-67a6-4027-b4c5-858c082c326d", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 401141400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi biomolekul (karbohidrat, protein, lipida) berdasarkan uji kualitatif", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "038c5f86-c722-47e8-8fd0-cb41df4a2415", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 842040400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi produk kriya kayu teknik bubut dua senter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "038ce697-5be8-4505-af21-fc448a3a4625", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan gambar kerja pemasangan instalasi listrik kawasan berb<PERSON> (Hazardous Area).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:17", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:07:17"}, {"kompetensi_dasar_id": "038cec04-06e6-45c0-acac-41b42b561948", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804120300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengelasan pelat dengan pelat pada sambungan sudut posisi di bawah tangan dengan las gas metal (MIG/MAG)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:24", "updated_at": "2019-11-27 00:29:24", "deleted_at": null, "last_sync": "2019-11-27 00:29:24"}, {"kompetensi_dasar_id": "038edb70-e885-40a6-845c-1d2d047e353d", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 800081300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeri<PERSON><PERSON> jamur dari sampel makanan dan minuman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "038f8755-b7eb-4839-9b17-f84fddd72725", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 830090200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan penataan rambut dengan teknik kepang dan pilin sesuai kreasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:51", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:23"}, {"kompetensi_dasar_id": "038f9c9b-b806-47ce-bab3-5ee4a5dc8ecb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:13", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:13"}, {"kompetensi_dasar_id": "038ff63b-4855-4a7f-8570-4c521df891bd", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah teknik bentuk (technique of the form) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "0390efbf-131d-474a-8054-cc6d7a0e3d20", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami teknik bentuk (technique of the form) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "0391ece3-946b-400b-9c57-e9c64ab90495", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804160800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memah<PERSON> konsep dasar Computer Aided Manufacturing untuk proses Turning (CAM Lathe)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "039261c8-d8a8-4f48-ba48-a3e1530ccefd", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 805010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencoba pengukuran survei teknik sipil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2019-06-15 14:49:58", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "03937aab-3461-49d7-a342-59b694630e79", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 828070200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Melakukan pengelompokkan sistem administrasi kepegawaian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "039398f8-e0fa-4e85-84eb-a4f09ff6b384", "id_kompetensi": "4.55", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> kondisi dan unjuk kerja peralatan kendali hidrolik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "03958af9-8492-4b76-8a3d-45cbf999c5f4", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 825063400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pemanenan hasil ternak ruminansia pedaging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "0395e4b3-3d8a-4e83-b204-03f22eba7fd0", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 817130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukan macam macam tangki timbun", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "0396354a-17b4-4b08-98a9-b1b88c8dac0b", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukanperhitunganberbagai proses berda<PERSON><PERSON><PERSON><PERSON>ter<PERSON>dinami<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:41", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:41"}, {"kompetensi_dasar_id": "0396b005-0023-4199-9dd9-d47a90db3e7e", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan panel kontrol sistem refrigerasi dan tata udara komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "039732cf-8803-4cf9-9d1e-985bd559a1e0", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 825100300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengidentifikasi komponen-komponen alat mesin sortasi/grading/pemisah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "039745f5-f51f-481e-aa49-ce8fb6f4d24c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pasca panen tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "03996b39-e72f-456b-a337-03e321e5b801", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi transaksional lisan dan tulis pendek dan sederhana yang melibatkan tindakan memberi dan meminta informasi terkait jati diri, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:02"}, {"kompetensi_dasar_id": "039af526-3287-4c0c-b217-225b12b5ba78", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 803071100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa parameter komponen jaringan Wireless LAN", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "039b2fb1-2245-41b5-bc96-5217b924df09", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> nilai-nilai multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:53"}, {"kompetensi_dasar_id": "039c7ebb-3432-44eb-a666-c8e209730334", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 807020610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan dasar-dasar pada sistem aircraft hydraulic & pneumatic systems", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "039c89b1-a00e-47e8-8646-1f59119167ce", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 821140500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami penggambaran layout kamar mesin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2022-10-18 06:44:03", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "039eff78-d15e-4f10-95bc-f823526a5d8f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> sikap dan perilaku\r\nwira<PERSON><PERSON>an", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:31", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:31"}, {"kompetensi_dasar_id": "039fdb23-754e-4168-b164-7228d1e95d17", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "03a0e825-3008-432f-9fda-5d221a380e8f", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:39"}, {"kompetensi_dasar_id": "03a21794-f44d-4076-a24a-c5457fdb4c41", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Me<PERSON><PERSON>t alur dan proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:00", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:00"}, {"kompetensi_dasar_id": "03a3d954-e8e0-44c0-892e-f7ab6acfae5f", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki kesalahan pemasangan plafon kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "03a48fc7-ecce-4861-8f05-80d1e90f6821", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:02", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:27:02"}, {"kompetensi_dasar_id": "03a57385-0fb3-4d3a-b67c-f4d677289a5c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat struktur organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:04"}, {"kompetensi_dasar_id": "03a65b05-ba30-40c9-8112-fa07f86f4d4d", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 825140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pembuangan irigasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "03a667b5-c570-4c6f-b29e-bfdaab7d9b3a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401131110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan peralatan gambar teknik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "03a76ca1-566f-4544-b990-e55e856be4e9", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804111000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemograman mesin frais CNC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "03ab0a0d-209b-479e-b61e-5ff4f77414c7", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 816070100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah pencegahan kecela<PERSON>an kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2019-06-15 14:50:05", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "03b0badf-4ce0-4b6b-8a55-149bdfc32d24", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:33"}, {"kompetensi_dasar_id": "03b108eb-2a98-4d60-9a93-abd38b76508f", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil proses orthofoto", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "03b18ffd-a263-447a-8531-0b1215865db8", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melak<PERSON><PERSON> pengukuran dan pematokan (staking out)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "03b1aa44-4f47-4d7f-8917-0c23c261f5fa", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803071100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perhitungan perencanaan Topologi jaringan computer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "03b290c0-5875-4454-9676-c1f87f100efb", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil telaah tentang kontribusi  bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON> serta menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:39", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:39"}, {"kompetensi_dasar_id": "03b2d31f-2bfb-4e91-8c16-58f360c09667", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan sterilisasi dan uji sterilitas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:54", "updated_at": "2019-11-27 00:29:54", "deleted_at": null, "last_sync": "2019-11-27 00:29:54"}, {"kompetensi_dasar_id": "03b2f7a6-6a83-4f4e-bda2-c153fa14a573", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 807022700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> perlen<PERSON>pan mesin frais", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "03b3ebdc-37bb-45c1-b01d-5325f159afe5", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pengukuran massa jenis dengan hydrometer tangan.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "03b47ad8-e8aa-43d5-93e1-3305497a6164", "id_kompetensi": "3.3.2", "kompetensi_id": 1, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>  karya tari  berda<PERSON> simbol, j<PERSON><PERSON>, dan fungsi dengan beragam teknik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "03b5c014-b595-44c7-9b19-b2d81cf04b04", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "03b7d0f5-a982-473a-902a-1288506d82da", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401130910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bahan bahan tekstil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "03b95713-ef19-4409-8ea5-74c9d091592f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825063400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan persiapan kandang dalam agribisnis ternak unggas pedaging", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "03baa951-07f3-4af2-816d-1f6ffaef336b", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menujukkan teknik ekstraksi dan pengeringan benih tanaman pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "03bb875d-b871-4cbc-8b1a-a0d512d553d7", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 820070200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki system kemudi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2019-06-15 15:03:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "03bbc6a1-9d39-46da-ab91-9bc7f0356cad", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Men<PERSON><PERSON>aikan nilai nilai sudut dengan rumus jumlah dan selisih dua sudut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:07"}, {"kompetensi_dasar_id": "03bd9c6a-30f2-43aa-91e3-8f01cf7cd7c5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 827190110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerangkan prosedur teknik pendederan komoditas perikanan di bak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "03be14a3-b49e-420f-8b9d-e757f0b46349", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804150500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> peker<PERSON>an pemeli<PERSON>an sistem pengendali kelistrikan motor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "03be4ed3-ac76-4d86-b46c-89332e7c8cc6", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 828041100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Mengoperasikan aplikasi pengolah presentasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:50", "updated_at": "2019-11-27 00:29:50", "deleted_at": null, "last_sync": "2019-11-27 00:29:50"}, {"kompetensi_dasar_id": "03bef5aa-363f-4c04-ba98-365441c8d901", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 816010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pengu<PERSON>an benang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "03c01327-e09c-46c1-9fbc-7a49a152ebab", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menya<PERSON><PERSON>g Air\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 14:50:17"}, {"kompetensi_dasar_id": "03c251fb-137a-4caf-93db-0827a505ed6f", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 401251190, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencatat transaksi Letter of Credit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "03c2660a-4b52-4afc-914b-4ac71aa1e719", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 401131600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan hasil evaluasi data analisis kadar lemak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:26"}, {"kompetensi_dasar_id": "03c2a4a2-6929-4345-a1e3-b9a8f9d005fb", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:16", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:16"}, {"kompetensi_dasar_id": "03c33a46-5890-4955-aedb-65acdb47cd36", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:30"}, {"kompetensi_dasar_id": "03c3831e-1ddc-4ab0-99f1-31929f151632", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:11"}, {"kompetensi_dasar_id": "03c4dec2-b8fc-4c32-af2f-89d2942b5689", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 825250400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penampungan berbagai jenis ikan hias untuk farm ikan hias/toko ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "03c52279-464c-4d37-a3c5-fd4956993186", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks penyerta gambar (caption), se<PERSON>ai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:51"}, {"kompetensi_dasar_id": "03c60d9c-246b-47c4-95ed-43d89f57a369", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:38"}, {"kompetensi_dasar_id": "03c71054-ecc1-4996-94bd-bd16fa1f0489", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur perawatan dan perbaikan atap dan plafon", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:13", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 16:04:13"}, {"kompetensi_dasar_id": "03c8a68f-f7c9-48d5-8851-6581a0f0793d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401141700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara standarisasi dan evaluasi bahan pengemas primer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:12", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:12"}, {"kompetensi_dasar_id": "03c8de84-a77b-47ae-b48e-dee4521a74a5", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keseimbangan tektik tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "03c9b98b-cbc1-4c56-bbdf-8b8fdcfe0fd5", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 826050400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami media penyuluhan kehutanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:50", "deleted_at": null, "last_sync": "2019-06-15 15:06:50"}, {"kompetensi_dasar_id": "03caa90e-89e3-45fe-aac6-a3a188813558", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "03cb8c15-fa09-4dd7-ab17-36908b3f4021", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:43", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:43"}, {"kompetensi_dasar_id": "03cbe6a6-6789-47c2-affa-555d0b067d2d", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 800061100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melakukan penggolongan obat dalam tindakan perawatan gigi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "03cc493d-7949-479a-971b-48b265353cd9", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 801031500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Menerapkan komunikasi dalam praktik pekerjaan sosial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:25", "updated_at": "2019-11-27 00:30:25", "deleted_at": null, "last_sync": "2019-11-27 00:30:25"}, {"kompetensi_dasar_id": "03cc4f01-4584-4f1d-bc1f-9504c2495bb3", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 803070200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan berbagai macam system power drive (pneumatic, hydraulic, dan electronic: SSR, inverter, bridge amplifier) pada peralatan mesin kontrol", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "03cd2317-ae03-4ae6-911b-0322a80d2f31", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821100100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam pompa <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "03cdd8af-d59b-47bf-89d2-971fe937ad10", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 839100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami desain produk dan pengemasan karya kerajinan fungsi pakai dari berbagai bahan limbah berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "03ce390a-ded4-4169-b4d8-b7966868f934", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825270200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan standar dalam pengujian mutu rempah-rempah dan produk o<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "03ce7a63-e218-48e1-9f4e-f5145f2755f4", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 829080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "        Membuat saus (mother sauce) dasar dan turunannya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "03cf4945-0dd0-4174-b3d7-c343a0f0bc40", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat kultur pakan alami artemia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "03cfc221-4984-433b-9823-f2df4e615dc3", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur per<PERSON> ter<PERSON> kor<PERSON>i", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2019-06-15 14:52:55", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "03cfc856-fd50-4c84-8b53-c88f393d3de2", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:11", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:11"}, {"kompetensi_dasar_id": "03d0b909-fee2-40d7-b929-9b56aa53bff8", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 843110300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan teknik aksi reaksi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2019-06-15 14:58:22", "deleted_at": null, "last_sync": "2019-06-15 14:58:22"}, {"kompetensi_dasar_id": "03d0c67f-042a-4f0c-a369-4c55515cc888", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sifat-sifat transformasi geometri (trans<PERSON>i, refleksi garis, dilatasi dan rotasi) dengan pendekatan koordinat dan menerapkannya dalam menyelesaikan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:01"}, {"kompetensi_dasar_id": "03d0ccfe-681e-4e39-a46f-14465bab7df3", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perawatan badan pada SPA dan teknologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "03d1a1a9-16fe-4c3d-b1a2-1963fb52b7ee", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar <PERSON>leng<PERSON>pan Hu<PERSON>ng Bagi (PHB) Penerangan 3fasa untuk lapangan olah raga sesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "03d2ebea-6595-4b0c-b96e-96a67298c948", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 808040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami konsep dasar prinsip kerja GTE (GTE Fundamentals)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "03d56906-1b8b-43cf-a21d-0fab0c3fda05", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifi kasi batu gerinda untuk berbagai jenis pek<PERSON>an pen<PERSON><PERSON> silinder", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "03d60798-6d41-411c-aae3-590dcab24b0f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "03d68df8-4ed7-4afd-8791-2907b046ca29", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keterampilan 4  gaya renang untuk memperbaiki keterampilan gerak, dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:46", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:57:46"}, {"kompetensi_dasar_id": "03d8b46a-1321-43b7-8ad4-a38aaae23053", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 401130620, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi data hasil analisis kadar vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "03db3e83-e96c-4414-8176-39725ed40ed7", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kebutuhan bahan dan alat produksi program televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "03dcecdc-6e03-48e0-a8b2-61f34b6d45e6", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 815010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> per<PERSON> mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "03dd8d14-26eb-49d4-a053-6062e6bbe05b", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep persamaan lingkaran dan menganalisis sifat garis singgung lingkaran dengan menggunakan metode koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:02"}, {"kompetensi_dasar_id": "03dec47b-7660-4d06-811c-833672c6f6d8", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 808020200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis benda geometri gabungan kerucut dengan selinder tegak lurus dengan garis sumbu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:45", "updated_at": "2019-11-27 00:29:45", "deleted_at": null, "last_sync": "2019-11-27 00:29:45"}, {"kompetensi_dasar_id": "03e0dc71-247b-436c-a939-eb0c91177a63", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:33", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:33"}, {"kompetensi_dasar_id": "03e1043c-ca17-4f9d-8937-9b5bedc5f895", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401240000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengelompokkan masyarakat sesuai dengan stratifikasi sosialnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:24", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:24"}, {"kompetensi_dasar_id": "03e1344d-3504-45b1-98ce-bc1f36442809", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran bangsa Indonesia dalam perdamaian dunia antara lain KAA, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gerakan Non Blok, dan ASEAN, OKI, dan Jakarta Informal Meeting", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "03e22d20-12d0-43f4-a986-1758f1d6d1e8", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan instalasi kontrol motor berbasis programmable logic control (PLC).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:58", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:58:58"}, {"kompetensi_dasar_id": "03e25a70-2f2b-49e3-87f9-c953fa625edd", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 401141210, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengidentifikasi simplisia <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2019-11-27 00:30:21", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "03e28f74-fc0e-4a82-8fb5-87a113043af9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:14"}, {"kompetensi_dasar_id": "03e3553a-6ac5-481c-9978-8cb56313ac88", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis piranti pendeteksi liquid level", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "03e59ed6-afb3-420e-96a7-90a3109fc731", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843090400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Meragakan teknik perna<PERSON>san dalam seni teater", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:35", "updated_at": "2022-10-19 23:19:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:21"}, {"kompetensi_dasar_id": "03e6dda6-f230-492c-a7de-5cc371323506", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 825040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan teknik penyiapan laboratorium kultur jaringan tanaman hortikultura", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "03e71515-e903-41fb-9ccd-e7ce5d5c1d65", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804010300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan  pembuatan gambar rencana dengan soft ware mencakup persiapan ruang, bahan, peralatan, dan keleng<PERSON> kerja, perencanaan dan mengorganisasikan gambar,  gambar rencana dengan software, perbaikan gambar, pencetakan dan penyusunan gambar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2019-06-15 14:49:56", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "03e7dbdb-3600-4722-a0ce-0349605121bb", "id_kompetensi": "<PERSON><PERSON><PERSON><PERSON> dan perkembangan Theory of Flight ", "kompetensi_id": 3, "mata_pelajaran_id": 800000111, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami gaya-gaya yang bekerja pada pesawat udara, p<PERSON><PERSON><PERSON>, huku<PERSON>, hukum kek<PERSON> energi. Elemen ini berhubungan dengan pengetahuan yang dibutuhkan dalam menjelaskan prinsip dan perkembangan Theory of Flight.  ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:19", "updated_at": "2022-11-10 19:56:56", "deleted_at": null, "last_sync": "2022-11-10 19:56:56"}, {"kompetensi_dasar_id": "03eaa518-b318-4fef-8f8f-8e2e46f841a5", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 843061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikan ragam motif tabuh dasar instrument karawitan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:07", "updated_at": "2019-11-27 00:29:07", "deleted_at": null, "last_sync": "2019-11-27 00:29:07"}, {"kompetensi_dasar_id": "03eba12d-11f0-46de-a019-bf2bca594410", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  teknik pemesinan  frais CNC", "kompetensi_dasar_alias": "Dapat menerapkan  teknik pemesinan  frais CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:04", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:57:04"}, {"kompetensi_dasar_id": "03ec3169-62dc-4744-bdaa-8360950e74d2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804130500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teorema <PERSON> pada pen<PERSON><PERSON>an gerbang logika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "03eccd6c-bd14-4292-85cc-75e893564176", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 828190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menerapkan pemanduan wisata overland tour", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "03edf539-4ee1-4b42-89fb-46373c9e2b90", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perubahan demokrasi Indonesia dari tahun 1950 sampai dengan era Reformasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:47", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:47"}, {"kompetensi_dasar_id": "03eeecf2-1f83-4b2d-aeb4-860208b7381e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:17"}, {"kompetensi_dasar_id": "03ef7b1d-484d-4c00-aa52-90ad1794f5ac", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:02", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:02"}, {"kompetensi_dasar_id": "03ef9da1-7a1c-4d9b-8db0-9f030870371c", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 807020300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan replace metal hydraulic pipe", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "03f01d04-b323-4375-a773-224503ac62bd", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja <PERSON> rang<PERSON>an elektronika", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "03f0fc48-3b03-4dff-b383-1e4b7504eddc", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis fungsi sosial, struktur teks, dan unsur kebahasaan teks interaksi transaksional lisan dan tulis yang melibatkan tindakan memberi dan meminta informasi terkait hubungan sebab akibat, sesuai dengan konteks penggunaannya. (Perhatikan unsur kebahasaan because of ..., due to ..., thanks to ...)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:09", "updated_at": "2019-11-27 00:30:09", "deleted_at": null, "last_sync": "2019-11-27 00:30:09"}, {"kompetensi_dasar_id": "03f1a3ab-c2dd-4e98-b5cc-2bd1931ee076", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 401251300, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis riset pasar dan informasi pemasaran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:46", "updated_at": "2019-11-27 00:29:46", "deleted_at": null, "last_sync": "2019-11-27 00:29:46"}, {"kompetensi_dasar_id": "03f37e4b-ee19-4a97-a2f1-b70de120b21f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 802020600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil perawatan komputer terapan jaringan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "03f407db-807c-4e22-8103-7d49c2a5df36", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa proses pembuatan rangkaian filter frekuensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:46", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:46"}, {"kompetensi_dasar_id": "03f4fb65-f308-460c-9bad-b24697e5f31c", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 821090330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa perakitan komponen konstruksi sekat kapal <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "03f58a72-274f-4f83-ae64-815280a93647", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804101300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan fungsi komponen pada pembangkit tenaga listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2019-11-27 00:28:42", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "03f6ecc7-13c3-47c9-9632-8e6e85bf2198", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 841010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan evaluasi produk perhiasan permata teknik ikatan claw", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "03f8526b-cf96-4b42-88ae-09d1db065ea9", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 807020610, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi komponen-komponen utama Aircraft hydraulic & pneumatic systems", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "03f8e360-31f5-465e-a747-515b84e6bd6c", "id_kompetensi": "Proses bisnis bidang teknik logistik atau teknik industri secara menyeluruh pada berbagai industri. ", "kompetensi_id": 3, "mata_pelajaran_id": 800000109, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, peserta didik mampu memahami proses bisnis bidang logistik atau teknik industri secara menyeluruh pada berbagai industri, mulai dari perbaikan lingkungan kerja, keg<PERSON><PERSON> administratif, pela<PERSON><PERSON> pela<PERSON>, pen<PERSON><PERSON><PERSON> per<PERSON>, dan teknik distribusi. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:19", "updated_at": "2022-11-10 19:56:55", "deleted_at": null, "last_sync": "2022-11-10 19:56:55"}, {"kompetensi_dasar_id": "03f9d27f-39e2-438e-aa50-adeaf127ab90", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827210600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan filleting berbagai tipe dan bentuk potongan daging ikan (Loin, <PERSON>ku, steak dll)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:51", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:51"}, {"kompetensi_dasar_id": "03fa0c47-55b2-477c-a8cf-2e926279858c", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401251030, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode penyusutan asset tetap dan pencatatannya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "03fa189e-e41d-48dd-911a-d44f406d696f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca buku manual mesin perkakas konvensional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "03fa7677-ce5b-485b-8318-9f3db0eaa67f", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat turunan fungsi aljabar dari aturan dan sifat limit fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:54"}, {"kompetensi_dasar_id": "03fa8794-74ee-4d79-b673-69a2c7e3da63", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perbaikan unit tata udara komersial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "03fb16c1-f12f-4b36-9709-f86ff48ae7a9", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 803070800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan program  aplikasi sederhana sistem minimum mikrokontroler", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "03fcbefd-a665-4997-a9ed-b858b5c00bb2", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:40", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:08:40"}, {"kompetensi_dasar_id": "03fd11dc-ae6a-4273-ad80-ef80763d2c98", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa kondisi operasi tripping batere", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "03fdfd63-8018-488c-94cf-170ab4962af3", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem Engine Management System (EMS)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:29", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:04:29"}, {"kompetensi_dasar_id": "03fefc63-a4b0-4321-a72e-ae574f869a30", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat gambar potongan sesuai tanda pemotongan dan aturan tata letak hasil gambar potongan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:39", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:39"}, {"kompetensi_dasar_id": "03fff7de-a1dd-42b1-96ee-68cd49b4dd12", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827290500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengendalian instruksi kerja pengolahan rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "04005ae5-c2e8-4e65-ade5-6e2a9be29c56", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:47", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:47"}, {"kompetensi_dasar_id": "04007bdc-ae4c-4ac6-b821-a0b049db0a7d", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 843062100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang garap irama dan tempo sajian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "0401f63a-fa6a-43df-94f5-3c2b9cd01dc8", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:17"}, {"kompetensi_dasar_id": "040366e4-3642-46e3-8cf1-2b42a57819b8", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 820080200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kinerja sistem penerangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "04042476-4417-421a-8f75-9fedb9787202", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam special service tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:34", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:34"}, {"kompetensi_dasar_id": "04059094-3aa4-495d-ad76-1cd1cfb19035", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membantu mobilisasi pada lanjut usia partial", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "040686ab-fdff-44c7-ab66-236b02aab9a3", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 839120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis proses produksi usaha kerajinan fungsi pakai dari berbagai bahan limbah di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:27", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "04068d42-b3d5-4523-b78c-e59c1b75f1d3", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON>is rangkaian  tunner pada sistim radio penerima.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "0407fe77-cebf-44fd-a0d3-4623e3d89e42", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 829050500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  wadah hidangan dari sayuran dan buah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:20", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:20"}, {"kompetensi_dasar_id": "0409e5ce-3cf8-45d1-8cdb-7e3a5f00accf", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan material dan bahan kimia sesuai SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:24", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:24"}, {"kompetensi_dasar_id": "040a9bac-bc0a-42ad-baa9-fa45f07550f5", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843080500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> antawacana pada adegan pedalangan dalam cerita Ma<PERSON><PERSON>ta at<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "040b14e4-a917-4f56-b42f-60e86334d48c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825020200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penanaman bibit tanaman sayuran.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:04", "updated_at": "2022-10-19 23:19:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:04"}, {"kompetensi_dasar_id": "040bf21c-14e2-4217-96b3-d71705833793", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804130400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengintrepetasikan sketsa gambar dan memberi label bagian-bagian dari <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "040c8c38-dc53-4ab8-9ba4-9b829ba6167c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821190100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskrisikan karakteristik komponen dan sirkit instalasi tegangan listrik yang digunakan untuk penerangan piranti elektronik.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "040e6bc2-c614-48d0-a378-2e4a88542ebb", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 828180110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pem<PERSON><PERSON><PERSON> harga wisata one day tour", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "040ea825-4451-4a65-a38a-885212790699", "id_kompetensi": "4.49", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai jenis katub pengarah hidrolik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "040eabe9-7de1-42d9-bc89-5740abec465d", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 818010300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan uji sifat fisik dan mekanik batuan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "04101b4a-4d44-4a6e-b037-7ec8acba26b8", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825020520, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan persiapan lahan tanaman perkebunan tahunan penghasil penyegar/penyedap", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "0410c4a0-4448-41bf-88f5-2d24b0549733", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827311100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply Volatile Organic Compound (VOC) Management Plan, Garbage Management System, Anti-fouling systems, Ballast Water Management and their discharge criteria", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2019-11-27 00:29:11", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "0412d858-0d58-40b8-b297-f67d68d7131a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan parameter pemotongan mesin gerinda silinder", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:54", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:54"}, {"kompetensi_dasar_id": "0412f461-698b-49f1-9c80-f487ade327ec", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna dalam teks ilmiah faktual (factual report) lisan dan tulis tentang benda, binatang dan gejala/perist<PERSON><PERSON> alam, terkait dengan <PERSON> pelajaran lain di Kelas XII.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:53", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "0412f9a2-50bd-45c2-b142-75735057e922", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 825021200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Melaksanakan Teknik pengajiran", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "0413444c-d6bb-4ab5-ac11-b2372ec76b2e", "id_kompetensi": "Teknik dasar teknik energi terbarukan ", "kompetensi_id": 3, "mata_pelajaran_id": 800000117, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase E, peserta didik mampu memahami teknik dasar teknik energi terbarukan melalui pengenalan dan praktik dasar yang terkait dengan seluruh proses produksi dan teknologi yang diaplikasikan dalam energi terbarukan, termasuk pengenalan teknologi yang diaplikasikan dalam pembangkit listrik tenaga air, tenaga bayu, tenaga surya, biomasa. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:20", "updated_at": "2022-11-10 19:56:57", "deleted_at": null, "last_sync": "2022-11-10 19:56:57"}, {"kompetensi_dasar_id": "041379a2-246f-45c1-bf45-93fc699e6da2", "id_kompetensi": "3.70", "kompetensi_id": 1, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan program pengendalian system otomasi industry dengan mikrokontroller", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "04137b79-224e-444a-b854-4700ccc9760f", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 401130900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan pulp dan kertas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:09", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:09"}, {"kompetensi_dasar_id": "0413c983-01c2-4db4-9837-fd443a9c65ac", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401251102, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pencatatan transaksi pembelian bahan-bahan perleng<PERSON>pan (supplies), barang dagangan, aset tetap dan transaksi pembayaran utang pada perusahaan dagang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:59", "updated_at": "2019-11-27 00:29:59", "deleted_at": null, "last_sync": "2019-11-27 00:29:59"}, {"kompetensi_dasar_id": "04142bf7-f689-451f-b1c8-b03b50085154", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan menu draw program CAD 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "0416110f-becf-42b1-a749-688bba5d5e57", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804080500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengolahan pengetahuan bahan untuk pekerjaan saiter", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "04188129-6942-40f2-9cdd-1837b93f11f9", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemanenan ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "04189463-aa0b-4f85-80bc-9d5809696976", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:34", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 16:05:34"}, {"kompetensi_dasar_id": "041a0ccd-5335-41a6-8790-d0c1c8852b5a", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 804100700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> bagian-bagian lighting arrester", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "041a38df-d98a-49e7-87d5-8e8d31e617ff", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827320110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply of displacement", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "041ab823-ac02-4df7-af94-0ca29509f5e5", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan peran dan tugas seorang editor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "041b9c12-3278-4edd-8055-1521debe587f", "id_kompetensi": "3.21", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil analisis khromatografi lapis tipis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "041bb548-01da-460f-80f4-1452deaa4ba9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825120100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik perawatan dan perbaikan alat mesin perawatan dan perbaikan alat mesin  pertanian (las karbit, Las TIG/MIG, bubut, frais)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "041c00e4-9104-4805-97c9-40f56cf2727d", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 825060200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pengawetan hijauan pakan ternak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "041d4b8c-8db9-4438-8541-69d2bc8faeaf", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 842010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil produk ukir pukul", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:03", "updated_at": "2019-11-27 00:29:03", "deleted_at": null, "last_sync": "2019-11-27 00:29:03"}, {"kompetensi_dasar_id": "041da374-74e1-4877-a4e7-735b9408cd34", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan makna  nilai-nilai demokrasi pada konteks lokal dan global dengan mengacu pada teks Alkitab.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:19", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:19"}, {"kompetensi_dasar_id": "041e2589-6407-4b2a-95bf-1c9e67aa2bbf", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 826060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dasar komposisi tari tunggal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2019-11-27 00:28:17", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "041e3891-3fb0-454a-b580-93968d67ea91", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 806010300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem tata udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "0420ac15-35e1-4cc3-ad90-2531ad0c003e", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 820070500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pengapian konvensional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2019-06-15 15:03:17", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "0420c050-9d3b-4e5f-87e3-9fb576cb92ac", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 801031300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemeliharaan kebersihan bagi penyandang disabilitas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "042173ad-9df2-4e32-b1fd-f4ba441429eb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:03", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:30:03"}, {"kompetensi_dasar_id": "0424cc8e-ead4-4f62-8bf0-bc1abadfbf97", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:51", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:51"}, {"kompetensi_dasar_id": "0425f666-130c-46f3-af6c-5550a8cba889", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 817120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembuatan maket jalan dan jembatan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "042610b9-f9d6-4ac8-90a3-4859595aaee9", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 843120400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mereplikasi plot tata cahaya pementasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "0426c43e-6070-4e59-bd95-45e3a3844242", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 802021300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami troubleshooting layanan ntp server", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "0426c77a-2484-4d59-871f-a89376e8545f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 802010700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan desain aplikasi berbasis mobile", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "042868f6-fd65-408e-9aa3-efa169cb10a3", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "3.4\tMenerapkan instruksi kerja pengoperasian peralatan secara mandiri\r\n\r\n", "kompetensi_dasar_alias": "", "user_id": "f593f0fd-51a9-4e0d-b08c-877b5fb2bf95", "aktif": 1, "kurikulum": 2013, "created_at": "2019-06-15 15:31:58", "updated_at": "2019-06-15 15:31:58", "deleted_at": null, "last_sync": "2019-06-15 15:31:58"}, {"kompetensi_dasar_id": "0428e9a9-fa4b-400e-aa2c-a6a0af4980f1", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 817110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memecahkan gangguan operasi pada proses pengolahan migas dan petro kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "04294c4a-e8e0-484c-9d8b-d88a8054d2b0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 840050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyiapkan tungku dan perlengkapannya untuk tahapan pembakaran glasir", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:28", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "0429876d-968b-4f80-8ac8-fb0a1eb5b689", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 839080100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemont<PERSON><PERSON><PERSON> teknik, proses, alat dan bahan jahit tindas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "042d06a8-c8b1-48ca-ad2f-0ddb21ce8cf0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:54"}, {"kompetensi_dasar_id": "042de362-e9a2-4bcd-8de3-92ebf6eb1abb", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 800061400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis penyakit DM berkaitan tindakan medis gigi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:28"}, {"kompetensi_dasar_id": "042e3e2a-14f9-429f-bf27-9e7f33f53ec7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804100400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemeliharaan sistem AC/DC power.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "042ed846-f208-40bc-829f-4f740d58dd84", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 819040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan prinsip dasar  penggunaan kontrol tekanan dengan sistem mekanik dan elek-trik serta hubungannya dengan interface dalam sistim komputasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:31", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:05:31"}, {"kompetensi_dasar_id": "042f6801-2d90-41a8-ac8e-70a8d5f483cc", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 800061500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komunikasi dengan pasien anak dan pasien berkebutuhan khusus setelah tindakan medis di klinik gigi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:31", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:27"}, {"kompetensi_dasar_id": "0430e3be-1899-484f-acc2-7e64876d3d5a", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401251060, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pelaporan dan penyetoran PPh pasal 23", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:15", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "043308c8-982d-44ed-a24c-1464c30632df", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakuka<PERSON> pengh<PERSON>an hasil produksi ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:23", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:23"}, {"kompetensi_dasar_id": "04339c08-0d62-44cf-bb44-8fc0e9a56a67", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:16"}, {"kompetensi_dasar_id": "0433bf02-ce98-4cd8-9ad5-7b4238f07dfe", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803050100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar peralatan elektronika.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "0433f7ce-65b1-4cf7-baa8-c0177d5273c1", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji kondisi <PERSON>i sensor liquid level", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:33", "updated_at": "2019-11-27 00:28:33", "deleted_at": null, "last_sync": "2019-11-27 00:28:33"}, {"kompetensi_dasar_id": "0434d6fb-cf4e-44c0-b458-71e9b7be22b0", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825210800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan  rekayasa pengelolaan  induk komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:48", "updated_at": "2019-06-15 14:52:48", "deleted_at": null, "last_sync": "2019-06-15 14:52:48"}, {"kompetensi_dasar_id": "043635f6-3f38-49ef-b774-d6440d46c7b8", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hikmah dan manfaat saling menasihati dan berbuat baik (ihsan) dalam kehidupan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:41", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:41"}, {"kompetensi_dasar_id": "04364b7b-0fc9-4781-9153-61ca922bada5", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 827190110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan inovasi dan persiapan wadah pada pendederan komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "0436d159-08dc-4f07-b448-f297840fba6e", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> desain/prototype dan kemasan produk barang/jasa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "043737f9-859a-4450-83b2-6f80feccde41", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kontribusi bangsa Indonesia dalam perdamaian dunia diantaranya : ASEAN, Non Blok, dan <PERSON><PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:42", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 16:07:42"}, {"kompetensi_dasar_id": "04374898-3b56-4af5-8996-4d43778640f0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan sistem analog dan sistem digital pada Sistem Kendali Industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2019-11-27 00:28:04", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "0437c572-6074-421e-915d-e1d778606695", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 808050500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pek<PERSON><PERSON><PERSON> trouble shooting techniques", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "0437d662-db56-449d-9c5b-38294880886f", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 817110100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> re<PERSON>i kimia pada proses konversi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "04395442-d55b-460f-a2e2-aaa0afb93982", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 808060620, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan peralatan dalam proses pembuatan benda kerja dari bahan material komposit sesuai dengan spesifikasi pekerjaan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "0439c335-1804-4031-bf37-c600854e8fa1", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 800030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan monitoring barang kembalian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2019-11-27 00:30:23", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "043a0c95-79cd-4521-86ac-f925e65aa71e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827050110, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Understand considers instructions on board ship", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "043a52d3-2f03-4b18-bcb8-33849489c09e", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804100810, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Instalasi Penerangan Jalan Umumsesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:49", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:21"}, {"kompetensi_dasar_id": "043b695f-94ad-4811-bc23-2475f94b3595", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyelesaikan masalah sistem persamaan linier dua variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:36", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:36"}, {"kompetensi_dasar_id": "043be08f-162a-4e4e-8174-d0430505c5d2", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803081400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan macam-macam sistem sandi  digital pada instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "043d8e77-92b7-4665-b428-7fbd5f52d457", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 803071300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguji penguat operasional pada rangkaian elektronika aritmatik dan kegunaan khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "043d962b-493f-4c00-a47e-8975027bf445", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 830120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Melakukan rias wajah sikatri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:34", "updated_at": "2019-11-27 00:30:34", "deleted_at": null, "last_sync": "2019-11-27 00:30:34"}, {"kompetensi_dasar_id": "043db681-c121-4dda-8659-ffca082f4445", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 807021720, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoreksi rangkaian listrik komplek dan sistemnya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "043dcf11-c2a5-4f3a-b62a-341dc4e39017", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian jika terjadi suatu keadaan/ kejadian/peristiwa di waktu yang akan datang, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:36", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:11:08"}, {"kompetensi_dasar_id": "043de93c-c023-44a0-88d2-d3777da00293", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun program peningkatan serta mengevaluasiderajat kebugaran jasmani terkaitkesehatan dan keterampilan secara pribadi berdasarkan instrument yang dipakai.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:22", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:22"}, {"kompetensi_dasar_id": "0441e343-43be-4cfa-bc61-88a36dbee333", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:39"}, {"kompetensi_dasar_id": "04422eb8-c9d5-4e66-a543-879829718d10", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 801031100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON> as<PERSON>men anak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:26", "updated_at": "2019-11-27 00:30:26", "deleted_at": null, "last_sync": "2019-11-27 00:30:26"}, {"kompetensi_dasar_id": "0443babb-0c40-481e-9245-f11b6be1a524", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan sistem sambungan pipa dan komponen pipa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "04446727-e976-4b47-818b-841b1e8f5208", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 401141200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sediaan ekstrak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:22", "updated_at": "2019-11-27 00:30:22", "deleted_at": null, "last_sync": "2019-11-27 00:30:22"}, {"kompetensi_dasar_id": "0444d026-b892-4ef8-ac4b-41ac7beafeb2", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 843060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis ragam gerak tari tradisi putri berdasarkan pengembangan tema", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "04451f81-deca-40e9-bb46-9c76865e6c1e", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:26", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:26"}, {"kompetensi_dasar_id": "04452bb2-8f35-4d39-bfae-b9770fbeb3bb", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi perkembangan kehidupan politik dan ekonomi bangsa Indonesia pada masa Demokrasi Terpimpin.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:19"}, {"kompetensi_dasar_id": "0445f08f-9e8d-4a86-84d5-0d223ffa3c68", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks penyerta gambar (caption), dengan me<PERSON><PERSON>ikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "0446d202-2263-4d10-821f-7262773f1909", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 826060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji<PERSON> pemadatan/rasional<PERSON>si ragam gerak tari", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "0446d354-e901-4a20-aeab-64ef4a3d3d01", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 804100600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasi-kan saluran udara tegangan menengah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "0446e11b-dd37-4189-9cbf-08167479bcc5", "id_kompetensi": "4.51", "kompetensi_id": 2, "mata_pelajaran_id": 821200200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang sequence operasional system control hidrolik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:54", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "0447529c-4adf-4353-a27b-1489a918ae98", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menentukan kebutuhan tenaga kerja usaha benih", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "04479df9-3b7b-40f7-b43b-e65e73bce75e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:24"}, {"kompetensi_dasar_id": "04486266-2fbd-46b9-8635-7372f9d23991", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem pengapian elektronik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:00", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:03:00"}, {"kompetensi_dasar_id": "044942d0-73a1-4e78-951b-a5c5db73f2fe", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 843120400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> prosedur penataan cahaya practical", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "044c8343-7c00-4dca-982b-d6eac66ec68a", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa system dan komponen programmable logic control (PLC).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:42", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:01:42"}, {"kompetensi_dasar_id": "044ce66b-ec9c-4222-a74f-b14a119c2b75", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami system Air Conditioning (AC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "044d3fd5-5b53-457a-94f5-77441f23f184", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 829111100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Men<PERSON><PERSON><PERSON> roti tawar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "044f1102-e452-4ddf-b755-321813cb2cd6", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 809020900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perawatan dan pemeliharaan mesin cetak cetak ofset", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "04500f22-bfb5-4c7b-95ea-00a1abda97f2", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 825020400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penanaman benih dan bibit tanaman hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:41", "updated_at": "2019-11-27 00:27:41", "deleted_at": null, "last_sync": "2019-11-27 00:27:41"}, {"kompetensi_dasar_id": "0452f284-3d01-4269-bb8a-8f80b0008714", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804050900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengecoran beton/beton bertulang berdasarkan metode dan prosedur yang disyaratkan dalam spesifikasi termasuk persyaratan standar mutu yang harus dipenuhi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "04539a8a-8ae8-4869-9402-e11dd7e88acc", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:38", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:38"}, {"kompetensi_dasar_id": "0453d886-d6b4-4bde-8daf-605468949089", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis gambar 3D kompleks", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:46", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:46"}, {"kompetensi_dasar_id": "04546da3-b278-46ea-8dde-0264123e7d4d", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804050460, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan pengelolaan bisnis konstruksi dan properti", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:15", "updated_at": "2019-11-27 00:30:15", "deleted_at": null, "last_sync": "2019-11-27 00:30:15"}, {"kompetensi_dasar_id": "0457ff1b-4d0c-4cc2-8c97-c11ae6f2a318", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan teknik pembiakan dalam produksi benih secara vegetatif", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:45", "updated_at": "2019-11-27 00:27:45", "deleted_at": null, "last_sync": "2019-11-27 00:27:45"}, {"kompetensi_dasar_id": "0458026f-4c60-4f60-88a0-4d852ffb1132", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa awal Reformasi dan menyajikannya dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:22", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:22"}, {"kompetensi_dasar_id": "04583a69-c4f8-4f2c-b1b1-f7bd63bcd81a", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep komposisi fungsi dengan menggunakan konteks sehari-hari dan menera<PERSON>kan<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:16", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:16"}, {"kompetensi_dasar_id": "0458b26d-6f60-4298-908f-09aa6f7be639", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:54"}, {"kompetensi_dasar_id": "0459f02e-fa1b-45a6-acf3-acb5d4e27ca4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkatagorikan spesifikasi teknis  saluran irigasi berdasarkan fungsinya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "045a2221-13fa-4e9b-b3e6-c5e636e87c48", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 820120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengamati cara kerja under carriage system.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "045a3d25-4b93-412b-8154-f832ebbb1610", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan Steering System", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:20", "updated_at": "2019-11-27 00:27:20", "deleted_at": null, "last_sync": "2019-11-27 00:27:20"}, {"kompetensi_dasar_id": "045ae0af-898a-4ea1-997d-4ae84a5ac75a", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 808060610, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapakan cara membuat cetakan(molding) komponen pesawat udara dari komposit sesuai dengan tuntutan peker<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "045b983d-bb88-4531-b519-0d0cb3f225b3", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 829060600, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengevaluasi menu seimbang untuk bayi dan balita", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-11-27 00:30:31", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "045ba6b9-2899-489a-969c-796cc5198050", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 802032700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan penggambaran dengan perangkat lunak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "045baf37-1530-46ae-8c31-eb8269f6cc2d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 802020510, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerja proyek bersama tim untuk menentukan topik kerja proyek yang aktual dan selaras dengan sarana-prasarana sekolah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "045bb7e0-dde9-4b8b-bf20-bf8a5d6c803f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menjelaskan fungsi komponen pada sistem pemasukan dan pengeluaran gas buang", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:44", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:32:44"}, {"kompetensi_dasar_id": "045cbab8-7a40-47ce-a850-b24854d49e89", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem sentral lock, alarm dan power window", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:52", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:52"}, {"kompetensi_dasar_id": "045cc080-52eb-4b9a-a5e3-e1c69796b711", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 401141200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pembuatan sediaan tinctura", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2019-11-27 00:30:21", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "045d1ebb-7c16-4102-b920-fbc63d2696e8", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur perawatan dan perbaikan jaringan air kotor.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:28", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:28"}, {"kompetensi_dasar_id": "045d8f1d-3b8c-403b-9dc6-3fca1f20c99d", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 823030200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan K3 dan prosedur pengoperasian pembangkit listrik tenaga biogas POME.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2019-06-15 14:51:16", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "045db1fd-fb5d-40ea-8bf8-c75027c78b07", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang sistem perawatan dan perbaikan jaringan HVAC (Heating Ventilation Air Conditioning)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:32:17", "updated_at": "2019-06-15 15:32:17", "deleted_at": null, "last_sync": "2019-06-15 15:32:17"}, {"kompetensi_dasar_id": "045e021f-07dc-481f-b909-588f4d4de4e5", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804101000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan program kontrol untuk programmable logic control (PLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:42", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:01:42"}, {"kompetensi_dasar_id": "045f7444-2f16-45e8-a7a0-e82a49b697d9", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 803081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis hasil pengukuran massa jenis melalui metode dua tengki dengan menggunakan transmitter", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "0460877d-ba63-46ab-8d4a-48d5db4dbf25", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:49", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:49"}, {"kompetensi_dasar_id": "0461006e-e15a-4fe6-b861-ed4a9ba57d0b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami psikoakustik anatomi telinga manusia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2019-06-15 15:06:55", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "046188bd-5fbe-4694-8676-43e652bcd4ff", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804011200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> garis-garis gambar teknik sesuai bentuk dan fungsi garis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "0461dfcd-f132-4c6e-a4a8-a5c150177a52", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep komposisi fungsi dengan menggunakan konteks sehari-hari dan menera<PERSON>kan<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:17", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:33:17"}, {"kompetensi_dasar_id": "04627506-65f4-4566-8477-5ba1a145832e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804100700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa bagian-bagian trafo tenaga", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2019-11-27 00:28:15", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "0462ad05-3afd-4cb6-9e3f-b6e0082eea7a", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804100500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan teknik pemasangan trafo, grounding, tower, kawat penghantar SUTT/SUTET", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "0462b8b8-9be5-4365-bc8a-45b289622979", "id_kompetensi": "4.32", "kompetensi_id": 2, "mata_pelajaran_id": 824060500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> proses rehearsal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "0462ffb5-556c-42e6-8d05-1f54dea203f1", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 827390700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain various types sealants and packing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "0464b02b-d7b0-46d2-a449-eedb1a2fadd4", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:28"}, {"kompetensi_dasar_id": "0465634a-3af9-492b-a92a-304b150aa9c9", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan output penggambaran CAD 2D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:33:58"}, {"kompetensi_dasar_id": "04656a44-09fa-458f-94cd-bbb8c2321117", "id_kompetensi": "3.31", "kompetensi_id": 1, "mata_pelajaran_id": 820030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sistem peralatan penunjang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "0465d86b-4bcb-4b67-970d-5bda40961578", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:29"}, {"kompetensi_dasar_id": "0466493d-64ff-420d-801d-91dfbe34e082", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 827290500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan administrasi kegiatan pengolahan rumput laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:32", "updated_at": "2019-11-27 00:29:32", "deleted_at": null, "last_sync": "2019-11-27 00:29:32"}, {"kompetensi_dasar_id": "0467f4c1-d6ee-496c-933e-fbc6f1f5e9ea", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 821080400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyaji<PERSON> hasil rakitan konstruksi kapal kayu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "04690cfe-2da2-4de4-8f66-d142de7ac484", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401140200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan uji kepekaan bakteri terhadap berbagai jenis antibiotik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "04693fe3-d244-443a-b251-6b5f1b8713fe", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 809010500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perkiraan biaya pokok cetak buku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:35", "updated_at": "2019-11-27 00:27:35", "deleted_at": null, "last_sync": "2019-11-27 00:27:35"}, {"kompetensi_dasar_id": "046b0463-2564-4a81-b4a6-cc8391bb9c81", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 821070100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi hasil teknologi pembangunan kapal baru", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "046b7779-d891-4346-b6d9-4b3a201b8577", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 818010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pen<PERSON> dan klas<PERSON><PERSON><PERSON> penambangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "046c03a3-d0d1-4617-911d-4493a2e7b835", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 820120200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanankan perbaikan cat pada perbaikan kecil/touchup)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:21", "updated_at": "2019-11-27 00:27:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:21"}, {"kompetensi_dasar_id": "046d9b56-a6a5-4f49-9fb8-696ecc8f06c9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pengetahuan tentang penanganan limbah dalam agribisnis  ternak ruminansia perah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:06", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:07:06"}, {"kompetensi_dasar_id": "046da254-a34b-4a49-b068-abbd8f9c2097", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:58:03", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:03"}, {"kompetensi_dasar_id": "046db750-e85f-481d-9cab-8a4fc8028261", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 816010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses pen<PERSON>ian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "046e8241-8d07-45d3-ab3b-3da9451460c5", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> strategi dakwah dan perkembangan Islam di Indonesia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:55"}, {"kompetensi_dasar_id": "046f21cf-2084-4610-a56c-fa0c6e7c8abe", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401140400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis sel eritrosit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "04700b6e-4fd2-4043-9740-83041a96b20f", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan   teknik  pembuatan benda kerja   pada mesin frais, dengan su<PERSON>n/toleransi khusus.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:00"}, {"kompetensi_dasar_id": "04709661-c806-47f2-9491-4ae64b31ab2c", "id_kompetensi": "3.36", "kompetensi_id": 1, "mata_pelajaran_id": 825100300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan mesin pemotong/pemangkas disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:51", "updated_at": "2019-11-27 00:28:51", "deleted_at": null, "last_sync": "2019-11-27 00:28:51"}, {"kompetensi_dasar_id": "0470f8db-5e54-4fe9-a415-40636f0a0b32", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 828190110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara mempresentasikan Informasi Ekowisata pelestarian alam <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:52", "updated_at": "2019-06-15 14:52:52", "deleted_at": null, "last_sync": "2019-06-15 14:52:52"}, {"kompetensi_dasar_id": "04716561-e249-4d97-ae71-38714dc82e24", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "047190d7-4522-4e36-b252-3ca14ca2f3ec", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 832020100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan teknik gambar sketsa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:30", "updated_at": "2019-11-27 00:28:30", "deleted_at": null, "last_sync": "2019-11-27 00:28:30"}, {"kompetensi_dasar_id": "0472b290-1002-4ce4-aaff-51d86c9fe40d", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sistem gasoline direct injection (GDI)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:08", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:31:08"}, {"kompetensi_dasar_id": "0472bd99-c278-43f4-9126-d052d788939f", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 808020200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  benda geometri gabungan kerucut beralas lingkaran dengan selinder sejajar dengan garis sumbu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "04740946-643f-4e44-a3f9-16996534b7c9", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 825021100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis kerusakan tanaman perkebunan akibat serangan penyakit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "0474759e-af5e-468c-9c40-81d481e588a6", "id_kompetensi": "4.43", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan ketentuan pembagian waris Islam dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:09:12", "updated_at": "2019-06-15 15:13:15", "deleted_at": null, "last_sync": "2019-06-15 15:13:15"}, {"kompetensi_dasar_id": "04780e29-2332-4691-a18e-dade35933d7b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:11", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:04:11"}, {"kompetensi_dasar_id": "04793c02-74ac-4b59-bc82-72f08e5dd9c9", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar konsep dan gaya eksterior  disesuaikan dengan situasi dan kondisi lingkungan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "04795291-1cd4-4372-ac96-ac054d739a16", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 820050500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaik  Spooring", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "04796a3c-9e7b-4f9a-a191-13273b6057fe", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik kerja titrasi pengendapan (argentometri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:26", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:01:26"}, {"kompetensi_dasar_id": "047a3090-c810-4755-bd90-696a781f5a3b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> dasar-dasar manajemen bidang konstruksi pada tugas membuat gambar kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:32:53"}, {"kompetensi_dasar_id": "047a3640-6d28-4df3-b425-4e0ff6f3716d", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat integral tak tentu dari aturan dan sifat turunan fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:07:02", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:07:02"}, {"kompetensi_dasar_id": "047a833e-abd7-47a1-bfff-2adc8a8b0825", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821060101, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan standarisasi di bidang perkapalan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "047b49ae-89c2-424b-92e4-9a4ec70b2a4a", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 804050460, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki pengajuan termin pembayaran peker<PERSON><PERSON> kons<PERSON>si", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:15", "updated_at": "2019-11-27 00:30:15", "deleted_at": null, "last_sync": "2019-11-27 00:30:15"}, {"kompetensi_dasar_id": "047b962a-3329-4cb1-928d-e5d5e0aa90e4", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:35", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:01:35"}, {"kompetensi_dasar_id": "047c60bb-b519-4281-92ec-25ba2d453279", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis rang<PERSON>an elektro-pneumatik pada sistem kontrol mekanik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "047cf02b-ae96-4c88-823c-1fab5e37441f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (K3LH)", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:10", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:10"}, {"kompetensi_dasar_id": "047cf454-ac26-491a-bd71-d52cb5809a85", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendiskripsikan cara menginstall program perangkat lunak visualisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "047ef763-b47f-4db4-8efc-c1ceea110fac", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis proses kerja\r\npembuatan prototype produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:17"}, {"kompetensi_dasar_id": "047f0801-a97b-4790-8269-2ab11edc7eb7", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 828150100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan k<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "047fd501-6736-4900-94ab-9e86466a5339", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 807010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan  cara pengukuran dengan alat ukur elektrik dan pembacaannya sesuai dengan  bentuk benda kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "04800540-2b27-4ea3-bf4e-d90b75870537", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prinsip K3LH mengikuti SOP", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:13"}, {"kompetensi_dasar_id": "048077c0-cab6-4297-aa5e-74e755fa9443", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827380200, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Apply dangerous, hazardous and harmful cargoes", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:14", "updated_at": "2019-11-27 00:29:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:14"}, {"kompetensi_dasar_id": "04826a4e-3301-4e42-9c74-eceb782d4992", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 827290400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membedakan kualitas rumput laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:50", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:50"}, {"kompetensi_dasar_id": "04838186-d9be-4bd9-83fc-4d076ac17fef", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 807022500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> proses pembubutan ulir spesifik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "0484609a-3b39-41e4-83bb-6d4162d2e6e4", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 834010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesian relief dengan teknik ketok", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "0485b3f2-397c-4a17-8f71-c82c7f4f72ed", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:57", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:57"}, {"kompetensi_dasar_id": "0485e3fd-1c71-45f3-9637-776be6691639", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401110220, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami  ekosistem dan semua interaksi yang berlangsung di dalamnya dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:55", "updated_at": "2019-06-15 14:49:55", "deleted_at": null, "last_sync": "2019-06-15 14:49:55"}, {"kompetensi_dasar_id": "04869645-811a-4fbe-bfbf-e156fad3279f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827110340, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> ilmu bahan permesinan kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "0488b9ad-c38a-4cb5-b779-1ae89a2a8a2f", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis faktor-faktor kema<PERSON>an dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:54", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:07:54"}, {"kompetensi_dasar_id": "048919a9-ff34-49aa-a543-0832a8bb73f6", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang integral tak tentu dari fungsi aljabar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:40", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:40"}, {"kompetensi_dasar_id": "04893441-0ee5-4936-810c-07ef8f6572e1", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 803080900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan koneksi jaringan komunikasi antar komponen perangkat keras", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "048992b7-59e4-4ce0-ac83-096f003bc058", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:37"}, {"kompetensi_dasar_id": "048ac103-c21e-43eb-86df-71a9be81b895", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 807022500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> proses pembu<PERSON>an alur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "048b554b-c6c5-4280-b080-2f07606a1713", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan upacara persembahyangan kepada para suci (Shenming)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:53:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:05:28"}, {"kompetensi_dasar_id": "048bad32-fffe-4a71-a45e-94efa0118c44", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 825250700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisisis pakan benih ikan hias alternatif", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "048bf77b-1e89-4dea-bb8d-3ef12ad4fccb", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804010900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis perangkat lunak untuk CAD", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "048c002c-f7e6-4f83-90ea-13921666cda7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menentukan peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:18"}, {"kompetensi_dasar_id": "048c3a9d-9b94-45ce-880a-972e24b6df8c", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 816040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses pencapan etsa", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "048eabc4-351a-46e8-8264-e562fea6924e", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 807022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> prinsi<PERSON> dasar electrical contactor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "048eb5c0-4e41-4fa9-a243-a005ff88cf76", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran tokoh Nasional dan Da<PERSON>h yang berjuang mempertahankan keutuhan negara dan bangsa Indonesia pada masa 1948 - 1965.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:55", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:28:55"}, {"kompetensi_dasar_id": "048fc8b3-d2dd-4ef5-95f7-74211c1c3fec", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:14", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:14"}, {"kompetensi_dasar_id": "04902690-408c-42d1-8d97-00f72c9ddacc", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:52", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:52"}, {"kompetensi_dasar_id": "049038f9-7488-4cc9-87b0-972925824716", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik penentuan vitamin", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:35", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:35"}, {"kompetensi_dasar_id": "049086aa-9f60-4401-88b8-5a88764f9033", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis sampel dengan kromatografi kertas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:45", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:00:45"}, {"kompetensi_dasar_id": "04924075-6bb0-4bdf-ba72-09e490bff493", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 827050110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Understand trainees willrole playthe various drills on board ships", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:58", "updated_at": "2019-11-27 00:28:58", "deleted_at": null, "last_sync": "2019-11-27 00:28:58"}, {"kompetensi_dasar_id": "04924830-6069-478c-b0c9-2cca9e3ba0cb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401130800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pengoperasian per<PERSON> filt<PERSON>i.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:29"}, {"kompetensi_dasar_id": "0492c43f-3f99-4608-b9d7-d6cd704ed564", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 824060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> dasar-dasar pen<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "0493a243-6b5c-4c69-b1e9-ed6ee5076e5a", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 401130910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil pengujian fisika bahan tekstil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "0495e1b7-35fa-416e-abe9-8b711ea090c3", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 804210200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan tahap eksplorasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "04961e0a-2b66-46b8-8c22-0c5d35ce11dd", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 100014140, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi cara menghindari Na<PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:01:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:01:18"}, {"kompetensi_dasar_id": "0496680c-01f2-4c48-93f5-3632e7d1d0bd", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat secara berkala pada sistem pemasukan dan pembuangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "04967fcd-645e-4abb-86ba-20a671981e80", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800050400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan mobilisasi pasif dan aktif (ROM aktif dan pasif)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "0496aeb1-c311-40ff-b531-ff38160f53d4", "id_kompetensi": "4.32", "kompetensi_id": 2, "mata_pelajaran_id": 825100500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Mengecek prinsip kerja alat mesin ekstruder", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "04976ffd-8d03-40de-b8a3-ef137feaca4c", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 803090300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan dan merawat measurement of fow and volume of blood", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "049870b3-b205-4e2b-854a-22a6a515540d", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan instalasi listrik dengan kond<PERSON>,  cable ladder dan cable tray/trunking.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:48", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:28:48"}, {"kompetensi_dasar_id": "049983ca-02cf-497f-8b53-dcc12b30d1bc", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 842010100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pembuatan jabung membuat landasan ukir (jabung)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2019-11-27 00:29:05", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "049a79ad-af6f-4a5c-b28e-a47048f11ff1", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 818010200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan konsep <PERSON>a bahan galian", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "049a8204-02af-4f27-8f34-030b755f03a0", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 811010300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat bagan perkembangan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2019-06-15 14:58:06", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "049c27cb-468b-495b-8846-7d4f1c65a49a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola  menyerang dan bertahan) salah satu permainan bola besar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:56", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:56"}, {"kompetensi_dasar_id": "049ca09e-3fa4-4981-ba29-f21969779cbe", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 829131300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat kue Indonesia dari tepung beras", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "049cabe0-4316-475a-b6a4-7b12be59f48f", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:39"}, {"kompetensi_dasar_id": "049d51c8-ab47-4a72-ac2a-3683cbd4f6d7", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil pengambilan sampel laboratorium", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "049e9973-3223-466c-9ca5-e7c22517a184", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 818010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> per<PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "049ec2db-fccb-421c-b1eb-7f707f612cda", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 826060600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dasar komposisi tari berpasangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:30", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 14:58:14"}, {"kompetensi_dasar_id": "049ecf51-52a7-45cb-902b-0598d43e0900", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat peta situasi konstruksi bangunan air sesuai spesifikasi teknis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "04a24125-8cdf-4b18-ae51-a308fd9ec350", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401140800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pembuatan media untuk pertumbuhan dan isolasi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "04a40ca0-18c5-4855-8b53-a33f6b496a26", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:13"}, {"kompetensi_dasar_id": "04a79115-b784-4557-9536-1f3bc0b0da63", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan  gambar instalasi Perlengkapan Hubung Bagi (PHB) Penerangan Bangunan Industri Kecil.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "04a7c33e-eb27-4951-ade7-f55fa0d4cb92", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 802031210, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> prosedur modeling hard surface secara sederhana", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:47", "updated_at": "2019-11-27 00:28:47", "deleted_at": null, "last_sync": "2019-11-27 00:28:47"}, {"kompetensi_dasar_id": "04a87cb9-ac07-4acf-a50a-70f9a6bc7316", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 809010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan kegiatan produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:00", "updated_at": "2019-11-27 00:29:00", "deleted_at": null, "last_sync": "2019-11-27 00:29:00"}, {"kompetensi_dasar_id": "04a8c24e-d6ea-4551-9526-08111db50c96", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 821080300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengoperasikpan mesin gerga<PERSON> be<PERSON> (radial arm saw)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:06:54"}, {"kompetensi_dasar_id": "04a904ce-39b4-419c-ba94-a1bb418bc29d", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Be<PERSON><PERSON> aktif dalam menjunjung kehidupan yang multikultur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:17"}, {"kompetensi_dasar_id": "04a9bff2-1d2b-486b-ae6c-8a78ef6a7ead", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 809010800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan proses pelapisan cetakan sesuai Standard Operating Procedure (SOP)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:03", "updated_at": "2019-06-15 14:50:03", "deleted_at": null, "last_sync": "2019-06-15 14:50:03"}, {"kompetensi_dasar_id": "04aa3d62-a5b2-4b24-88b4-b52bb78f6903", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 807020900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan maintenence & repair part pesawat yang terbuat dari bahan material composite", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "04aa7b96-c99a-4aaf-99fb-7539b91b136c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sikap dan perilaku yang menghargai HAM.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:34", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:34"}, {"kompetensi_dasar_id": "04aa8ae8-2c64-4a6a-b0a4-8649e0accd39", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 824050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rencana tindak lanjuti hasil evaluasi naskah program televisi non drama", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "04aada82-53bb-46ec-9518-5b603f909d9c", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 807021410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> langkah-langkah pengetesan/uji coba rangkaian", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:41", "updated_at": "2019-11-27 00:29:41", "deleted_at": null, "last_sync": "2019-11-27 00:29:41"}, {"kompetensi_dasar_id": "04ab315d-2f8e-49e3-8cb6-1bba4e7e9e3c", "id_kompetensi": "3.40", "kompetensi_id": 1, "mata_pelajaran_id": 825100500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prosedur kalibrasi alat mesin ekstraksir", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "04ad1068-c1b9-4e59-8161-b32cf5bd40f1", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 825090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengolahan tanah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:09", "updated_at": "2019-06-15 15:07:09", "deleted_at": null, "last_sync": "2019-06-15 15:07:09"}, {"kompetensi_dasar_id": "04ae05da-4a2d-4108-b154-3b186ac3e35e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:59", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:26:59"}, {"kompetensi_dasar_id": "04ae6697-5b27-4c93-a09a-02f3f6055e22", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 809010500, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perhitungan biaya penyusutan mesin.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "04ae7e02-1d3b-4e8e-889e-550e13cd95b2", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan prinsip induksi matematika dan menerapkannya dalam membuktikan rumus jumlah deret persegi dank<PERSON>k.", "kompetensi_dasar_alias": "Menganalisis aturan pen<PERSON> (aturan pen<PERSON>, aturan perkal<PERSON>, permutasi, dan kombinasi) melalui masalah kotekstual.", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:44", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:44"}, {"kompetensi_dasar_id": "04af7c0b-f462-4b51-9b4c-a8bb3dce7ccf", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825100400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis prosedur pengoperasian alat mesin pengendali hama dan penyakit (mist blower) disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:49", "updated_at": "2019-11-27 00:28:49", "deleted_at": null, "last_sync": "2019-11-27 00:28:49"}, {"kompetensi_dasar_id": "04b029a8-7e36-4c6a-960e-ab6055d8be17", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 803071400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menaganalisa proses kerja dan perbaikan rangkaian RF power amplifier", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "04b04d16-2cff-4219-bd9a-f076c86bdf6d", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 825100200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis prosedur mengemudikan traktor pertanian roda empat pada jalan datar/gelombang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:49", "updated_at": "2019-11-27 00:28:49", "deleted_at": null, "last_sync": "2019-11-27 00:28:49"}, {"kompetensi_dasar_id": "04b05d15-3c25-45b7-ac0f-a5e05cf6ae3c", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> tujuan hidup berda<PERSON>kan agama Buddha", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "04b0f3ec-a9e9-4dc0-ac2b-da37c4da4fd8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 802032300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Software untuk vector drawing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "04b14ff2-ae91-4012-9ed8-16a81b47ca77", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep peluang dan harapan suatu kejadian dan menggunakannya dalam pemecahan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:49"}, {"kompetensi_dasar_id": "04b1cde5-4547-487c-bb1f-9ef8c47be10a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan pekerjaan laboratorium sesuai SOP K3LH", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:21", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:21"}, {"kompetensi_dasar_id": "04b1dcdd-6214-4e6f-8bf1-af197ed96f1a", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis pembuatan media padat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:19", "updated_at": "2019-11-27 00:30:19", "deleted_at": null, "last_sync": "2019-11-27 00:30:19"}, {"kompetensi_dasar_id": "04b1ef6f-e5aa-4b32-8194-9ab6e7cb5259", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 828180110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan komponen biaya wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "04b2277f-37d3-40a4-83b0-645f0849cc01", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804131530, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan finishing produk teknik las listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:05", "updated_at": "2019-11-27 00:29:05", "deleted_at": null, "last_sync": "2019-11-27 00:29:05"}, {"kompetensi_dasar_id": "04b23827-2f60-4741-b2f4-bd81e8a58807", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843090100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penataan debog/batang pisang.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2019-06-15 14:58:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:20"}, {"kompetensi_dasar_id": "04b273a7-fc04-4a7c-88c3-71eb9e09a630", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 401251103, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi landasan hukum rahn", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2019-06-15 15:07:16", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "04b6a176-4d32-4f0e-acbe-e4fcf4342653", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843062300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstrasikan tata irama dalam sajian karawitan etnis nusantara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:52:33", "updated_at": "2019-06-15 14:58:19", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "04b6a5c2-78ab-475e-858e-fa1d5c9641d2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses pencatatan transaksi ke dalam jurnal umum untuk perusahaan jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:23"}, {"kompetensi_dasar_id": "04b6ea2e-b37c-4961-b7b1-edbd2295a73c", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 807022600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memonitor benda kerja hasil program G Codes frais CNC", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "04b727f0-f8f0-497f-9b79-44b47e255828", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:37", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:37"}, {"kompetensi_dasar_id": "04b78f4c-5bc2-4041-8d24-51b8263952b7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis <PERSON><PERSON><PERSON><PERSON> (3): 190-191, dan <PERSON><PERSON><PERSON><PERSON> (3): 159, serta hadits tentang berpikir kritis dan bers<PERSON> demokratis,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:34", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:34"}, {"kompetensi_dasar_id": "04b95779-c001-4d1e-8773-f07f2913ef1f", "id_kompetensi": "3.35", "kompetensi_id": 1, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis teknik kepemimpinan dalam kegiatan perbenihan tanaman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "04b9af34-99e9-4aad-8182-262041bd37d1", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 824060100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "04ba6547-d142-4ee0-88e9-2d50df099cc9", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 827350800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami cara perawatan dan perbaikan alat-alat penolong di atas kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "04bb09a2-7de5-4bbe-b315-5c6df8ae2d88", "id_kompetensi": "3.43", "kompetensi_id": 1, "mata_pelajaran_id": 823170610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami dasar produksi dengan system SCADA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:42", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "04bb1815-80d2-42b2-98b2-5749f48f09f9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "04bb80d1-322e-4f66-abba-2cd7c0656c39", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 804060400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang rencana kerja mingguan proyek konstruksi irigasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "04bce683-98f7-4557-beef-73274e574bd4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:00:12", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:12"}, {"kompetensi_dasar_id": "04beede4-ceed-4ec7-8b43-15d1e33ec7e3", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 806010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merakit rangkaian pengontrolan electric/hot gas defrost", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "04bf6572-e5e5-4fd2-9625-c9eb9e23c6f3", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa papan hubung bagi utama tegangan menengah (Medium Voltage Main Distribution Board).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "04c0b375-192b-4206-aecd-42fba9b9b04d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804100800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang komponen dan sirkit instalasi penerangan tegangan rendah tiga fasa yang digunakan untuk bangunan industrI.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "04c38db7-6d81-4a0c-a167-7bc494330a80", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan objek k<PERSON>, menganalisis informasi terkait sifat-sifat objek dan menerapkan aturan transformasi geometri (refleksi, translasi, dilatasi, dan rotasi) dalam memecahkan masalah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:15", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:02:15"}, {"kompetensi_dasar_id": "04c43516-f16d-42cb-9eeb-a7b3f58326eb", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memresentasikan Tri Purusha sebagai manifestasi Ida Sang Hyang W<PERSON>hi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:57:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:57:11"}, {"kompetensi_dasar_id": "04c5d3b0-8176-4e94-a5eb-aa540e351ac3", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 830050100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>, tujuan dan jenis pen<PERSON>an rambut blow dry dan penataan rambut.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:22", "updated_at": "2019-06-15 15:07:22", "deleted_at": null, "last_sync": "2019-06-15 15:07:22"}, {"kompetensi_dasar_id": "04c805fc-21ca-4c15-8947-a2dc975d975f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804040200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan Ke<PERSON>ama<PERSON>\r\ndan <PERSON>ja dan\r\n<PERSON>du<PERSON> dalam\r\npelaksanaan peker<PERSON><PERSON> Gedung\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:23", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:23"}, {"kompetensi_dasar_id": "04c857e8-a8f2-43fd-8477-3bc6f3a41061", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi peran Indonesia dalam hubungan Internasional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:40"}, {"kompetensi_dasar_id": "04c877d2-59d8-4381-a84d-7dfbb94b84f9", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803070900, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik keselamatan kerja dalam bidang pekerjaan elektronika industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "04c917e3-ea57-471b-918e-018bd1f41e2d", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengabstraksi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik secara lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:08", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:08"}, {"kompetensi_dasar_id": "04ca5594-3e4a-41dd-b052-7bb664d4b316", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memelihara sistem audio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:24", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:29:24"}, {"kompetensi_dasar_id": "04ca6aeb-1c5f-485c-bcac-d6f5920a4c10", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820140400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mendiagnosis kerusakan poros roda", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:56", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 15:33:56"}]