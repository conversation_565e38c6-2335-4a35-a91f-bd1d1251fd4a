[{"kompetensi_dasar_id": "2c28ced3-98d2-4017-bcba-836daa4c7255", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 875190100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat macam-macam rangkaian control dengan kontaktor relai", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:40", "updated_at": "2019-11-27 00:29:40", "deleted_at": null, "last_sync": "2019-11-27 00:29:40"}, {"kompetensi_dasar_id": "2c28fa46-05af-4c05-b250-c74123a64b2b", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan di wilayah setempat melalui pengamatan dari berbagai sumber", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:11", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:11"}, {"kompetensi_dasar_id": "2c29d8d5-e10c-4cb7-ae21-208f15d3d44f", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> proses mesin konversi energi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:38", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:38"}, {"kompetensi_dasar_id": "2c2b004b-a572-476a-a177-bd91bfc17631", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 825110100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis kapasitas kerja alat mesin pasca panen (perontok/pemipil/pemisah/ pemindah/pengupas)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:43", "updated_at": "2019-11-27 00:28:43", "deleted_at": null, "last_sync": "2019-11-27 00:28:43"}, {"kompetensi_dasar_id": "2c2b7b37-d7ac-460f-beed-306003f60f8f", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis geometri gambar teknik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:20", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:29:20"}, {"kompetensi_dasar_id": "2c2bd45b-0055-42c7-83e3-c8aec3574642", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan perencanaan sucker rod pump", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2019-11-27 00:27:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "2c2da42d-2428-4a5c-830f-8f24fd1745dc", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827130110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> komponen bantu mesin <PERSON>in", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:06", "updated_at": "2019-11-27 00:29:06", "deleted_at": null, "last_sync": "2019-11-27 00:29:06"}, {"kompetensi_dasar_id": "2c2ee02e-8377-4a28-b9ce-7335d4d043b2", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 827320110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Show of curves of statical stability", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "2c2f6ec4-eca9-4b79-ab29-4e701366f74f", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 829131300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkatagorikan  berbagai jenis kue Indonesia berdasarkan bahan dasar dan ciri-cirinya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2019-06-15 15:07:21", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "2c312493-5883-43c7-a81c-9a803ec1b020", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 807022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat rangkaian rectifier", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "2c31eaa9-91a7-4f3b-a5d2-818f9ff2d975", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 821060300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menya<PERSON><PERSON> simbol,teks dan ukuran gambar menggunakan CAD", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "2c34fd3e-6ce8-451c-965f-befebbe194d0", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> hasil evaluasi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:07", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:07"}, {"kompetensi_dasar_id": "2c35f268-cb7a-4054-b667-f261dc5de463", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804120300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengelasan pelat pada sambungan sudut posisi atas kepala (4F) dengan las gas metal (MIG/MAG)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:24", "updated_at": "2019-11-27 00:29:24", "deleted_at": null, "last_sync": "2019-11-27 00:29:24"}, {"kompetensi_dasar_id": "2c372bc4-06d2-40fd-aa14-75caaf700040", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:59", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:59"}, {"kompetensi_dasar_id": "2c373921-4cab-4c91-a368-f62644b30538", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengancable duct  dan cable trench.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:59", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:49:59"}, {"kompetensi_dasar_id": "2c374841-c633-4b17-81ab-8e77a7bd6176", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "2c378988-8e34-4ff7-ab53-f3e5f575e314", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:31", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:31"}, {"kompetensi_dasar_id": "2c39c4e6-c1e7-4126-9ec3-bf47002a4240", "id_kompetensi": "2.4.4", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat tulisan tentang musik berdasarkan jenisnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:47", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:31:47"}, {"kompetensi_dasar_id": "2c3a7065-e53b-4210-908e-b48abb69a578", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 817130100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan spesifikasi produk migas yang dikeluarkan oleh <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:05", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "2c3b645f-e458-485b-8723-22ea24b5ed15", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 821200400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan langkah-langkah pemasangan perabot interior kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "2c3be0a9-0a00-4d14-8b3d-72cf974c9f99", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:37", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:37"}, {"kompetensi_dasar_id": "2c3ca71e-21eb-4fd7-bd51-450b4faf6808", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 826130100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merumuskan cara meningkatkan hasil usaha agroforestry", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "2c3d9981-56f2-4d94-92f5-978651666d32", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 800081300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan teknik aseptik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:19", "updated_at": "2019-11-27 00:30:19", "deleted_at": null, "last_sync": "2019-11-27 00:30:19"}, {"kompetensi_dasar_id": "2c3e18d8-5510-43e2-8b57-e13a60cedf73", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 806010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis koordinasi proteksi jaringan distribusi tenaga listrik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "2c3e252a-9e77-4bc4-95d1-04f29fa25248", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Bahasa Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:04", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:04"}, {"kompetensi_dasar_id": "2c408840-bd51-4b5b-9217-0f80fc446918", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 827350300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Describes methods of mooring to a buoy", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:07", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:58:07"}, {"kompetensi_dasar_id": "2c41619e-baf2-4749-bcf5-7b28d3810895", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 820060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem audio", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:26", "updated_at": "2019-11-27 00:27:26", "deleted_at": null, "last_sync": "2019-11-27 00:27:26"}, {"kompetensi_dasar_id": "2c420982-551c-4308-981b-304ae3421906", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 809010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan praktik pengembangan film.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "2c422a8b-9475-42c9-96de-bf134fd36e0e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 821170610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perlakuan panas pada logam dasar pengelasan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:22", "updated_at": "2019-11-27 00:29:22", "deleted_at": null, "last_sync": "2019-11-27 00:29:22"}, {"kompetensi_dasar_id": "2c42446d-96a9-4124-89bb-ee81cffc714b", "id_kompetensi": "Teknologi dasar kef<PERSON> ", "kompetensi_id": 3, "mata_pelajaran_id": 800000125, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON> a<PERSON><PERSON> fase <PERSON>, pese<PERSON> didik mampu memahami melalui praktik dasar tentang proses pembuatan obat, mencakup praktik laboratorium yang baik, praktik dasar pemilihan obat, k<PERSON><PERSON><PERSON><PERSON> obat, dan j<PERSON><PERSON><PERSON><PERSON> bentuk sediaan obat. ", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:21", "updated_at": "2022-11-10 19:56:57", "deleted_at": null, "last_sync": "2022-11-10 19:56:57"}, {"kompetensi_dasar_id": "2c42d2a9-43a8-469e-8028-a3e5447b7071", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 843080510, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami musik pedalangan untuk mendukung pedalangan utuh dengan cerita Mahabharata dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 15:07:31"}, {"kompetensi_dasar_id": "2c4345c5-c51a-45a5-83cb-60888b8b63e8", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:29"}, {"kompetensi_dasar_id": "2c43ec94-41cf-4729-b977-66a319c39761", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 825030300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      <PERSON><PERSON><PERSON> prosedur dan teknik <PERSON>n", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "2c44113f-1164-4605-bfb9-39d9472a9b3a", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804140300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara pemantauan tanur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "2c44a9a3-1709-44aa-8e3a-220c30f306d3", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804011100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang etiket gambar teknik sesuai prosedur dan aturan penerapan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "2c450db1-48d4-4e03-8809-6432e11b9021", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 818010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pemetaan geologi eksplorasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "2c465c6f-7632-43e9-b7af-10dbcdc24cd9", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan faktor-faktor kemajuan dan kemunduran peradaban Islam di dunia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:44"}, {"kompetensi_dasar_id": "2c47d51d-decf-42d1-a5e4-ac4f879551b3", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:33", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:28:33"}, {"kompetensi_dasar_id": "2c47dd12-39b0-4f9e-b35c-004aca489cf9", "id_kompetensi": "2.4.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi berdasarkan pilihan sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:26", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:26"}, {"kompetensi_dasar_id": "2c47ea2b-2583-4072-8c5a-d4ef0a6198db", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 822170500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan cara mematikan  peralatan kelistrikan elektromekanik jika terjadi kegagalan fungsi dan cara pengoperasian kembali.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:56", "deleted_at": null, "last_sync": "2019-06-15 15:06:56"}, {"kompetensi_dasar_id": "2c48b9ed-60d7-4f48-8525-67c9ceb188db", "id_kompetensi": "3.24", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Manganalisis proses pemotongan benda manufaktur kompleks (roda gigi lurus, dll)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "2c4ce410-c1e6-4901-b563-e713eb6daabd", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan faktor yang mempengaruhi struktur bangunan berdasarkan kriteria desain dan pembebanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:33", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:33"}, {"kompetensi_dasar_id": "2c4dbe51-224b-4d3e-b010-6420c98c335b", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Membandingkan teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "2c4dcbc4-6ac3-48c6-ba43-b077148d002f", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 401251060, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan pengisian pajak PPh Orang Pribadi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "2c4e2552-001b-4a6e-9e9b-bc9e4cd9354d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Penyetelan :  idle RPM, maximum RPM, exhaust  gas temperatur (EGT)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-10-18 06:43:59", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2c4e476b-56c8-42fc-9c18-dd4201e43a35", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 801030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "     Melakukan relasi secara positif terhadap korban NAPZA", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:27", "updated_at": "2019-11-27 00:30:27", "deleted_at": null, "last_sync": "2019-11-27 00:30:27"}, {"kompetensi_dasar_id": "2c4e73c2-413e-458c-ad6a-0dc19e72e1dd", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyimpulkan kelayakan bahan kimia untuk disimpan atau dibuang", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:55"}, {"kompetensi_dasar_id": "2c4f45e9-e7d0-4a73-a189-0712b304bcc8", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 842030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat produk 3D dengan teknik ukir datar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:29", "updated_at": "2019-06-15 15:07:29", "deleted_at": null, "last_sync": "2019-06-15 15:07:29"}, {"kompetensi_dasar_id": "2c5169ed-f0fa-4e95-8946-8b08e2c97d75", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:51", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:29:51"}, {"kompetensi_dasar_id": "2c53b64f-977d-4b26-996f-3cf5f774b0cb", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem pengendalian cascade pada besaran instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "2c55b637-3ee2-4e44-bbd4-31a623332c67", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan beberapa data karakteristik pemodelan perangkat lunak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "2c55b7b4-2a0e-4e64-8b81-d10cc0a67d1a", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 814031400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menentukan macam-macam dokumen muatan angkutan laut", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:03", "updated_at": "2019-11-27 00:30:03", "deleted_at": null, "last_sync": "2019-11-27 00:30:03"}, {"kompetensi_dasar_id": "2c5624f9-f6c4-40e4-8046-9bb805ae3275", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyiapan sampel dan standar analisis kromatografi lapis tipis (TLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:58", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:57:58"}, {"kompetensi_dasar_id": "2c562f76-a294-4c24-9bf8-ee8e33e39a5c", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 804110500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan teknik pemesinan bubut  kompleks untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:24", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:07:24"}, {"kompetensi_dasar_id": "2c565c18-6077-453f-aa16-d796fc8f0a95", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:25", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:25"}, {"kompetensi_dasar_id": "2c568c82-0d06-4595-b375-13bb8a29840c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 826050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> dinamika e<PERSON>em hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "2c572225-b342-4ca9-b20a-bd958173d384", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 825063100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi persiapan kandang dan peralatan dalam usaha aneka ternak (hewan kesayangan)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:18", "updated_at": "2019-11-27 00:28:18", "deleted_at": null, "last_sync": "2019-11-27 00:28:18"}, {"kompetensi_dasar_id": "2c596af9-234b-44c9-bd79-172e658918ab", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803070900, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengujian dan pengukuran pada proses perawatan dan perbaikan peralatan elektronik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "2c597fcd-f0ed-4395-804f-721fd30795ad", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis prosedur perawatan dan perbaikan konstruksi bangunan gedung yang tergolong restorasi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:45", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:33:45"}, {"kompetensi_dasar_id": "2c59cad3-9ba9-44d8-bd7b-8f14f9029cfe", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 817040110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengukuran Basic Sedimen And Water (Bs&W)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "2c5a0422-d576-40e6-80e7-9bf12beb58d9", "id_kompetensi": "Menciptakan", "kompetensi_id": 3, "mata_pelajaran_id": 700121000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": " peserta didik mampu menciptakan karya seni yang menun<PERSON>kkan pilihan\nketer<PERSON>n,medium dan pengetahuan elemen seni rupa atau prinsip desain tertentu\nyang sesuai dengan tujuan karyanya, dalam konteks ekspresi pribadi atau sesuai topik\ntertentu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2021, "created_at": "2022-10-19 15:59:25", "updated_at": "2022-11-10 19:57:02", "deleted_at": null, "last_sync": "2022-11-10 19:57:02"}, {"kompetensi_dasar_id": "2c5d6e90-eaab-41ee-b2bb-00fea2792dad", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 800061400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan asistensi berkaitan penyakit jantung dalam tindakan medis gigi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:16", "updated_at": "2019-11-27 00:30:16", "deleted_at": null, "last_sync": "2019-11-27 00:30:16"}, {"kompetensi_dasar_id": "2c5d7559-0b3a-4b0a-b67d-08a9ae3fdd27", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:09"}, {"kompetensi_dasar_id": "2c5d9a6d-0914-4417-bb6c-ddc83b409d6e", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 826050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> jenis-jenis tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:07", "updated_at": "2019-11-27 00:28:07", "deleted_at": null, "last_sync": "2019-11-27 00:28:07"}, {"kompetensi_dasar_id": "2c5df33f-4559-4be6-963f-5b68ab8e1430", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 803080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja sistem kontrol saklar manual", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:19"}, {"kompetensi_dasar_id": "2c5e68e0-c0c0-4d79-ac66-a36f29ade11e", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825061000, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menyajikan hasil pencatatan data produksi telur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:05", "updated_at": "2019-11-27 00:28:05", "deleted_at": null, "last_sync": "2019-11-27 00:28:05"}, {"kompetensi_dasar_id": "2c5f3c09-edbf-4b31-b170-44d5609a9eee", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401130400, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat biodiesel skala laboratorium", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2019-06-15 14:50:06", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "2c5f7f9a-84b3-4276-abda-6d50c62817ba", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 817010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan macam macam struktur geologi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "2c605efb-424e-415c-a37b-7d108b43cac3", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 820050500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan  sistem kemudi dan Power Steering", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "2c609607-d114-4e25-ae7c-1bdbf788602e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kasus pelanggaran hak dan pengingkaran kewajiban sebagai warga negara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:22"}, {"kompetensi_dasar_id": "2c6176fe-a3ac-4436-90aa-7e47ae39a833", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pengangkutan hasil panen pengangkutan hasil panen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "2c61b412-0b8c-4f4f-b649-9c7cefd82c0b", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonstruksikan teks eksposisi berkaitan bidang pekerjaan dengan memerhatikan isi (per<PERSON><PERSON><PERSON>, argumen, pen<PERSON><PERSON><PERSON>, dan reko<PERSON>), struktur dan kebah<PERSON>an", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:54", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:54"}, {"kompetensi_dasar_id": "2c6364fe-a30d-4784-8cce-cd9a7e3ab450", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengenali dan mengidentifikasi sumber-sumber energi terbarukan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "2c657146-de0d-4a20-b195-f6c27773fa59", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804040300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Merancang  perbaikan konstruksi bangunan gedung yang tergolong rehabilitasi\r\n", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:08:32", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:08:32"}, {"kompetensi_dasar_id": "2c658a54-4d5c-47d1-96aa-f6fd9d3cb5fc", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 807021510, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan proses pembuatan Bill Of Material (daftar komponen dan harga) menggunakan Schematics Capture dan hasil <PERSON>i Bill Of Material dari rangkaian kelistrikan dan elektronik pada aplikasi CAD", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:57", "updated_at": "2019-11-27 00:29:57", "deleted_at": null, "last_sync": "2019-11-27 00:29:57"}, {"kompetensi_dasar_id": "2c6603a6-f1a8-4cfd-9445-ca8d57a55e06", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 821090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan mesin frais CNC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "2c671b2f-c489-483a-b39d-a9761090653c", "id_kompetensi": "3.34", "kompetensi_id": 1, "mata_pelajaran_id": 821200300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan tempat tidur untuk kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "2c688676-31ea-4be5-93f9-fe005580548d", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 802030410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan cara menggambar ruang tamu program perangkat lunak visualisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:58", "updated_at": "2019-11-27 00:27:58", "deleted_at": null, "last_sync": "2019-11-27 00:27:58"}, {"kompetensi_dasar_id": "2c6896e8-34c6-4ab0-8c76-c225c8ec6d1f", "id_kompetensi": "4.37", "kompetensi_id": 2, "mata_pelajaran_id": 803071400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengintegrasikan peneri<PERSON>/LNB pada sistem komunikasi satelit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "2c68f8c5-6096-4b81-b4b5-6323ac1c4b03", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 803080910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pressure tranducer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:17", "updated_at": "2019-11-27 00:29:17", "deleted_at": null, "last_sync": "2019-11-27 00:29:17"}, {"kompetensi_dasar_id": "2c69d1df-e1e4-4af1-8ef2-18cb0d1c1e83", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 840030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> alat bantu dan bahan dekorasi keramik clay body leather hard", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "2c6a31cf-537c-4838-b6f5-7a77345736a4", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:55", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:59:55"}, {"kompetensi_dasar_id": "2c6a7c16-407d-4516-9fcd-0c58f87256cb", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 803071200, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknologi pemrosesan gambar televisi analog dan digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "2c6cd550-1935-494a-bba1-1d7ec4eb9265", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 839080100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan alat dan bahan yang digunakan pada setiap keteknikan jahit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2022-11-10 19:57:42", "deleted_at": null, "last_sync": "2019-06-15 15:07:27"}, {"kompetensi_dasar_id": "2c6d4c31-e95d-463c-bd69-7ce4b3ada3a0", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804040500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan prosedur per<PERSON>an upah untuk pekerjaan konstruksi bangunan gedung.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:29", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 15:30:29"}, {"kompetensi_dasar_id": "2c6d8196-3f6b-4acc-8a77-a9bf0b2fa78c", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 803071500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan perhitungan rugi-rugi dalam saluran transmisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "2c6f7a5a-12c5-45c5-97cd-acf90db25a1a", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyebutkan fungsi sosial dan unsur kebahasaan dalam lagu.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:33:30"}, {"kompetensi_dasar_id": "2c71b644-8716-49aa-b7df-5ede87125180", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    <PERSON><PERSON><PERSON><PERSON> peluang usaha benih", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:50", "updated_at": "2019-11-27 00:27:50", "deleted_at": null, "last_sync": "2019-11-27 00:27:50"}, {"kompetensi_dasar_id": "2c721972-023a-4ff8-928e-7b65166a1734", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat integral tak tentu dari aturan dan sifat turunan fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:23", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:23"}, {"kompetensi_dasar_id": "2c73969f-2bd0-412c-abfd-3cc6ed412d6d", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 826050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan penentuan volume pohon", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "2c74f32d-2604-43cf-97ee-bb71c7da950a", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 804100600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa konstruksi sambungan tenaga listrik tegangan men<PERSON> (SLTM)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:46", "updated_at": "2022-10-19 23:19:25", "deleted_at": null, "last_sync": "2019-06-15 15:12:17"}, {"kompetensi_dasar_id": "2c75d7de-7fdc-491a-b73f-7e9b92377e7c", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825200100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menalar meat borne disease.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "2c7960b3-7cf8-48bb-be22-c298b3b83587", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 814030800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur tata letak fasilitas berdasarkan aliran produksi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:28", "updated_at": "2019-11-27 00:29:28", "deleted_at": null, "last_sync": "2019-11-27 00:29:28"}, {"kompetensi_dasar_id": "2c7a15af-9c42-431e-8f7f-da8e3c5a364b", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 803080800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memeriksa sistem kontrol Direct On Line (DOL) berbasis kontaktor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:35", "updated_at": "2019-11-27 00:28:35", "deleted_at": null, "last_sync": "2019-11-27 00:28:35"}, {"kompetensi_dasar_id": "2c7a1ed1-850a-4bf7-adff-1a9ba7bfb052", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis strategi yang diterapkan negara Indonesia dalam menyelesaikan  ancaman terhadap negara dalam memperkokoh persatuan bangsa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:40", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:08:40"}, {"kompetensi_dasar_id": "2c7ac14d-84f3-400a-8687-f692bb4e0a48", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 811010300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan standard laporan keuangan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:58:06", "updated_at": "2019-06-15 14:58:06", "deleted_at": null, "last_sync": "2019-06-15 14:58:06"}, {"kompetensi_dasar_id": "2c7b0b96-23b7-4836-97ce-e25c74e08d9c", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 820100200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menjelaskan fungsi komponen pada sistem pemasukan dan pengeluaran gas buang", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:02:36", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:36"}, {"kompetensi_dasar_id": "2c7c8577-dfe5-4dcb-bfff-4a608a3ed3eb", "id_kompetensi": "3.1", "kompetensi_id": 2, "mata_pelajaran_id": 814031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis transportasi multimoda", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:40", "updated_at": "2019-11-27 00:29:40", "deleted_at": null, "last_sync": "2019-11-27 00:29:40"}, {"kompetensi_dasar_id": "2c7c92b4-e30e-400e-bdaf-7a1270cf74b1", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 300310400, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "    Mengomunikasikan wacana yang berkaitan dengan kegiatan pada waktu senggang (Himana toki) dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar sesuai konteks", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "2c7d12bb-ed07-4bdd-8cf0-f97231568174", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:22", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:22"}, {"kompetensi_dasar_id": "2c7d696f-be5e-4a40-b0f7-870300b0808e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:55"}, {"kompetensi_dasar_id": "2c7fc20a-a077-4b26-ac36-919a09a139c5", "id_kompetensi": "4.21", "kompetensi_id": 2, "mata_pelajaran_id": 804040400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengg<PERSON><PERSON> instalasi pemipaan sistem tata udara domestik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:17", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:17"}, {"kompetensi_dasar_id": "2c7fd3c9-2478-4e65-ae08-793584dadfa4", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keterampilan 4  gaya renang untuk memperbaiki keterampilan gerak, dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:50", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:50"}, {"kompetensi_dasar_id": "2c8133b7-aa22-4112-8cbd-c0931df14aa5", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804120300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pengelasan pelat dengan pelat pada sambungan sudut dan tumpul posisi vertikal dengan las gas metal (MIG/MAG)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:24", "updated_at": "2019-11-27 00:29:24", "deleted_at": null, "last_sync": "2019-11-27 00:29:24"}, {"kompetensi_dasar_id": "2c81a0e9-92df-4621-ba7c-5abd492a8e01", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menguraikan perannya  sebagai  pembawa damai sejahtera dalam kehidupan sehari-hari se<PERSON>u murid <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:18", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:06:18"}, {"kompetensi_dasar_id": "2c823284-b605-45b5-811c-3f959e28e12e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 802020400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis obyek basis data pada DBMS sederhana", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:16", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "2c82b5d5-c6cc-4302-a1de-3f4e159fcdf6", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 825200100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan milk borne disease", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:25", "updated_at": "2019-11-27 00:28:25", "deleted_at": null, "last_sync": "2019-11-27 00:28:25"}, {"kompetensi_dasar_id": "2c83006a-b91d-4a98-9429-53dd5bf2b29b", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 821090600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pekerjaan pada pembubutan komplek", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "2c830c20-6b38-42b3-bb9e-1a16ddf20dee", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 804150500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami simbol dan diagram listrik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "2c8351a3-e5f8-4b07-b63e-e6bfd36a5c6e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 824051500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan sistem green screen/blue screen", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "2c84529b-03f6-4226-b414-5cb59e8a74d3", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:24", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:02:24"}, {"kompetensi_dasar_id": "2c84a026-6018-49b6-9410-985235523089", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 818010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi pemetaan geologi metode kompas  langkah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 15:00:05"}, {"kompetensi_dasar_id": "2c8595eb-bbae-4fff-ab3c-60c4a6d7f1be", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 807020610, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami dasar-dasar pada sistem aircraft hydraulic & pneumatic systems", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "2c87818d-6985-439c-a8eb-4d9bfea29578", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807020300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan  Close/ install A/C panels/ Components other than complete systems for limited A/C types as en-dorsed in the C of C", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-10-18 06:43:58", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2c882a89-e964-4368-b5e9-cbd3f43b30c0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 100012050, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami arti HAM dan hubung<PERSON>ya dengan tuntutan keadilan yang <PERSON> kehendaki.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:30"}, {"kompetensi_dasar_id": "2c893d1b-eafd-41e7-a6ba-8a88e09a3a37", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis konsep desain/\r\nprototype dan kemasan produk\r\nbarang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:17", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:17"}, {"kompetensi_dasar_id": "2c8962ff-a3cc-453a-ae82-0c1e475e7e58", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804130300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan bentuk bentangan geometri lanjut benda Transisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2c89f888-66f6-4295-a9f8-77e329b0198e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804120200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengelasan pipa pada sambungan tumpul posisi mendatar (5G),  posisi 45? (6G) dan dengan las busur manual (SMAW)..", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2c8a7faf-9f53-4771-a578-0bcaf29aa12d", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dansifat turunan fungsi untuk menentukan gradien garis singgung kurva, garis tangen, dan garis normal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:47", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:06:47"}, {"kompetensi_dasar_id": "2c8b8ea3-0a48-435a-b9e2-85aabc58d929", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 802031800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses pembuatan film,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:26", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "2c8bb25f-0e41-42eb-a556-c84f0ccf22f8", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 804011000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggambar kontruksi geometris : isome<PERSON>k, dimetrik, per<PERSON><PERSON><PERSON><PERSON>, proye<PERSON>i miring", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:13", "updated_at": "2019-11-27 00:29:13", "deleted_at": null, "last_sync": "2019-11-27 00:29:13"}, {"kompetensi_dasar_id": "2c8c8504-77b5-4e7b-856e-1e9c33ca6bf8", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 802020600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami kinerja I/O bus komputer terapan jaringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:58", "updated_at": "2019-06-15 15:06:58", "deleted_at": null, "last_sync": "2019-06-15 15:06:58"}, {"kompetensi_dasar_id": "2c8c8533-49c9-4063-a20f-f2e2963702ec", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 803081400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan Mikroprosesor sebagai sistem kendali pada instrumentasi dan otomatisasi proses", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:53", "updated_at": "2019-11-27 00:27:53", "deleted_at": null, "last_sync": "2019-11-27 00:27:53"}, {"kompetensi_dasar_id": "2c8d1db8-177a-442f-8c5a-b31858a0a255", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 820130100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami  sistem hidraulic system (semua unit)  alat berat", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "2c8d672a-1566-4a86-8574-b46abe33166c", "id_kompetensi": "2.3.4", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pergelaran musik berdasarkan hasil kreasi  sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:48", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:05:48"}, {"kompetensi_dasar_id": "2c8d8192-f1ef-425d-832f-544e75628103", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 800020300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON>, per<PERSON><PERSON><PERSON><PERSON>, dan gangguan pada dewasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "2c8da4b9-56f1-416b-8243-b6690b45d329", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses fisika dan proses kimia pada industri  pengecoran logam/metalurgi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:06", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 14:50:06"}, {"kompetensi_dasar_id": "2c8e0a55-78bf-40e7-a152-6683b5b97746", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803080700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi operasi dan aplikasi aktuator pneumatik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:47", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:18"}, {"kompetensi_dasar_id": "2c8e1697-3179-4b8e-84fc-476461a3d408", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 827110340, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> bahan logam dan non logam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "2c8ea824-06bb-4391-afa7-afbfa5124645", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 801030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> tindakan bila terjadi kehilangan pada lanjut usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:15", "updated_at": "2019-06-15 15:03:15", "deleted_at": null, "last_sync": "2019-06-15 15:03:15"}, {"kompetensi_dasar_id": "2c8ecaed-8e89-4ecd-af5f-487a64aaf90b", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 804030200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan  teknik perawatan dan pengecekan jenis optic", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:05", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:30:05"}, {"kompetensi_dasar_id": "2c8f2dd8-6c8a-47e6-a20a-f16cb7119adf", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 807021600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan alat navigasi & fungsi dari alat navigasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "2c8f6a27-55fd-4a4e-9434-ba899041beb2", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 816040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses pencapan kasa datar (flat screen printing)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "2c90228e-b454-457b-a16c-bf5cf5ce6bb7", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825270400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan “Cleaning and Sanitation”", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:32", "updated_at": "2019-11-27 00:28:32", "deleted_at": null, "last_sync": "2019-11-27 00:28:32"}, {"kompetensi_dasar_id": "2c9059cd-89b3-451b-bd5b-ac467eb3efd4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803060900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kerusakan pada rangkaian penala penerima radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "2c90e9ad-4d32-4e17-934b-3551d50ee1a2", "id_kompetensi": "4.2.2", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON> (31): 13-14 dan Q.S. <PERSON> (2): 83 denagn lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:35", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:35"}, {"kompetensi_dasar_id": "2c911822-6d0b-46c6-98bd-0b384f948df6", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 802020300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan interaksi antar obyek dalam sistem berorientasi obyek.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:15", "updated_at": "2019-06-15 15:06:57", "deleted_at": null, "last_sync": "2019-06-15 15:06:57"}, {"kompetensi_dasar_id": "2c91fc9f-9071-4faa-87aa-263da2dd1688", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 828190100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis materi pemanduan wisata", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:39", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:26"}, {"kompetensi_dasar_id": "2c9647b3-a809-428b-ba01-41b25eeff811", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401141400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kadar bahan obat dengan metode volumetrik Asidi-alkalimetri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:03", "updated_at": "2022-10-19 23:19:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "2c969ed8-86c3-4514-b1b2-9fed4e3c6ad1", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:07", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:07"}, {"kompetensi_dasar_id": "2c979be6-9921-4f76-8cfc-f9f24b21d550", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengident<PERSON><PERSON><PERSON><PERSON> faktor-faktor yang mempeng<PERSON>hi olah gerak kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:01", "updated_at": "2019-11-27 00:29:01", "deleted_at": null, "last_sync": "2019-11-27 00:29:01"}, {"kompetensi_dasar_id": "2c97f7b0-edb6-49c0-9aa3-e6d01da594d2", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401131500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis mikrobiologi dalam bahan alam dan produk industri dengan metoda TPC.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:12:25"}, {"kompetensi_dasar_id": "2c98b497-3997-4c41-8d75-b4e35efc528d", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan keselamatan dan kesehatan kerja serta lingkungan hidup K3LH  ", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:04", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:04:04"}, {"kompetensi_dasar_id": "2c98b64b-f821-45d5-9aea-9e68f063871f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 804130400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  sketsa gambar memberi label bagian-bagian dari <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2019-06-15 14:50:00", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2c98e615-2846-4022-bb7b-a7f6d7bdd4da", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menangkap makna teks penyerta gambar (caption).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:29"}, {"kompetensi_dasar_id": "2c9974df-b4a7-44d3-91cc-b9d1903c8a80", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "el<PERSON><PERSON><PERSON> perilaku jujur dalam kehidupan sehari-hari.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:00", "updated_at": "2019-11-27 00:30:00", "deleted_at": null, "last_sync": "2019-11-27 00:30:00"}, {"kompetensi_dasar_id": "2c998915-0875-4480-a90a-7824ffdf0ed2", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 820050500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan  sistem rem Konvensional", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "2c99dfe3-65e0-4db9-af41-0802555f03d2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:03:14"}, {"kompetensi_dasar_id": "2c9a0c11-81fe-4def-ab84-b1e91542b597", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 843040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat sound effect  untuk keperluan artistik dengan menggunakan teknologi manual.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:56", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 14:49:56"}, {"kompetensi_dasar_id": "2c9a3510-7bc7-446c-99e4-d144efbb434f", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 817150110, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis kesehatan dan penyakit akibat kerja", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2019-11-27 00:30:21", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "2c9ba11e-413f-48ad-ae37-a794cf74e02e", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 811010100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pem<PERSON>han warna dengan raster.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2019-06-15 14:50:02", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "2c9d194c-6262-49b8-9a55-f650fc82c514", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> se<PERSON>ah tentang peran pelajar, ma<PERSON><PERSON><PERSON> dan tokoh masyarakat dalam perubahan politik dan ketatanegaraan Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:33:19"}, {"kompetensi_dasar_id": "2c9e0606-db04-4a36-9820-c1047db3e58f", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan persamaan dan pertidaksamaan nilai mutlak bentuk linear satu variabel", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:39", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:39"}, {"kompetensi_dasar_id": "2c9e6126-a939-4137-a1fd-5400bcba402c", "id_kompetensi": "3.31", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis teknik instrumen (technique of the instrumen) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:19", "updated_at": "2019-11-27 00:28:19", "deleted_at": null, "last_sync": "2019-11-27 00:28:19"}, {"kompetensi_dasar_id": "2c9e94bd-9061-481c-a2e0-63a8b4cfa940", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemesinan bubut CNC", "kompetensi_dasar_alias": "Mampu menggunakan  teknik pemesinan bubut CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:03", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:03"}, {"kompetensi_dasar_id": "2c9f727d-6339-47a8-abe6-9606c5e6ee10", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 300310600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menciptakan kembali ungkapan terkait lalu lintas dengan memperhatikan fungsi sosial struktur teks dan unsur kebahasaan pada teks interaksi transaksional lisan dan tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:10", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:54:10"}, {"kompetensi_dasar_id": "2ca08a0b-094c-4913-8dbb-4c3889f1f236", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 817120110, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembuatan gambar detail kamar mandi/WC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:42", "updated_at": "2019-11-27 00:27:42", "deleted_at": null, "last_sync": "2019-11-27 00:27:42"}, {"kompetensi_dasar_id": "2ca0b70a-9efe-4a75-a094-35f4b6fb58dc", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 817100100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pengoperasian DCS sesuai SOP", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:05", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:05"}, {"kompetensi_dasar_id": "2ca1a154-6627-4230-a591-3ccffb873693", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 600090000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Menganalisis penentuan dan penyepakatan tindakan yang tepat untuk penyelesaian keluhan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:30", "updated_at": "2019-11-27 00:30:30", "deleted_at": null, "last_sync": "2019-11-27 00:30:30"}, {"kompetensi_dasar_id": "2ca2db23-6876-4129-9287-630c9d3f2ead", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> ajaran up<PERSON>a sebagai tuntunan hidup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:54:39", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:06:17"}, {"kompetensi_dasar_id": "2ca326ae-2822-4d97-80aa-79a452bb9b9f", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan entry transaksi berbasis fee pada bank", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:38", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "2ca45036-a0c4-42f7-ba64-dc55c97cabdb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja peralatan dan karakteristik jenis kebakaran dalam prosedur pengg<PERSON>an <PERSON>at Pemadam <PERSON> (APAR)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "2ca64c2d-13c4-41d9-875d-ec520a29d7f7", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>n unsur non musikal vokal karawitan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "2ca705b2-fb25-4809-ac83-e0a2e225bcf9", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usahadengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:33"}, {"kompetensi_dasar_id": "2ca8531d-873f-4a9b-8e07-2de02b453622", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825100200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kapasitas kerja Traktor Pertanian Roda Empat disertai sistem otomatisasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:46", "updated_at": "2019-11-27 00:28:46", "deleted_at": null, "last_sync": "2019-11-27 00:28:46"}, {"kompetensi_dasar_id": "2ca93bfc-56b6-4a82-9a2f-f883086f18ef", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804110600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan   teknik  pembuatan benda kerja   pada mesin frais, dengan su<PERSON>n/toleransi khusus.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2ca97d38-427a-4f4b-bd06-5aef2526e3fd", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825040300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menunjukkan teknik pembiakan dalam produksi benih secara vegetatif", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "2caab791-7a9b-4afc-8846-83f13083f92b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 825170100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengendalian penyakit hewan.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:08", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 15:07:08"}, {"kompetensi_dasar_id": "2cac45a6-2b53-45e3-8603-d99a7d1268fb", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 807020810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> rang<PERSON> penguat (amplifier )", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:39", "updated_at": "2019-11-27 00:29:39", "deleted_at": null, "last_sync": "2019-11-27 00:29:39"}, {"kompetensi_dasar_id": "2cac89e5-0dea-478e-b8fe-dee0c5808403", "id_kompetensi": "3.29", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menurunkan aturan dan sifat integral tak tentu dari aturan dan sifat turunan fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:16", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:16"}, {"kompetensi_dasar_id": "2cacd37e-f593-43b7-bbfb-cc89da403dd3", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis peluang usaha\r\nproduk barang/jasa", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "2cacf617-2397-42bc-b053-8a6473f21a7c", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON>an pembangkit gelombang", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "2cadfbbc-1cbc-4cd6-b66c-8a71f399655b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827210100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan waktu panen sesuai ukuran dan umur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:12", "updated_at": "2019-06-15 15:07:12", "deleted_at": null, "last_sync": "2019-06-15 15:07:12"}, {"kompetensi_dasar_id": "2cae0a04-a6a9-4cd4-b790-1559befda899", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 821080300, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON><PERSON> bagian-bagian mesin lamelo", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2019-11-27 00:27:57", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "2cae51e3-24b9-46c7-8735-afab6d707e35", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 100013010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan diri sebagai citra Allah yang memiliki kemampuan dan keterbatasan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:41", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 14:55:29"}, {"kompetensi_dasar_id": "2caf4d18-58f3-44c6-8350-a83d22393c79", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 803060500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem antena penerima TV", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:37", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:31:37"}, {"kompetensi_dasar_id": "2cb05d56-d72a-40ec-a483-02be4273150d", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menyajikan luas area gambar", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:46", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:32:46"}, {"kompetensi_dasar_id": "2cb08376-221c-4cf3-a868-5b0d0a4675ab", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804010700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan aturan gambar kerja pada gambar menggunakan CAD 2D", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "2cb117f5-4154-4013-bbff-69b20bbcce2d", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 800020500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melaksanakan kegiatan Kesehatan <PERSON> (KIA)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:11", "updated_at": "2019-11-27 00:30:11", "deleted_at": null, "last_sync": "2019-11-27 00:30:11"}, {"kompetensi_dasar_id": "2cb211bb-19bb-4480-afa0-d0f5e34de03b", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 840030100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat dekorasi keramik claybody leather hard teknik toreh (sgraffito)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:27", "updated_at": "2019-06-15 15:07:28", "deleted_at": null, "last_sync": "2019-06-15 15:07:28"}, {"kompetensi_dasar_id": "2cb3f356-b180-43e7-a5ad-784396508876", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 826120100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami cara pengumpulan data dan informasi lapangan mengenai perencanaan reklamasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "2cb49db3-2a78-4b0d-91ab-a21c700c4149", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 817120100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur operasi fungsi masing masing peralatan proses pengolahan migas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 14:50:48", "updated_at": "2019-06-15 15:00:04", "deleted_at": null, "last_sync": "2019-06-15 15:00:04"}, {"kompetensi_dasar_id": "2cb4a61e-d9fa-455b-8fba-e49ced674a48", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 821020100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> prosedur running repair", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:55", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-06-15 14:52:55"}, {"kompetensi_dasar_id": "2cb5cab9-3787-4d8a-ad49-6f04067c23b7", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan mengevaluasi taktik dan strategi permainan (pola  menyerang dan bertahan) salah satu permainan bola besar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:18", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:18"}, {"kompetensi_dasar_id": "2cb63ca0-a053-4d8d-b6ba-d5fbe76a1f8d", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 804132300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan perakitan pelat dan lembaran yang meliputi pemahaman terhadap lembar kerja, pem<PERSON>han peralatan dan bahan, melaksanakan perakitan/pengg<PERSON><PERSON><PERSON>, pengetesan hasil perakitan serta penanganan dan penyimpanan hasil rakitan dengan aman", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:56", "updated_at": "2019-11-27 00:27:56", "deleted_at": null, "last_sync": "2019-11-27 00:27:56"}, {"kompetensi_dasar_id": "2cb785c6-f269-45d3-900e-5fb7328842c8", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 803070810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merencanakan program aplikasi sederhana dengan mikrokontroller", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "2cb85f65-09c5-4cfc-ae75-0a7a93459858", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 843060620, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik tata busana padu padan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "2cb88b16-ef67-4dba-875a-a54d85924de9", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 801040300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan komunikasi dan bahasa sesuai tingkat usia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:14", "updated_at": "2019-06-15 15:03:14", "deleted_at": null, "last_sync": "2019-06-15 15:03:14"}, {"kompetensi_dasar_id": "2cb8acbf-a9c7-42ea-aeea-bb3e90877668", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>pta karya pengolahan dari bahan nabati dan hewani menjadi produk kesehatan yang berkembang di wilayah setempat dan lainnya sesuai  teknik  dan prosedur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:06", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:06"}, {"kompetensi_dasar_id": "2cbc046e-1f22-487d-9e68-590da8c71f1e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 815010900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pengendalian mutu", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "2cbc2b35-6d89-46ce-b715-7d5e983e02d3", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 830050200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis tipe-tipe salon SPA", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "2cbc3e42-41a0-4d63-8f81-51286c1431ec", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 831080800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakuka<PERSON> pengg<PERSON>ran bahan berdasarkan ukuran dan jumlah produksi blus", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "2cbc6ee2-4624-4da3-9faa-c2081870778c", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:15"}, {"kompetensi_dasar_id": "2cbd77dc-e8ff-47e3-b974-09e0d4c68ffb", "id_kompetensi": "2.4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menampilkan musik kreasi dengan membaca partitur lagu", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:35", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 16:01:35"}, {"kompetensi_dasar_id": "2cbd9299-51f6-46d3-90e0-92d89f419d22", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 803090100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis inovasi prosedur perawatan instrumentasi medik berbasis komputer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:00:02"}, {"kompetensi_dasar_id": "2cbdcfbf-f8ff-49d8-938a-9221de04dfe7", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807021910, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki hasil pemeriksaan kurang baik sampai sesuai dengan spesifikasi yang di tetapkan.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:47", "updated_at": "2019-11-27 00:29:47", "deleted_at": null, "last_sync": "2019-11-27 00:29:47"}, {"kompetensi_dasar_id": "2cbdfd81-3e31-4d43-9f05-19ca3a951c39", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan sistem pen<PERSON>ia bahan bakar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "2cbec854-add7-462a-a5bc-6c1ce6556110", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penanganan limbah B3 dan non B3", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:25", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:30:25"}, {"kompetensi_dasar_id": "2cbee134-a8ce-4084-a603-efcbf3c5a0b6", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengimplementasikan repertoar lagu vokal non ritmis dengan orkestrasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:34", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:19"}, {"kompetensi_dasar_id": "2cbefca3-d8c1-4fca-b839-9be018b9c2a1", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 821080500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perawatan  Mesin Kayu stationer", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:08", "updated_at": "2019-06-15 14:50:08", "deleted_at": null, "last_sync": "2019-06-15 14:50:08"}, {"kompetensi_dasar_id": "2cbfe13e-b989-463e-9854-44ef0ab5276c", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 804011300, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan gambar potongan furnitur dengan teknik manual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:38", "updated_at": "2019-11-27 00:28:38", "deleted_at": null, "last_sync": "2019-11-27 00:28:38"}, {"kompetensi_dasar_id": "2cc00ea1-043f-44c2-9c16-807feea22944", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 800020700, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pertumbuhan dan dan tugas-tugas perkembangan usia pra sekolah (3-5 tahun)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:24"}, {"kompetensi_dasar_id": "2cc061fd-5c1f-4f71-becd-a65d8b15181a", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan instruksi kerja pengoperasian peralatan secara mandiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:13", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:13"}, {"kompetensi_dasar_id": "2cc0d333-9462-4aeb-9028-116501bfa161", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 829020400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan peralatan dan perlengkapan restaurant", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:40", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 14:58:27"}, {"kompetensi_dasar_id": "2cc24ff3-5d0b-4c7d-9464-e639a13da286", "id_kompetensi": ".", "kompetensi_id": 2, "mata_pelajaran_id": 828040100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:13", "updated_at": "2019-06-15 15:07:13", "deleted_at": null, "last_sync": "2019-06-15 15:07:13"}, {"kompetensi_dasar_id": "2cc2a0f5-8da9-4f93-9d2a-104bc8447eb2", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 826050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukankan kegiatan perlindungan hutan dari kerusakan akibat penyakit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:10", "updated_at": "2019-11-27 00:28:10", "deleted_at": null, "last_sync": "2019-11-27 00:28:10"}, {"kompetensi_dasar_id": "2cc2a788-396d-40c1-820e-7d50b99dfa87", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi kondisi bahan/pereak<PERSON>  Kim<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "2cc45c4d-83ec-4924-b635-c15c1e58746b", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 827190100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekayasa teknik pengelolaan induk ikan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2019-06-15 15:07:11", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "2cc68283-f8aa-42ec-856a-b9e7b5539c00", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 807022300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> rangkaian demultiplexer", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "2cc761aa-db0a-4be3-9690-9732bc34cc7c", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401251109, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jenis-j<PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:17", "updated_at": "2019-06-15 15:07:17", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "2cc80a00-0a62-433d-9cd6-c13c8b649bb5", "id_kompetensi": "4.66", "kompetensi_id": 2, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencoba dan menerapkan metode pencarian kesalahan transistor sebagai penguat daya akibat pergeseran titik kerja DC transistor.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "2cc8415b-e187-480b-98dd-eb176790eb7a", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 800070100, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis anatomi fisiologi sistem panca indera", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:54", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-11-27 00:27:54"}, {"kompetensi_dasar_id": "2cca5367-4e1e-43bb-aa79-1ddfb5706120", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan transaksi-transaksi pembelian tunai dan kredit bagi perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:14", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:07:14"}, {"kompetensi_dasar_id": "2ccaf9d7-6956-48a9-88cf-27b8932a1dd6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 824050800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan unsur naratif dalam produksi program televisi dan film", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "2ccbfa0e-078b-46db-a546-fe080c3bc600", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 802030900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> hasil gambar bentuk", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "2ccc52bf-d347-42d1-a7d5-7477783759e1", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 817010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON> jenis jenis batuan beku, batuan sedimen, dan batuan ubahan di alam", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:27", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:27"}, {"kompetensi_dasar_id": "2cccc155-bcae-4266-a0aa-98e031ee2ec0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengekplorasi urutan dan paktor pengulangan yang teratur dari bentuk, teknik dan isi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "2cccc96a-fb04-41ef-9183-275b8efbb9bb", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 819010100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan titrasi reduksi-ok<PERSON><PERSON> (redoksimetri)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:15", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:15"}, {"kompetensi_dasar_id": "2ccd7a62-1940-4199-88ea-be30026f2e95", "id_kompetensi": "4.45", "kompetensi_id": 2, "mata_pelajaran_id": 814031800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "      Melakukan penyiapan barang yang akan diangkut melalui angkutan perposan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:06", "updated_at": "2019-11-27 00:30:06", "deleted_at": null, "last_sync": "2019-11-27 00:30:06"}, {"kompetensi_dasar_id": "2ccdbbd3-3630-4d16-be0d-b367521a76e9", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 818010400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merancang pemetaan dengan lintasan kompas dan langkah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:49", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:00:06"}, {"kompetensi_dasar_id": "2ccdf2ef-83f1-4059-875d-c7e4d3eecec8", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825063600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menentukan pemeliharaan ternak unggas petelur fase grower", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:11", "updated_at": "2019-11-27 00:28:11", "deleted_at": null, "last_sync": "2019-11-27 00:28:11"}, {"kompetensi_dasar_id": "2ccf0b65-f71e-47f8-b5df-a9744482767a", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 820140400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem suspensi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:23", "updated_at": "2022-11-10 19:57:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:23"}, {"kompetensi_dasar_id": "2ccfadcf-4576-4410-9785-cbda7233999d", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memprak<PERSON><PERSON><PERSON> pelaks<PERSON>an pembagian waris dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:41", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:31:41"}, {"kompetensi_dasar_id": "2ccfb16c-1f52-4e67-a219-ac538d806db7", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 804040120, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menera<PERSON><PERSON> prosedur <PERSON> (RAB) pekerjaan finishing", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:15", "updated_at": "2019-11-27 00:30:15", "deleted_at": null, "last_sync": "2019-11-27 00:30:15"}, {"kompetensi_dasar_id": "2cd1b431-2da5-4e17-a1b0-f5bb4f05be23", "id_kompetensi": "4.26", "kompetensi_id": 2, "mata_pelajaran_id": 803070400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan jenis aktuator untuk sistem aplikasi robot mobile se<PERSON>ai keperluan dan tujuan robot mobile", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:52", "updated_at": "2019-11-27 00:27:52", "deleted_at": null, "last_sync": "2019-11-27 00:27:52"}, {"kompetensi_dasar_id": "2cd22cbf-eda8-4e16-8e9e-ec73ec39cd5d", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 401132100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis      khromato<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:53", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "2cd331d7-67cb-4962-a02e-917311570832", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 401130920, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan identifikasi zat warna pada kain selulosa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:55", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:28"}, {"kompetensi_dasar_id": "2cd38d89-2974-4209-a562-8bd427420ba0", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan dan menyelesaikan model mate<PERSON><PERSON> dalam bentuk persamaan matriks dari suatu masalah nyata yang berkaitan dengan persamaan linear.", "kompetensi_dasar_alias": "<p>Men<span>entukan jarak dalam ruang&nbsp;</span>(antar titik, titik\r\nke garis, dan titik ke bidang)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:59", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:00:59"}, {"kompetensi_dasar_id": "2cd4cf85-5063-4b26-9358-7b36db9d595d", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 825280200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan Standar Nasional Indonesia (SNI)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:27", "updated_at": "2019-11-27 00:28:27", "deleted_at": null, "last_sync": "2019-11-27 00:28:27"}, {"kompetensi_dasar_id": "2cd4fec5-c783-4684-86f4-42e653eee493", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 808080800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan perintah-perintah  CAD 2 dimensi sesuai konsep dan prosedur aircraft drawing", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:01", "updated_at": "2019-06-15 14:50:01", "deleted_at": null, "last_sync": "2019-06-15 14:50:01"}, {"kompetensi_dasar_id": "2cd528c1-bca3-483f-9c65-973401115492", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821120310, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan motor bantu kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:55", "updated_at": "2019-11-27 00:28:55", "deleted_at": null, "last_sync": "2019-11-27 00:28:55"}, {"kompetensi_dasar_id": "2cd56fcd-348d-4b4b-82bb-ddbf9758640c", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 807020300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat Landing Gear system (ATA 32)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "2cd8ca02-323c-4c04-bfcc-0bc0cdd276c9", "id_kompetensi": "3.25", "kompetensi_id": 1, "mata_pelajaran_id": 801031200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan aksessibilitas dan alat bantu pelayanan lanjut usia sakit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:13", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 15:03:13"}, {"kompetensi_dasar_id": "2cd94cf9-4b41-4580-9b67-207aa588abb8", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:55", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:29:55"}, {"kompetensi_dasar_id": "2cd98ea3-4f70-435b-848f-c0364643c9b2", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 830050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan ratus untuk perawatan badan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:35", "updated_at": "2019-11-27 00:30:35", "deleted_at": null, "last_sync": "2019-11-27 00:30:35"}, {"kompetensi_dasar_id": "2cdb77c1-92d9-4c42-b679-e74754197e6c", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan cara perhitungan biaya produksi, harga jual, k<PERSON><PERSON><PERSON><PERSON>, B/C rasio, R/C rasio dalam pembuatan analisis kelayakan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:59", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:28:59"}, {"kompetensi_dasar_id": "2cdc162c-67c8-44e1-87cd-97907850a914", "id_kompetensi": "3.33", "kompetensi_id": 1, "mata_pelajaran_id": 807022210, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis electronic device pada pesawat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:44", "updated_at": "2019-11-27 00:27:44", "deleted_at": null, "last_sync": "2019-11-27 00:27:44"}, {"kompetensi_dasar_id": "2cdc5137-96ef-4ca7-b569-0830e4d91e34", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804111100, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan Prosedur Perawatan peralatan Kelistrikan system mekatronik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "2cdc7c47-4a35-4686-b793-f373d2a34a0d", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 821090330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami perakitan komponen konstruksi bangunan atas kapal", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "2cdd7640-385f-45e6-a3ab-77602d52447b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:19", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:33:19"}, {"kompetensi_dasar_id": "2cde1333-e108-461a-bcf2-717b01544378", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804130700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat mixer (penggiling /pengaduk) sesuai POS", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:19", "updated_at": "2019-11-27 00:29:19", "deleted_at": null, "last_sync": "2019-11-27 00:29:19"}, {"kompetensi_dasar_id": "2cde4b06-e46c-4da8-bc2a-e734451a863d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 401251030, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kartu piutang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "2cde79b8-acdb-4402-bb81-258c19846f08", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pen<PERSON>an & pengukuran peralatan ukur elektronika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:54", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:59:54"}, {"kompetensi_dasar_id": "2cdeb683-8958-4000-b5e1-d7bca1e64d80", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:43", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:43"}, {"kompetensi_dasar_id": "2cdf1900-66f7-4980-876f-853cd8496ab2", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:33:33"}, {"kompetensi_dasar_id": "2ce0dd5e-975b-459d-ae9b-61ffc7a3152e", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 825250300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekayasa aquascape (air terjun didalam aquascap, aquascape tingkat dll)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:49", "updated_at": "2022-11-10 19:57:35", "deleted_at": null, "last_sync": "2019-06-15 14:52:49"}, {"kompetensi_dasar_id": "2ce1035b-f674-44b6-af08-64f2469e68d2", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi  rangkaian aktivitas gerak ritmik (masing-masing tiga hingga lima gerak).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:45", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:07:45"}, {"kompetensi_dasar_id": "2ce1978d-d78d-4042-b276-e8f12576f6e0", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803071300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa  proses  pembang<PERSON>t gel<PERSON>ang", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "2ce1af4e-ac52-48e2-898c-63a77c51767e", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804100700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis diagram satu garis gardu induk", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:15", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:15"}, {"kompetensi_dasar_id": "2ce261b3-2f03-4c3b-9228-58c09fc2fb97", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan pencatatan (umum, teknis dan finansial) ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "2ce2fa12-a45b-416f-98dc-9cb3ab635768", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:52", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:52"}, {"kompetensi_dasar_id": "2ce528a2-1457-4fb8-99ab-62b309f0fa6b", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 809020900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> persiapan pencetakan satu warna sesuai pesanan cetak dengan teknik cetak sablon/saring", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:57", "updated_at": "2019-11-27 00:28:57", "deleted_at": null, "last_sync": "2019-11-27 00:28:57"}, {"kompetensi_dasar_id": "2ce573ec-c84f-440a-8c58-da9a32d9bdfc", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "memeriksa instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "2ce68c31-9e4f-40e5-a6d1-511ed141cabe", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 814031800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "      Melaksanakan prosedur peneri<PERSON>an barang dari pengirim/ pemilik barang kepada perusahaan angkutan udara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:06", "updated_at": "2019-11-27 00:30:06", "deleted_at": null, "last_sync": "2019-11-27 00:30:06"}, {"kompetensi_dasar_id": "2ce6bb49-1887-4d78-8ff4-13688bf83188", "id_kompetensi": "4.30", "kompetensi_id": 2, "mata_pelajaran_id": 803071100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengatasi masalah pada perangkat telepone seluler.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:45", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:34"}, {"kompetensi_dasar_id": "2ce93c65-d157-419c-b6cd-25bbcc1ebc76", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 827090200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan cara pengoperasian alat tangkap pukat", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:56", "updated_at": "2019-11-27 00:28:56", "deleted_at": null, "last_sync": "2019-11-27 00:28:56"}, {"kompetensi_dasar_id": "2ce992da-12b0-4f16-b287-17ed983a816c", "id_kompetensi": "3.13", "kompetensi_id": 1, "mata_pelajaran_id": 822190200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pengoperasian PLTH secara bertahap", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:36", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-11-27 00:27:36"}, {"kompetensi_dasar_id": "2ce9ee33-d3a2-4953-ba37-4606b1900ef0", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang instalasi listrik dengan k<PERSON>, cable ladder dan cable tray/trunking.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:42", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:00:42"}, {"kompetensi_dasar_id": "2cec01df-8db4-420a-bd8c-5516fd1af2da", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:54", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 14:49:54"}, {"kompetensi_dasar_id": "2cec5cce-683c-495e-82c7-8cf9cc1db28a", "id_kompetensi": "4.1 Rupa", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> karya seni rupa dua dimensi berdasarkan melihat model", "kompetensi_dasar_alias": "", "user_id": "881946e1-fcde-4c87-8e1c-ed5c309853ac", "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:29:43", "updated_at": "2019-06-15 15:29:43", "deleted_at": null, "last_sync": "2019-06-15 15:29:43"}, {"kompetensi_dasar_id": "2cec6ae1-047c-4dd6-b4fc-1d2a4104bee2", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 832010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami teknik gambar alam benda, gambar  dengan teknik kering", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "2cecbcdf-364d-4576-8c80-87dc96a7c162", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain prosesproduksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi berdasarkan identifikasi kebutuhan sumberdaya dan prosedur berkarya dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:32", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:30:32"}, {"kompetensi_dasar_id": "2ced83ad-eb7d-430a-9701-9ba523417b40", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan data dari situasi nyata, memi<PERSON>h variabel dan mengkomunikasikannya dalam bentuk model matematika berupa persa<PERSON> fungsi, serta menerapkan konsep dan sifat turunan fungsi dalam memecahkan masalah maximum dan minimum.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:00", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:00"}, {"kompetensi_dasar_id": "2ceda169-0286-4801-ae68-1b8200ed933e", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 821060200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta dan menya<PERSON>kan bukaan dan memberi penandaan pada gambar kotak air laut", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "2cee12a1-ba83-4c88-84e9-d4c658b27571", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis Q<PERSON><PERSON> (31): 13-14 dan Q.S<PERSON> (2): 83, serta hadits tentang saling menasihati dan berbuat baik (ihsan).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:15", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:15"}, {"kompetensi_dasar_id": "2cee9817-3d3f-4c91-a317-e8b237e55fff", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 804100810, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memasang Instalasi Penerangan Jalan Umumsesuai dengan Peraturan Umum Instalasi Listrik (PUIL)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:42", "updated_at": "2019-11-27 00:29:42", "deleted_at": null, "last_sync": "2019-11-27 00:29:42"}, {"kompetensi_dasar_id": "2ceee950-2173-4a5d-851d-ee8a1464d75b", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 825021300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil evaluasi keuangan atau modal usaha perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2019-11-27 00:27:55", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "2cef9878-cebd-4d51-a209-3e0543b54f79", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 808060610, "kelas_10": null, "kelas_11": null, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemontrasikan <PERSON> membuat komponen pesawat udara dari komposit", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}, {"kompetensi_dasar_id": "2cf0451e-50d1-4a8f-9326-cff05cc894aa", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pengendalian penyakit tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "2cf07b65-34d1-42dc-b364-9b6057c3895a", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 828090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menata interior kantor (office arrangement)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:52", "updated_at": "2019-11-27 00:29:52", "deleted_at": null, "last_sync": "2019-11-27 00:29:52"}, {"kompetensi_dasar_id": "2cf0f0dd-da20-4fca-a59a-377f35fb2e43", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis berbagai kasus pelanggaran HAM secara argumentatif dan saling keterhubungan antara  aspek ideal, instrumental dan  praksis sila-sila <PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:16", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:16"}, {"kompetensi_dasar_id": "2cf1b894-fee4-492c-bc1e-fb908c02b2b2", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130300, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Melaksanakan pekerjaan laboratorium sesuai SOP K3LH", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:08"}, {"kompetensi_dasar_id": "2cf1daf9-5cd2-4db6-a6a5-56a2319d5171", "id_kompetensi": "3.22", "kompetensi_id": 1, "mata_pelajaran_id": 100016010, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bagian dan isi dari masing-masing kitab yang pokok (Sishu) dan kitab yang men<PERSON> (Wujing)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:05:57", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:05:58"}, {"kompetensi_dasar_id": "2cf30fa1-60c1-4c7b-ba50-1c516db289c4", "id_kompetensi": "3.19", "kompetensi_id": 1, "mata_pelajaran_id": 814031900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan prosedur pen<PERSON>n barang berbahaya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:06", "updated_at": "2019-11-27 00:30:06", "deleted_at": null, "last_sync": "2019-11-27 00:30:06"}, {"kompetensi_dasar_id": "2cf5230e-8715-44b5-9bca-a3a1a2464331", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 804100900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan gambar kerja (rancangan) pemasangan instalasi listrik dengan menggunakan sistem busbar.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:08", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:02:08"}, {"kompetensi_dasar_id": "2cf58465-2159-4e0b-8ed3-1ff123d4e829", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan gambar instalasi Perlengkapan Hubung Bagi (PHB) Penerangan Bangunan Industri Kecil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2019-11-27 00:27:59", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "2cf636ca-5c22-46f2-9336-ab00eb251d17", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis editing efek suara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:42", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:42"}, {"kompetensi_dasar_id": "2cf64c9e-e7d6-4787-96eb-48c151ca2f2c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 826140100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  Aplikasi Sistem Informasi Geografis (SIG) Untuk  Pemetaan Inventarisasi Tegakan Tinggal  (ITT)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:10", "updated_at": "2019-06-15 15:07:10", "deleted_at": null, "last_sync": "2019-06-15 15:07:10"}, {"kompetensi_dasar_id": "2cf67eb3-a150-4193-bcaf-b933d1c8839a", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 802031800, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penyuntingan dialog dan efek suara menggunakan aplikasi penyunting suara", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "2cf6b061-9472-4f30-9035-655929fb8c57", "id_kompetensi": "3.20", "kompetensi_id": 1, "mata_pelajaran_id": 100014140, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> alam-alam kehidupan dalam perspektif agama Buddha", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "2cf83787-c6ad-49aa-a20a-f96e07acb409", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 825020800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan evaluasi usaha tanaman pangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:40", "updated_at": "2019-11-27 00:27:40", "deleted_at": null, "last_sync": "2019-11-27 00:27:40"}, {"kompetensi_dasar_id": "2cf84e40-f568-4800-aa18-633294cfc625", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 828190130, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "      Membuat rute penerbangan berdasarkan hasil analisis geografi penerbangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:28", "updated_at": "2019-11-27 00:30:28", "deleted_at": null, "last_sync": "2019-11-27 00:30:28"}, {"kompetensi_dasar_id": "2cf8d88d-88e2-4fc0-953e-77546f9e87ce", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 815010700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil laporan pemeli<PERSON>an mesin <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:48", "updated_at": "2019-11-27 00:28:48", "deleted_at": null, "last_sync": "2019-11-27 00:28:48"}, {"kompetensi_dasar_id": "2cf8ed07-f1ef-486c-9675-ba63d0a08ae2", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819050100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan standar spesifikasi bahan dan alat dalam pengadaan fasitilas produksi industri kimia", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:02:20", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:02:20"}, {"kompetensi_dasar_id": "2cf902f3-21fb-4df7-84e8-ac82e8f9aad8", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 829040400, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "        Melakukan pemeri<PERSON>aan kualitas bahan makanan unggas dan hasil o<PERSON>ya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:31", "updated_at": "2019-11-27 00:30:31", "deleted_at": null, "last_sync": "2019-11-27 00:30:31"}, {"kompetensi_dasar_id": "2cfb4d14-c1e2-4bcc-95f7-17213b907cb6", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis dinamika penyelenggaraan negara dalam konsep NKRI dan konsep negara federal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:33:50"}, {"kompetensi_dasar_id": "2cfba074-3fcf-4cd6-9f9c-03d943d152c7", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:08:14"}, {"kompetensi_dasar_id": "2cfda0ef-cd53-40a9-95bc-9c69a7970f51", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 817010100, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menganalisis batuan beku", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:27", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-11-27 00:27:27"}, {"kompetensi_dasar_id": "2cff450f-2ce4-412c-90b4-8335ceea7ef5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401131500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan analisis mikrobiologi dalam bahan alam dan produk industri dengan metoda TPC", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:56", "updated_at": "2019-11-27 00:29:56", "deleted_at": null, "last_sync": "2019-11-27 00:29:56"}, {"kompetensi_dasar_id": "2cffddc0-92ea-4168-9f09-c0c7df0c61a8", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "2d021c8c-b400-4816-a0a5-273d301cf92b", "id_kompetensi": "3.28", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep integral tak tentu suatu fungsi sebagai kebalikan dari turunan fungsi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:05:16", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:05:16"}, {"kompetensi_dasar_id": "2d023520-4029-4f14-abe0-3f7568bfda25", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 827390400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Show monitoring system", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:08", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:08"}, {"kompetensi_dasar_id": "2d03bcd1-bf89-4d6a-aca6-18b9054a6860", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginterpretasi makna teks cerita sejarah, berita, i<PERSON><PERSON>, editorial/opini, dan cerita fiksi dalam novel baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:02"}, {"kompetensi_dasar_id": "2d04170a-0400-480a-beb4-69339b42f4ce", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 825210200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip fermentasi dan enzimatis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2019-06-15 15:06:49", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "2d04c388-3c76-42ee-a492-c4c3e49a4ae6", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "2d06a49c-d739-4bfa-80c1-e56051e37557", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 814040100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON> Proses (In Process Control/IPC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 15:07:03", "deleted_at": null, "last_sync": "2019-06-15 15:07:03"}, {"kompetensi_dasar_id": "2d06b63f-e705-4dcb-9604-49d2de91d6de", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 809020800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencipta perwajahan packaging dos ulang tahun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:02", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:50:02"}, {"kompetensi_dasar_id": "2d08f0de-0f2d-4094-87db-850679008476", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI) dan menya<PERSON><PERSON><PERSON> dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:31", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:06:31"}, {"kompetensi_dasar_id": "2d099f42-5c23-49d9-b7de-61acda62b66c", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 805010800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi informasi geospasial dasar sesuai spesifikasi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2019-11-27 00:27:49", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "2d0a7c6c-863b-48da-a7c0-8b46003fe4c2", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 821190200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengaturan tegangan dan frekuensi operasional generator pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "2d0be41a-c1c1-4665-9956-a405bd7f295e", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 830050300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perawatan tubuh dengan <PERSON>er", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:52", "updated_at": "2022-11-10 19:57:41", "deleted_at": null, "last_sync": "2019-06-15 15:12:24"}, {"kompetensi_dasar_id": "2d0ce6ad-da16-4ef7-b337-d79ac6283fe0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "2d0dfd8e-1094-4b16-b1a8-b2a55542ccf0", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 800020410, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menerapkan pemeriksaan penyakit sistem endokrin berdasarkan manifestasi klinis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:13", "updated_at": "2019-11-27 00:30:13", "deleted_at": null, "last_sync": "2019-11-27 00:30:13"}, {"kompetensi_dasar_id": "2d0e4391-cb3b-4666-b486-cca7783da5b4", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 875150020, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan kondisi  ignitor plug", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:25", "updated_at": "2022-11-10 19:57:45", "deleted_at": null, "last_sync": "2019-06-15 14:59:21"}, {"kompetensi_dasar_id": "2d0ec6ce-0c8c-446b-a223-f0bdd2e7af3f", "id_kompetensi": "3.41", "kompetensi_id": 1, "mata_pelajaran_id": 820030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis asesmen kinerja unit boiler/ketel uap.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "2d0ee818-a1c9-4812-bd2d-865e35c0e197", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi  perkembangan kehidupan  politik dan ekonomi bangsa Indonesia pada masa Demokrasi Liberal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:46", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:29:46"}, {"kompetensi_dasar_id": "2d0eea2d-0a1b-4a3c-ab23-a1bd44861574", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 807020810, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat rangkaian menggunakan thyristor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:39", "updated_at": "2019-11-27 00:29:39", "deleted_at": null, "last_sync": "2019-11-27 00:29:39"}, {"kompetensi_dasar_id": "2d0fdb7e-fa46-4a8e-b49c-915f7c630657", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807020100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuktikan airfoil terhadap performa pesawat udara", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:24", "updated_at": "2022-10-19 23:19:28", "deleted_at": null, "last_sync": "2019-06-15 14:59:20"}, {"kompetensi_dasar_id": "2d10de03-c971-4098-87d7-dce8e112958f", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 821090330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan perakitan pembuatan komponen konstruksi deck kapal <PERSON> dan <PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:26", "updated_at": "2019-11-27 00:28:26", "deleted_at": null, "last_sync": "2019-11-27 00:28:26"}, {"kompetensi_dasar_id": "2d11533e-5bb6-4372-8222-eada516471f0", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami ketentuan pernikahan dalam Islam", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:22", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:33:22"}, {"kompetensi_dasar_id": "2d1170a5-274d-48d7-a560-bcb3f5faab75", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 843050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>ink<PERSON> progresi akor", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "2d12b234-b898-4f02-848f-49ee2d4b2efc", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401251350, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan pengertian stock opname", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:18", "updated_at": "2019-06-15 15:07:18", "deleted_at": null, "last_sync": "2019-06-15 15:07:18"}, {"kompetensi_dasar_id": "2d12cd1a-3d97-4615-a4b4-b7c31610e93f", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 825110200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menghitung estimasi hasil produksi ikan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "2d134dac-77b1-4689-8c01-69e9de841e94", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON><PERSON> peran pelajar, ma<PERSON><PERSON><PERSON> dan tokoh masyarakat dalam perubahan politik dan ketatanegaraan Indonesia.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:36", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:30:36"}, {"kompetensi_dasar_id": "2d13be9b-7ce0-4a30-90ff-76dd39429c3f", "id_kompetensi": "4.24", "kompetensi_id": 2, "mata_pelajaran_id": 825180100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pemeriksaan patologi anatomi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "2d142a2b-6296-4684-8322-7c2cf43114dc", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 800060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentif<PERSON><PERSON> macam-macam abses dan kelainan lidah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:02", "updated_at": "2019-06-15 15:07:02", "deleted_at": null, "last_sync": "2019-06-15 15:07:02"}, {"kompetensi_dasar_id": "2d14ed14-7ec4-48fb-b4e3-35e75d0f5358", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "2d15a496-f881-47fc-92ed-d94028d59b20", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merekonstruksi upaya bangsa Indonesia dalam menghadapi ancaman disintegrasi bangsa terutama dalam bentuk pergolakan dan pemberontakan (antara lain: PKI Madiun 1948, DI/TII, APRA, <PERSON><PERSON>, RMS, PRRI, Permesta, G-30-S/PKI) dan menya<PERSON><PERSON><PERSON> dalam bentuk cerita sejarah.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:26", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 16:03:26"}, {"kompetensi_dasar_id": "2d15e798-f9b9-4941-80ed-b102e91a93b3", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses fisika dan proses kimia pada industri asam klorida, sulphur dan senyawa dari sulphur.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "2d1871e3-32dc-44c4-afe7-1f7259fde010", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 803081300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> kapasitansi pada besaran proses", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:23", "updated_at": "2022-10-19 23:19:24", "deleted_at": null, "last_sync": "2019-06-15 15:03:23"}, {"kompetensi_dasar_id": "2d19177e-750d-496d-832b-0b56865a8c90", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 802032400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan gerak kamera", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:07:25"}, {"kompetensi_dasar_id": "2d1a074a-3158-4afd-b9f3-f06a2303babe", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 816040100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami cara pembuatan gambar/design dan konsep k<PERSON>, keselamatan kerja dan lingkungan hidup (K3LH)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2022-11-10 19:57:28", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "2d1a3322-fcfc-40f0-9adb-75b33c39e79a", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 800081300, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pemeriksaan bakteri dalam air, makanan dan minuman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:32", "updated_at": "2022-11-10 19:57:18", "deleted_at": null, "last_sync": "2019-06-15 14:59:29"}, {"kompetensi_dasar_id": "2d1a61b8-eb7b-47ee-9425-d4ffb08ffe15", "id_kompetensi": "3.18", "kompetensi_id": 1, "mata_pelajaran_id": 824050700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan hunting dan survey lokasi produksi program televisi drama", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:32", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "2d1a67f2-22a1-4c2d-8da4-009c287643eb", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengeval<PERSON><PERSON> kompo<PERSON>, harmoni, dan estetika pada dekorasi dan ornamen eksterior", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:07", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 16:04:07"}, {"kompetensi_dasar_id": "2d1a7243-61a3-42c4-a6de-863911bada81", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 804101200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pelaporan pemeliharaan mesin listrik pembangkit", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:12:16", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:16"}, {"kompetensi_dasar_id": "2d1ab6ba-ddc9-4006-ad8a-95c3ba751e2d", "id_kompetensi": "4.34", "kompetensi_id": 2, "mata_pelajaran_id": 803061100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan grading (color correction)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:12", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:12"}, {"kompetensi_dasar_id": "2d1af736-2ce7-4f8a-9aca-4e2b0ec2c83b", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 822190500, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat tiruan bagian-bagian turbin angin PLTB", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:51:16", "updated_at": "2022-10-19 23:19:34", "deleted_at": null, "last_sync": "2019-06-15 14:51:16"}, {"kompetensi_dasar_id": "2d1b8ddd-b0b6-41d3-9ccd-589c604ecb17", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:54", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:54"}, {"kompetensi_dasar_id": "2d1bd78c-99c6-4be4-a7a7-c08b82924bf9", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802010200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan pelbagai teknologi pengembangan aplikasi web", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:09", "updated_at": "2019-06-15 14:50:09", "deleted_at": null, "last_sync": "2019-06-15 14:50:09"}, {"kompetensi_dasar_id": "2d1c5ec8-4b3b-4c50-b85f-91ef8869a3d4", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 821060101, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON>, macam-macam garis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:10", "updated_at": "2019-11-27 00:29:10", "deleted_at": null, "last_sync": "2019-11-27 00:29:10"}, {"kompetensi_dasar_id": "2d1dfd47-31c3-43ff-a820-539f624394c6", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:29", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:29"}, {"kompetensi_dasar_id": "2d1edd3f-8b30-48e2-bf66-e6f73d794d7e", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan penyiapan sampel dan standar analisis kromatografi kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:58", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:58"}, {"kompetensi_dasar_id": "2d1fdd1d-a837-4f83-a5a6-708ee9f12e97", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendesain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usahadengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:33", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:33"}, {"kompetensi_dasar_id": "2d20441c-6381-4f72-9c1c-2f153dfe07c7", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  Menganalisis unsur pembangun puisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:05", "updated_at": "2019-11-27 00:30:05", "deleted_at": null, "last_sync": "2019-11-27 00:30:05"}, {"kompetensi_dasar_id": "2d21517f-2e44-47dc-85e3-bcc209220c3f", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempraktikkan keterampilan 4 gaya renang,dan keterampilanrenang penyelamatan/pertolongan kegawatdaruratan di air, serta tindakan lanjutan di darat (contoh: tindakanresusitasi jantung dan paru (RJP)).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:14"}, {"kompetensi_dasar_id": "2d231c9b-b26e-4dee-bc58-eea1d804dada", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep dan prinsip titrasi dalam proses  titrimetri sederhana.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:33", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:33"}, {"kompetensi_dasar_id": "2d2395ef-5cd3-4a82-87e1-38555b490f03", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi perangkat pada sistem jaringan lokal akses radio tetap,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "2d243f9e-4107-43ba-a0cc-08d6510b15f0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 824060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami <PERSON>p Web design/Perancangan Web", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:36", "updated_at": "2019-11-27 00:28:36", "deleted_at": null, "last_sync": "2019-11-27 00:28:36"}, {"kompetensi_dasar_id": "2d24488b-d53f-4883-8acf-66e146ec2117", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 820010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mendemontrasikan mesin konversi energi", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:26", "updated_at": "2022-10-18 06:44:01", "deleted_at": null, "last_sync": "2019-06-15 16:04:26"}, {"kompetensi_dasar_id": "2d24ed19-1e55-4adc-b821-9aa2d5c80513", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:21", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:01:21"}, {"kompetensi_dasar_id": "2d2576b4-d436-48cf-b669-8c219cff4338", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 805010500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis keb<PERSON> data spasial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:47", "updated_at": "2019-11-27 00:27:47", "deleted_at": null, "last_sync": "2019-11-27 00:27:47"}, {"kompetensi_dasar_id": "2d278a49-9992-4595-899b-6c118eeff2b1", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "  <PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>, <PERSON><PERSON><PERSON>, per<PERSON><PERSON><PERSON><PERSON> dan penutup dalam teks negosiasi berkaitan dengan bidang pekerjaan secara lisan atau tulis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:05", "updated_at": "2019-11-27 00:30:05", "deleted_at": null, "last_sync": "2019-11-27 00:30:05"}, {"kompetensi_dasar_id": "2d282103-eb3e-4935-9721-130cb0e98d81", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 822090110, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>  dasar-dasar casis dan pemindah tenaga", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:19", "updated_at": "2022-11-10 19:57:31", "deleted_at": null, "last_sync": "2019-06-15 15:03:19"}, {"kompetensi_dasar_id": "2d297deb-c1a8-42d0-a169-0612712b094d", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:59:57", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:59:57"}, {"kompetensi_dasar_id": "2d298996-71e6-496c-aea8-a0dc32b11bb7", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan proses fisika dan proses kimia dalam industri makanan,  lemak dan  minyak", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:32:01", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:32:01"}, {"kompetensi_dasar_id": "2d2ab742-9de6-48e8-ae52-73b34d961144", "id_kompetensi": "4.31", "kompetensi_id": 2, "mata_pelajaran_id": 804111100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan trouble shoot kegagalan komponen system robotik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:44", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:33"}, {"kompetensi_dasar_id": "2d2ae6c3-b356-43f7-8518-637a0bcd98b6", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:42", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:29:42"}, {"kompetensi_dasar_id": "2d2b23ec-7220-468b-aab0-8a56a2f11adb", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan  teknik pemesinan gerinda datar  untuk berbagai jeni<PERSON> p<PERSON>", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:04", "updated_at": "2022-10-19 23:19:26", "deleted_at": null, "last_sync": "2019-06-15 16:03:04"}, {"kompetensi_dasar_id": "2d2ba4dc-5a4c-4493-a092-7b1afde73a08", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 804100800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis instalasi penerangan Jalan Umum", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:59", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-11-27 00:27:59"}, {"kompetensi_dasar_id": "2d2bc8ce-84b0-4df9-8f6a-3885f1410e51", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 401251103, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengklasifikasikan jenis al bai'", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2019-06-15 15:07:16", "deleted_at": null, "last_sync": "2019-06-15 15:07:16"}, {"kompetensi_dasar_id": "2d2c2698-0333-43d3-b297-530463373870", "id_kompetensi": "4.17", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>h strategi yang efektif dan menyajikan model mate<PERSON><PERSON> dalam memecahkan masalah nyata tentang fungsi naik dan fungsi turun.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:49", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:27:49"}, {"kompetensi_dasar_id": "2d2d0638-2412-4496-8a13-9df3d1b26bb8", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 805010500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan kebutuhan data non spasial", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "2d2d0eac-b00c-429f-ac70-51ffdca72d8c", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 804132200, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Memahami system koordinat pada gambar CAD 3D", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:33:58", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 15:33:58"}, {"kompetensi_dasar_id": "2d2daa42-dadd-4651-992d-b6e007452c35", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 807010200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkalibrasi alat ukur mekanik", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:51", "updated_at": "2019-11-27 00:29:51", "deleted_at": null, "last_sync": "2019-11-27 00:29:51"}, {"kompetensi_dasar_id": "2d30a805-913c-4b30-a133-22535d7d8412", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 826160100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>un<PERSON><PERSON><PERSON> jenis cacat kayu jati", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2022-11-10 19:57:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "2d314e11-50f6-4bd9-80e0-444c753461a7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 802021200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami langkah-langkah penguatan host (host hardening).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:06:59", "updated_at": "2019-06-15 15:06:59", "deleted_at": null, "last_sync": "2019-06-15 15:06:59"}, {"kompetensi_dasar_id": "2d33a210-b0e4-4c44-956e-82973c13cbea", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 802031800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat storyboard se<PERSON><PERSON> dengan naskah film", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:25", "updated_at": "2022-11-10 19:57:19", "deleted_at": null, "last_sync": "2019-06-15 15:07:26"}, {"kompetensi_dasar_id": "2d347c67-0ed2-43ff-837c-27ad0499b9c2", "id_kompetensi": "4.13", "kompetensi_id": 2, "mata_pelajaran_id": 804210100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan sifat fisik batuan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:04", "updated_at": "2019-06-15 14:50:04", "deleted_at": null, "last_sync": "2019-06-15 14:50:04"}, {"kompetensi_dasar_id": "2d36c1da-2779-4fbf-8663-6470f7a33afc", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 820070100, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami prinsip kerja sistem pelumasan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:03:17"}, {"kompetensi_dasar_id": "2d371f40-d01b-4150-a7fe-0078c86f5a97", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat  studi komparasi tentang ide dan gagasan perubahan demokrasi Indonesia 1950 sampai dengan era Reformasi dalam bentuk laporan tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:32", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:32"}, {"kompetensi_dasar_id": "2d381814-1890-40a1-a8f1-1acaf024bd06", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100015010, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan hakekat ajaran Wariga dalam kehidupan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:02", "updated_at": "2019-11-27 00:30:02", "deleted_at": null, "last_sync": "2019-11-27 00:30:02"}, {"kompetensi_dasar_id": "2d397ba8-9b08-4eeb-a300-780214d30d68", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menganalisis teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen  baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:03:15"}, {"kompetensi_dasar_id": "2d398d9f-518e-4641-96b5-743a92b80131", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 825030400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Memahami teknik per<PERSON>uan khusus", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:51", "updated_at": "2019-11-27 00:27:51", "deleted_at": null, "last_sync": "2019-11-27 00:27:51"}, {"kompetensi_dasar_id": "2d3ad347-cfe6-47d0-a80a-89401f5f318a", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan sistem plambing pipa tembaga untuk air panas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:49:57", "updated_at": "2019-06-15 14:49:57", "deleted_at": null, "last_sync": "2019-06-15 14:49:57"}, {"kompetensi_dasar_id": "2d3b6736-708b-468a-8851-35c6a17fcf23", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks lisan dan tulis untuk mengucapkan dan merespon ungkapan meminta per<PERSON> (extended), dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebahasaan yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:09", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:09"}, {"kompetensi_dasar_id": "2d3bda88-3904-46d6-b1e9-47289abe1534", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 825050300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "   Merumuskan prosedur analisis kemurnian fisik benih tanaman hortikultura/ perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:47", "updated_at": "2019-11-27 00:27:47", "deleted_at": null, "last_sync": "2019-11-27 00:27:47"}, {"kompetensi_dasar_id": "2d3bdcd6-530d-49dc-acc2-637ef19a7a18", "id_kompetensi": "3.8", "kompetensi_id": 1, "mata_pelajaran_id": 808050500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan inspection and testing pada Springs", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:53", "updated_at": "2019-11-27 00:29:53", "deleted_at": null, "last_sync": "2019-11-27 00:29:53"}, {"kompetensi_dasar_id": "2d3ca95d-2dfa-4153-8b45-c20fbbfdc9c8", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 818010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan hasil analisis peta geomorfologi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "2d3d99ec-8619-4d4c-9aff-80d7260a1873", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 401130900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan proses fisika dan proses kimia pada industri gas dan bahan bakar gas.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:51", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:28:51"}, {"kompetensi_dasar_id": "2d3dc4a6-9741-4e36-bb91-483d5fe43fd3", "id_kompetensi": "4.19", "kompetensi_id": 2, "mata_pelajaran_id": 818010100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola proyek pemetaan geologi detil", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:30", "updated_at": "2019-11-27 00:27:30", "deleted_at": null, "last_sync": "2019-11-27 00:27:30"}, {"kompetensi_dasar_id": "2d3e325e-19a9-4fac-bb2a-5f6f6818af34", "id_kompetensi": "4.9", "kompetensi_id": 2, "mata_pelajaran_id": 821060100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan Gambar Midship Section", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:17", "updated_at": "2019-11-27 00:30:17", "deleted_at": null, "last_sync": "2019-11-27 00:30:17"}, {"kompetensi_dasar_id": "2d3ff99a-8476-4907-9473-ecede3f75845", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 807020610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan kodefikasi aircraft hydraulic & pneumatic systems", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:27", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:59:23"}, {"kompetensi_dasar_id": "2d404f77-e6b8-4a7e-8653-815fa1069214", "id_kompetensi": "4.79", "kompetensi_id": 2, "mata_pelajaran_id": 821170700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mencontohkan penerapan tranformator daya frekuensi rendah dan frekuensi tinggi.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:11", "updated_at": "2019-06-15 15:06:52", "deleted_at": null, "last_sync": "2019-06-15 15:06:52"}, {"kompetensi_dasar_id": "2d40b8f7-9091-4ce2-9502-3df03a7d1011", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 843061700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "menerapkan teknik medium (technique of medium) dalam pengembangan koreografi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:31", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:15"}, {"kompetensi_dasar_id": "2d41235a-ca7f-4462-b310-acb651868cb9", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 825020700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> pem<PERSON><PERSON>an kesuburan tanah tanaman herbal/atsiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:05", "updated_at": "2019-06-15 15:07:05", "deleted_at": null, "last_sync": "2019-06-15 15:07:05"}, {"kompetensi_dasar_id": "2d4128e5-4b00-41f5-ae92-87ce2ddb1c7a", "id_kompetensi": "4.76", "kompetensi_id": 2, "mata_pelajaran_id": 821170900, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memadukan al<PERSON><PERSON>an pada gerbang logika digital.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:12", "updated_at": "2019-06-15 15:06:53", "deleted_at": null, "last_sync": "2019-06-15 15:06:53"}, {"kompetensi_dasar_id": "2d4187bb-c059-4a4b-b620-2338dc60fe47", "id_kompetensi": "3.21", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep turunan dengan menggunakan konteks matematik atau konteks lain dan menerapkannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:04:53", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:04:53"}, {"kompetensi_dasar_id": "2d41fa96-4b9e-4a47-9319-b3b5e6865ce7", "id_kompetensi": "4.1.2", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON><PERSON><PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159 dengan lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:35", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:35"}, {"kompetensi_dasar_id": "2d43ecac-2433-4477-b09e-06fcd03799bc", "id_kompetensi": "4.16", "kompetensi_id": 2, "mata_pelajaran_id": 803090200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan rancang bangun rangkaian gerbang logika untuk aplikasi aritmatik sistem digital", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:55", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:55"}, {"kompetensi_dasar_id": "2d44142d-b84d-4e2f-a3c3-c82e4982bd68", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menggunakan berbagai prinsip konsep dan sifat diagonal ruang, diagonal bidang, dan bidang diagonal dalam bangun ruang dimensi tiga serta menerapkannya dalam memecahkan.", "kompetensi_dasar_alias": "<p><span>Menyelesaikan&nbsp; &nbsp;masalah&nbsp;\r\n&nbsp;yang&nbsp;</span>berkaitan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;dengan&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;peluang kejadian\r\nmajemuk (peluang, kejadian-kejadian saling bebas, saling lepas, dan kejadian\r\nbersyarat)</p>", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:38", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:38"}, {"kompetensi_dasar_id": "2d443398-6fbf-408d-aa10-4fc095537458", "id_kompetensi": "2.3.1", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami musik kreasi berdasarkan jenis dan fungsi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:28:21", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:28:21"}, {"kompetensi_dasar_id": "2d44b0d6-d7e5-4846-84f1-818468b40e5e", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan konsep dan sifat turunan fungsi terkaitdan menerapkannya untuk menentukan titik stasioner (titik maximum, titik minimum dan titik belok).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:03", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:31:03"}, {"kompetensi_dasar_id": "2d461f85-f460-41c4-b842-02488bc84b96", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi  rangkaian aktivitas gerak ritmik (masing-masing tiga hingga lima gerak).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:07:45", "updated_at": "2022-10-19 23:19:18", "deleted_at": null, "last_sync": "2019-06-15 16:07:45"}, {"kompetensi_dasar_id": "2d47c13f-67f7-4d09-91fd-a3e42f573853", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks interaksi interpersonal lisan dan tulis sederhana yang melibatkan tindakan memberikan ucapan selamat bersayap (extended), dan responnya dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:04:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:04:17"}, {"kompetensi_dasar_id": "2d47eeef-9aad-4d54-851a-ae732fae642e", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami desain produk dan pengemasan pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan konsep berkarya dan peluang usaha dengan pendekatan budaya setempat dan lainnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:11", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:05:11"}, {"kompetensi_dasar_id": "2d48c16d-019b-46ba-ad6c-acf1ff08fe63", "id_kompetensi": "4.25", "kompetensi_id": 2, "mata_pelajaran_id": 821170100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelas pipa baja dengan proses las SMAW pada posisi vertical up (5G pipa tidak diputar)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:20", "updated_at": "2019-11-27 00:29:20", "deleted_at": null, "last_sync": "2019-11-27 00:29:20"}, {"kompetensi_dasar_id": "2d4a0767-c872-440f-a859-602d0eaa3ee0", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 802040800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>ak<PERSON><PERSON> dasar riset pendengar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:34", "updated_at": "2019-11-27 00:28:34", "deleted_at": null, "last_sync": "2019-11-27 00:28:34"}, {"kompetensi_dasar_id": "2d4a3405-807d-4bd5-a547-c41b10bc537e", "id_kompetensi": "4.8", "kompetensi_id": 2, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Men<PERSON><PERSON><PERSON> hasil evaluasi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan berdasarkan kriteria keberhasilan usaha", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:46", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:32:46"}, {"kompetensi_dasar_id": "2d4c0152-8a3c-4d07-81a1-67e6cc7b9b18", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman kepada hari akhir.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:44", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:03:44"}, {"kompetensi_dasar_id": "2d4c7e20-241d-482a-9cb6-9c635bdd4445", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 803090100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instal bahasa program", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:57", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-11-27 00:27:57"}, {"kompetensi_dasar_id": "2d4f3f73-f6e6-481f-9041-f7f789bae8d5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 843062100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan desain karya penataan karawitan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:31", "updated_at": "2019-11-27 00:28:31", "deleted_at": null, "last_sync": "2019-11-27 00:28:31"}, {"kompetensi_dasar_id": "2d4f5cdc-abd7-47ae-a0aa-5e1f0f28287c", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 804050450, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> konsep pela<PERSON>an dan pengawasan pekerjaan konstruksi gedung", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:14", "updated_at": "2019-11-27 00:30:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:14"}, {"kompetensi_dasar_id": "2d500c7d-0f0d-424e-a6eb-5b68260e49f7", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 802040800, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan prosedur k<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, dan keamanan kerja", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:10", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 14:58:10"}, {"kompetensi_dasar_id": "2d513022-2587-4480-b7c7-0078c2df9ffc", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 800030200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis IPC produk sediaan obat cair", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:23", "updated_at": "2019-11-27 00:30:23", "deleted_at": null, "last_sync": "2019-11-27 00:30:23"}, {"kompetensi_dasar_id": "2d515367-a04e-433a-a537-5fd0d02427e1", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 804110800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pemograman  mesin frais CNC", "kompetensi_dasar_alias": "Dapat menerapkan teknik pemograman  mesin frais CNC", "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:08:50", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 16:08:50"}, {"kompetensi_dasar_id": "2d518eb4-4be8-408c-80f3-b2043a216bdf", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 500010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperagakan dan mengevaluasi  rangkaian aktivitas gerak ritmik (masing-masing tiga hingga lima gerak).", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:06:59", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:06:59"}, {"kompetensi_dasar_id": "2d5194bf-b4cc-4902-bf58-6d526969594a", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 820030600, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menginspeksi sistem  penyedia bahan bakar gas", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:22", "updated_at": "2019-06-15 15:03:22", "deleted_at": null, "last_sync": "2019-06-15 15:03:22"}, {"kompetensi_dasar_id": "2d52622f-1b35-47d9-99e9-964132a772e9", "id_kompetensi": "3.4", "kompetensi_id": 2, "mata_pelajaran_id": 401130610, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik pembuatan unsur-unsur golongan utama pada skala laboratorium dan skala industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:28:04", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:04"}, {"kompetensi_dasar_id": "2d52c9e6-d405-40b7-bbad-00ef2b8ebe7b", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengoperasikan alat khromatografi lapis tipis (TLC)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:45", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 16:00:45"}, {"kompetensi_dasar_id": "2d543c82-76f7-4978-9e05-1ad102c0aad5", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401130500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> analisis bahan tambahan makanan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:03:08", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:03:08"}, {"kompetensi_dasar_id": "2d54457f-15d7-411d-bf86-92af7d16cf7e", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengan<PERSON><PERSON> bahasa inggris maritim dalam komunikasi di atas kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:37", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:37"}, {"kompetensi_dasar_id": "2d54903e-cd4c-49f3-8045-ee6d003ded1d", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis strategi yang diterapkan negara Indonesia dalam menyelesaikan ancaman terhadap negara dalam memperkokoh persatuan dengan bingkai Bhinneka Tunggal Ika", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:17", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:17"}, {"kompetensi_dasar_id": "2d550121-7049-4c63-a5ba-03e81fdc48a9", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 807022700, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> (spur gear)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:26", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:59:22"}, {"kompetensi_dasar_id": "2d55ef37-cbf1-4d62-b68e-88b1efbc215d", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 839060100, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakuka<PERSON> pen<PERSON>n akhir tenun polos dengan tehnik makrame", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:54", "updated_at": "2019-11-27 00:28:54", "deleted_at": null, "last_sync": "2019-11-27 00:28:54"}, {"kompetensi_dasar_id": "2d56769f-fba0-475b-b08b-2b248bd5240f", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 816050100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkanproses pen<PERSON><PERSON> akhir (making up)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:31", "updated_at": "2019-11-27 00:29:31", "deleted_at": null, "last_sync": "2019-11-27 00:29:31"}, {"kompetensi_dasar_id": "2d57e7e7-ca23-44de-9eb3-6ae1a5c7ce5d", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 826090100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memvalidasi  pengendalikan Vegetasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:11", "updated_at": "2022-11-10 19:57:36", "deleted_at": null, "last_sync": "2019-06-15 15:07:11"}, {"kompetensi_dasar_id": "2d593778-1e1b-4983-8585-5802c5c5127d", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pela<PERSON><PERSON>an pasal-pasal yang mengatur tentang keuangan, BPK, dan kek<PERSON><PERSON><PERSON> kehakiman", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:28", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:57:28"}, {"kompetensi_dasar_id": "2d59633f-34e3-485b-8d8d-56ed00e0bad2", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 401141500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melak<PERSON><PERSON> pema<PERSON>an dan salesmanship", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:30:21", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-11-27 00:30:21"}, {"kompetensi_dasar_id": "2d597c87-9823-4c81-b8af-644f80793c5e", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 802032700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi gambar kontur menggunakan perangkat lunak", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:49", "updated_at": "2019-11-27 00:27:49", "deleted_at": null, "last_sync": "2019-11-27 00:27:49"}, {"kompetensi_dasar_id": "2d5a4953-8451-4325-9057-0629269e4e84", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 803050400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan macam-macam komponen semikonduktor empat lapis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:13", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:06:55"}, {"kompetensi_dasar_id": "2d5a8087-cc5b-4b82-a6aa-354412bcc96d", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 843050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis genre musik jazz", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:01", "updated_at": "2019-11-27 00:28:01", "deleted_at": null, "last_sync": "2019-11-27 00:28:01"}, {"kompetensi_dasar_id": "2d5b4b9a-8a21-41db-b97a-c718239a1a80", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 825060100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep kualitas produk pada pemanenan ternak.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:10", "updated_at": "2022-11-10 19:57:34", "deleted_at": null, "last_sync": "2019-06-15 15:06:49"}, {"kompetensi_dasar_id": "2d5b5ab8-ba78-4d76-bb23-e3a491ec0dd0", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerap<PERSON> pengujian dan pengukuran peralatan elektronika daya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:47", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:47"}, {"kompetensi_dasar_id": "2d5b9be7-735b-447d-a449-65f0021be097", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 821010100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> macam-macam perleng<PERSON>pan kapal", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 14:50:07", "updated_at": "2019-06-15 14:50:07", "deleted_at": null, "last_sync": "2019-06-15 14:50:07"}, {"kompetensi_dasar_id": "2d5d2814-6c60-4a75-82ec-5f746d50b32c", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 826080100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengolah data survey dan pemantauan tumbuhan sesuai metode", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:20", "updated_at": "2019-11-27 00:28:20", "deleted_at": null, "last_sync": "2019-11-27 00:28:20"}, {"kompetensi_dasar_id": "2d5d329e-ed80-4899-a161-7d568d29eadc", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 824060400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> jenis-jenis media rekam kamera video", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:11", "updated_at": "2022-11-10 19:57:33", "deleted_at": null, "last_sync": "2019-06-15 14:58:11"}, {"kompetensi_dasar_id": "2d5f7187-abf1-4326-9b57-672178fd0c72", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 843030100, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat karya pengembangan desain  produk yang berdampak pada aspek kreatif.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:24", "updated_at": "2019-06-15 15:07:24", "deleted_at": null, "last_sync": "2019-06-15 15:07:24"}, {"kompetensi_dasar_id": "2d5f97be-b2b2-41b3-bdd5-94f27082903c", "id_kompetensi": "3.27", "kompetensi_id": 1, "mata_pelajaran_id": 814032100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis macam-macam peralatan alat bantu berbasis IT dalam administrasi freight forwading", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:08", "updated_at": "2019-11-27 00:30:08", "deleted_at": null, "last_sync": "2019-11-27 00:30:08"}, {"kompetensi_dasar_id": "2d6277f1-07fb-41fb-b16b-d81cc4c265bf", "id_kompetensi": "3.6", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami sumber daya yang dibutuhkan dalam mendukung proses produksi usaha pengolahan dari bahan nabati dan hewani menjadi produk kesehatan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:14", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 16:05:14"}, {"kompetensi_dasar_id": "2d637b6a-c932-461a-ac83-f07945332020", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800020410, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemeriksaan penyakit sistem otot dan rangka (muskuloskletal) berdasarkan manifestasi klinisnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:29", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "2d63c6a1-dca5-469f-96ca-520b90775caa", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 803060800, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisa pemrosesan  sinyal  digital  dan  penggunaan  perangkat lunak untuk perencanaan sistem radio", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:56", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:12:30"}, {"kompetensi_dasar_id": "2d63d4b1-cf7e-456b-be6b-9a7b4da03b0e", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 820100100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memperbaiki sistem pelumasan engine", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:19", "updated_at": "2019-11-27 00:27:19", "deleted_at": null, "last_sync": "2019-11-27 00:27:19"}, {"kompetensi_dasar_id": "2d643f3c-1bd2-41a2-9963-63b02cecaa4e", "id_kompetensi": "4.29", "kompetensi_id": 2, "mata_pelajaran_id": 817030100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Melakukan perawatan sumur migas", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:29", "updated_at": "2019-11-27 00:27:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:29"}, {"kompetensi_dasar_id": "2d647b28-b522-4ac9-81c4-d60a00c76194", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebahasaan untuk menyatakan dan menanyakan tentang pengandaian diikuti oleh perintah/saran, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:42", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:33:42"}, {"kompetensi_dasar_id": "2d652a57-0086-4beb-acb4-46beb509078e", "id_kompetensi": "4.7", "kompetensi_id": 2, "mata_pelajaran_id": 401251150, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> laporan laba/rugi, per<PERSON><PERSON> modal, neraca dan arus kas untuk perusa<PERSON>an jasa", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 14:58:24"}, {"kompetensi_dasar_id": "2d673156-dccc-4e7f-8b61-26212b0551b4", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> struktur dan kaidah teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:45", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:58:45"}, {"kompetensi_dasar_id": "2d67b973-5e2c-4a2c-a263-7eaad5a67893", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 805010500, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalis cara membuat laporan transformasi koordinat.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:49:58", "updated_at": "2022-11-10 19:57:25", "deleted_at": null, "last_sync": "2019-06-15 14:49:58"}, {"kompetensi_dasar_id": "2d68eeac-0470-4c4c-8264-5547e0984cf5", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 803080900, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi plant proses produksi dan manufaktur di industri", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:37", "updated_at": "2019-11-27 00:28:37", "deleted_at": null, "last_sync": "2019-11-27 00:28:37"}, {"kompetensi_dasar_id": "2d6a0337-87d7-4222-b027-3bcf87d9abb8", "id_kompetensi": "4.22", "kompetensi_id": 2, "mata_pelajaran_id": 807022100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat receiver <PERSON><PERSON><PERSON><PERSON>", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:43", "updated_at": "2019-11-27 00:27:43", "deleted_at": null, "last_sync": "2019-11-27 00:27:43"}, {"kompetensi_dasar_id": "2d6a2877-3761-4c69-bbed-0a5112c92246", "id_kompetensi": "2.3.4", "kompetensi_id": 2, "mata_pelajaran_id": 843020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis pergelaran musik berdasarkan hasil kreasi  sendiri", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:57", "updated_at": "2022-11-10 19:57:43", "deleted_at": null, "last_sync": "2019-06-15 15:31:57"}, {"kompetensi_dasar_id": "2d6b08b9-9617-45c3-85fa-c5ae2b12f7e3", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:13", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:13"}, {"kompetensi_dasar_id": "2d6ce46c-c3e6-498e-a524-d577bb389fb2", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 820040400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Merawat berkala sistem <PERSON>n", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2017, "created_at": "2019-06-15 15:03:16", "updated_at": "2019-06-15 15:03:16", "deleted_at": null, "last_sync": "2019-06-15 15:03:16"}, {"kompetensi_dasar_id": "2d6d40dc-7159-4233-8c85-d0bf5ab72a41", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prinsip kerja per-alatan dalam teknik penimbangan dengan neraca analitis", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:01:19", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 16:01:19"}, {"kompetensi_dasar_id": "2d6e5f27-b35e-4e36-91ae-a05265f0367b", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 819020100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan teknik kerja alat khromatografi kolom", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:57", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:57"}, {"kompetensi_dasar_id": "2d70ff33-6630-4948-9f66-d203596010fd", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan metode pencarian kerus<PERSON>n, perbaikan & perawatan macam-macam pesawat penerima Televisi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:11", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:11"}, {"kompetensi_dasar_id": "2d71b08c-e8db-4d62-8654-8908bc3a60a4", "id_kompetensi": "3.7", "kompetensi_id": 1, "mata_pelajaran_id": 825060600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Menerapkan penanganan kesehatan ternak ruminansia perah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:00", "updated_at": "2019-11-27 00:28:00", "deleted_at": null, "last_sync": "2019-11-27 00:28:00"}, {"kompetensi_dasar_id": "2d71da68-76fd-4ae2-84ce-8c4195fac3c2", "id_kompetensi": "4.15", "kompetensi_id": 2, "mata_pelajaran_id": 827190120, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> pakan pada pemeliharaan benih komoditas perikanan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:21", "updated_at": "2019-11-27 00:29:21", "deleted_at": null, "last_sync": "2019-11-27 00:29:21"}, {"kompetensi_dasar_id": "2d73c06f-e85f-4550-ae53-c50476d32442", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401130600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan organisasi laboratorium dan uraian tugasnya", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:55", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:26:55"}, {"kompetensi_dasar_id": "2d73e10b-af6e-4a0b-81a0-ad35c9494892", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 401231000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan penelitian sederhana tentang kehidupan politik dan ekonomi bangsa Indonesia pada masa Orde Baru dan menyajikannya dalam bentuk laporan  tertulis.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:32:40", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 15:32:40"}, {"kompetensi_dasar_id": "2d74d97e-0ad0-460e-bfc8-bedb1a1dde8b", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": 825250600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengelola kualitas air pada pemeliharaan larva ikan hias", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:27", "updated_at": "2019-11-27 00:29:27", "deleted_at": null, "last_sync": "2019-11-27 00:29:27"}, {"kompetensi_dasar_id": "2d750d30-2157-43a6-9027-276ff9eca8f3", "id_kompetensi": "4.20", "kompetensi_id": 2, "mata_pelajaran_id": 805010600, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menentukan input data tabular", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:13", "updated_at": "2019-11-27 00:28:13", "deleted_at": null, "last_sync": "2019-11-27 00:28:13"}, {"kompetensi_dasar_id": "2d75ae45-d154-4c09-8c94-6b4daee4d6cf", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:29:32", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:29:32"}, {"kompetensi_dasar_id": "2d75ede5-0338-492e-bdbf-deb349028754", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 827150200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengkaji kesesuaian parameter kualitas airdengan standar kualitas air yang dipersyaratkan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:48", "updated_at": "2022-11-10 19:57:38", "deleted_at": null, "last_sync": "2019-06-15 14:52:48"}, {"kompetensi_dasar_id": "2d778677-75c9-4417-bbaa-5bbd30de434a", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 401131800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresentasikan proses pembukaan dan penumpukan kain, penyambungan dan pemeriksaan kain grey (pile up, sewing dan Inspecting)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:12", "updated_at": "2019-11-27 00:28:12", "deleted_at": null, "last_sync": "2019-11-27 00:28:12"}, {"kompetensi_dasar_id": "2d77c97d-5db6-4192-a741-8e034d738b34", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401251105, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan mekanisme pembiayaan akad murabahah dengan urbun", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:16", "updated_at": "2022-11-10 19:57:15", "deleted_at": null, "last_sync": "2019-06-15 15:07:17"}, {"kompetensi_dasar_id": "2d785e37-7ad1-43fe-be7c-c21f8d3d552b", "id_kompetensi": "4.1.1", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Membaca Q.S<PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159, se<PERSON><PERSON> dengan ka<PERSON>h tajwid dan makh<PERSON> huruf.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:07", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:00:07"}, {"kompetensi_dasar_id": "2d7a1a99-90da-4f71-b6a6-31b97723f391", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 803041100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON>   cara pengukuran trafik,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 1, "kurikulum": 2006, "created_at": "2019-06-15 15:07:01", "updated_at": "2019-06-15 15:07:01", "deleted_at": null, "last_sync": "2019-06-15 15:07:01"}, {"kompetensi_dasar_id": "2d7aa891-078e-4b45-a536-d56a7d345fc3", "id_kompetensi": "3.13", "kompetensi_id": 2, "mata_pelajaran_id": 814031200, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami tentang jenis barang-barang khusus dan berbahaya", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:40", "updated_at": "2019-11-27 00:29:40", "deleted_at": null, "last_sync": "2019-11-27 00:29:40"}, {"kompetensi_dasar_id": "2d7ae311-bf19-4c92-b45c-ab3f3d82df5a", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827390800, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Explain maintenance and repair methods", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:58:09", "updated_at": "2022-11-10 19:57:39", "deleted_at": null, "last_sync": "2019-06-15 14:58:09"}, {"kompetensi_dasar_id": "2d7bc613-cf43-41ad-a6ac-dc7674d70851", "id_kompetensi": "4.11", "kompetensi_id": 2, "mata_pelajaran_id": 815010900, "kelas_10": 0, "kelas_11": 0, "kelas_12": 0, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaks<PERSON><PERSON> pen<PERSON>an sliver Roving", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:03:25", "updated_at": "2022-11-10 19:57:27", "deleted_at": null, "last_sync": "2019-06-15 15:03:25"}, {"kompetensi_dasar_id": "2d7e3fba-e506-478b-bc4f-c36710247362", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 803060600, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan pengujian dan pengukuran peralatan elektronika konsumen", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:26:48", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:26:48"}, {"kompetensi_dasar_id": "2d7fd262-0adc-452c-ade9-56f245424303", "id_kompetensi": "4.27", "kompetensi_id": 2, "mata_pelajaran_id": 825050700, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menunjukkan teknik pengendalian gulma di lahan produksi benih tanaman hortikultura/perkebunan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "2d80bbdb-7635-4eab-86ea-2c27a90653d0", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> istilah-<PERSON><PERSON><PERSON> Inggris teknis di kapal.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:27:02", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:27:02"}, {"kompetensi_dasar_id": "2d82fb3a-b4cc-4d1f-ab75-9b59ec66f2b4", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": *********, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis daftar akun untuk per<PERSON>an jasa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:37", "updated_at": "2022-11-10 19:57:14", "deleted_at": null, "last_sync": "2019-06-15 14:58:25"}, {"kompetensi_dasar_id": "2d837ea8-1f5d-4eb2-92c0-9345065f4925", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 840020120, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur pembentukan teknik lempeng (slab)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:59", "updated_at": "2019-11-27 00:28:59", "deleted_at": null, "last_sync": "2019-11-27 00:28:59"}, {"kompetensi_dasar_id": "2d83ab0e-4b96-4484-a2ad-4161a34df793", "id_kompetensi": "3.16", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendeskripsikan dan menerapkan aturan/rumus peluang dalam memprediksi terjadinya suatu kejadian dunia nyata serta menjelaskan alasan- alasannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:58:11", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:58:11"}, {"kompetensi_dasar_id": "2d8475ff-fcd3-40d2-89d3-5eaa5650d0a4", "id_kompetensi": "4.23", "kompetensi_id": 2, "mata_pelajaran_id": *********, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "    Melakukan entry transaksi pembelian bahan baku, bahan pembantu, aset tetap dan pencatatan transaksi pembayaran utang pada perusahaan manufaktur", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:58", "updated_at": "2019-11-27 00:29:58", "deleted_at": null, "last_sync": "2019-11-27 00:29:58"}, {"kompetensi_dasar_id": "2d84d216-ad1c-4a27-a296-7cfe272d216a", "id_kompetensi": "4.3", "kompetensi_id": 2, "mata_pelajaran_id": 803060100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan instalasi sistem hiburan pertunjukkan siaran langsung ruang terbuka dan tertutup", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:09", "updated_at": "2022-11-10 19:57:20", "deleted_at": null, "last_sync": "2019-06-15 15:57:09"}, {"kompetensi_dasar_id": "2d84f5a7-4247-468d-889c-ef0274d572a0", "id_kompetensi": "4.4", "kompetensi_id": 2, "mata_pelajaran_id": 825210500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON>aks<PERSON><PERSON><PERSON><PERSON> bahan per<PERSON>nan secara organoleptis", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:37", "updated_at": "2019-11-27 00:29:37", "deleted_at": null, "last_sync": "2019-11-27 00:29:37"}, {"kompetensi_dasar_id": "2d85a09e-e487-43ed-ab57-677ec9aa597d", "id_kompetensi": "3.11", "kompetensi_id": 1, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis fungsi sosial, struk<PERSON> teks, dan unsur kebah<PERSON>an dari teks prosedur berbentuk resep, sesuai dengan konteks penggunaannya.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:50", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:30:50"}, {"kompetensi_dasar_id": "2d85a2f9-b7a5-483d-a844-94663c226f12", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 827110330, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> perawatan dan perbaikan mesin", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:08", "updated_at": "2019-11-27 00:29:08", "deleted_at": null, "last_sync": "2019-11-27 00:29:08"}, {"kompetensi_dasar_id": "2d8694e2-5a62-4e2a-bb12-21135e45ecd0", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 802031110, "kelas_10": 1, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Me<PERSON>ami alat dan bahan dalam pembuatan animasi 2D", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:44", "updated_at": "2019-11-27 00:28:44", "deleted_at": null, "last_sync": "2019-11-27 00:28:44"}, {"kompetensi_dasar_id": "2d86f2d6-0487-4edf-a136-bd149b14996b", "id_kompetensi": "3.1", "kompetensi_id": 1, "mata_pelajaran_id": 401000000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan konsep bilangan be<PERSON>, bentuk akar dan logaritma dalam menyelesaikan masalah", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:58", "updated_at": "2022-11-10 19:57:13", "deleted_at": null, "last_sync": "2019-06-15 15:57:58"}, {"kompetensi_dasar_id": "2d8788a3-0487-4879-8c0c-0b57ad2c4395", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 800050420, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis kebutuhan cairan dan elektrolit tubuh", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:28", "updated_at": "2022-11-10 19:57:17", "deleted_at": null, "last_sync": "2019-06-15 14:59:25"}, {"kompetensi_dasar_id": "2d888dc3-008f-4d77-b599-35c9ceafce13", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 804010200, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyajikan elemen utama eksterior sesuai konsep dan gaya eksterior yang telah ditentukan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:30:24", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:30:24"}, {"kompetensi_dasar_id": "2d88a2b3-d001-4b76-ad9f-33df36a64dc6", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 814031500, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Mengelompokan alat bantu berbasis IT dalam administrasi pergudangan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:04", "updated_at": "2019-11-27 00:30:04", "deleted_at": null, "last_sync": "2019-11-27 00:30:04"}, {"kompetensi_dasar_id": "2d88cb50-ebc7-4397-a4b3-799741b60d23", "id_kompetensi": "3.12", "kompetensi_id": 1, "mata_pelajaran_id": 825200100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan pemeriksaan antemortem", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:23", "updated_at": "2019-11-27 00:28:23", "deleted_at": null, "last_sync": "2019-11-27 00:28:23"}, {"kompetensi_dasar_id": "2d892bbf-0c6b-4a3a-9cbd-02af62e83862", "id_kompetensi": "3.17", "kompetensi_id": 1, "mata_pelajaran_id": 803061100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> proses titling dan efek visual", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:52", "updated_at": "2019-11-27 00:28:52", "deleted_at": null, "last_sync": "2019-11-27 00:28:52"}, {"kompetensi_dasar_id": "2d8a69ca-ba22-424c-9a15-3b906d88907c", "id_kompetensi": "3.23", "kompetensi_id": 1, "mata_pelajaran_id": 803060800, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis tabung gambar CRT dan layar gambar (LCD/LED)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:11", "updated_at": "2019-11-27 00:29:11", "deleted_at": null, "last_sync": "2019-11-27 00:29:11"}, {"kompetensi_dasar_id": "2d8aa226-3326-4d21-bbd2-9da5f086633e", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 823040100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Membuat laporan tentang sistem proteksi, switch gear, pembumian dan penangkal petir pada PLTBm", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:27:38", "updated_at": "2019-11-27 00:27:38", "deleted_at": null, "last_sync": "2019-11-27 00:27:38"}, {"kompetensi_dasar_id": "2d8aa6ad-866c-48c9-b0b3-d2e213f1e795", "id_kompetensi": "3.26", "kompetensi_id": 1, "mata_pelajaran_id": 824060500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan komunikasi bahasa verbal dan non verbal penyutradaraan televisi", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:39", "updated_at": "2019-11-27 00:28:39", "deleted_at": null, "last_sync": "2019-11-27 00:28:39"}, {"kompetensi_dasar_id": "2d8b5ca5-55eb-417b-ac59-f091ef41e534", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 401130610, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menerapkan prosedur analisis air tercemar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:54", "updated_at": "2022-10-19 23:19:16", "deleted_at": null, "last_sync": "2019-06-15 15:12:27"}, {"kompetensi_dasar_id": "2d8bedea-b008-408f-b967-b64789b68845", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 200010000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> hasil analisis strategi yang diterapkan negara Indonesia dalam menyelesaikan  ancaman terhadap negara dalam memperkokoh persatuan bangsa.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:05:20", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:05:20"}, {"kompetensi_dasar_id": "2d8c1400-3c8a-4ffc-acef-1931ea2c0e7e", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804050420, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "<PERSON><PERSON><PERSON> spesif<PERSON><PERSON> dan karakteristik beton.", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:03:46", "updated_at": "2022-11-10 19:57:22", "deleted_at": null, "last_sync": "2019-06-15 16:03:46"}, {"kompetensi_dasar_id": "2d8da49d-6e44-4fec-8e86-552d10a7bca0", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 600060000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami pembuatan proposal usaha pengolahan dari bahan nabati dan hewani menjadi makanan khas daerah yang dimodifikasi", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:27:15", "updated_at": "2022-11-10 19:57:16", "deleted_at": null, "last_sync": "2019-06-15 15:27:15"}, {"kompetensi_dasar_id": "2d8dd741-b8b1-4edb-8f32-ee1b9285eaf5", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 820140100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melaksanakan pembinaan dan pengembangan SDM bengkel", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:24", "updated_at": "2019-11-27 00:27:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:24"}, {"kompetensi_dasar_id": "2d8e7de1-c17c-46c1-8753-e8722d88e533", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Mengevaluasi teks prosedur, e<PERSON><PERSON><PERSON><PERSON>, ceramah dan cerpen berdasarkan kaidah-kaidah baik melalui lisan maupun tulisan", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:31:30", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:30"}, {"kompetensi_dasar_id": "2d8f7b4a-d720-464b-862c-5e0a6a0dcae0", "id_kompetensi": "3.5", "kompetensi_id": 1, "mata_pelajaran_id": 804110700, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengidentifikasi mesin gerinda silinder (cylindrical grinding machine)", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:33:37", "updated_at": "2022-11-10 19:57:23", "deleted_at": null, "last_sync": "2019-06-15 15:33:37"}, {"kompetensi_dasar_id": "2d8fb3e2-56fa-4925-847d-c3a6aad48a27", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 804132400, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menerapkan semua alat bantu \r\nyang ada pada mesin bubut, \r\nseperti cekam rahang tiga, cekam \r\nrahang empat, senter, pelat \r\npem<PERSON>, \r\npenyang<PERSON>, eretan \r\nmelintang dan kepala lepas", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 16:06:27", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-06-15 16:06:27"}, {"kompetensi_dasar_id": "2d90f5fe-9193-4dff-a0cd-826c88477afb", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 808040400, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis  komponen Lubrication Systems", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 14:50:00", "updated_at": "2022-11-10 19:57:26", "deleted_at": null, "last_sync": "2019-06-15 14:50:00"}, {"kompetensi_dasar_id": "2d919181-67c0-458c-9b4b-03294e7a437c", "id_kompetensi": "4.18", "kompetensi_id": 2, "mata_pelajaran_id": 803070310, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menjelaskan prinsip kerja rang<PERSON>an pengendali terbuka (open loop) secara digit,", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:59:57", "updated_at": "2022-10-19 23:19:23", "deleted_at": null, "last_sync": "2019-06-15 15:12:31"}, {"kompetensi_dasar_id": "2d9338ca-3a80-4666-ba08-468c5b34f931", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 829111100, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON>i produk roti", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2006, "created_at": "2019-06-15 15:07:21", "updated_at": "2022-11-10 19:57:40", "deleted_at": null, "last_sync": "2019-06-15 15:07:21"}, {"kompetensi_dasar_id": "2d939c7e-a2c8-4de7-b368-e8591fba9dc5", "id_kompetensi": "4.10", "kompetensi_id": 2, "mata_pelajaran_id": 300210000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyusun teks il<PERSON>h fak<PERSON> (factual report), lisan dan tulis, se<PERSON><PERSON>, tentang orang, binata<PERSON>, benda, gejala dan peristiwa alam dan sosial, terkait dengan Mata pelajaran lain di <PERSON>las XII, dengan memperhatikan fungsi sosial, struktur teks, dan unsur kebah<PERSON>an yang benar dan sesuai konteks.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:31:15", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:31:15"}, {"kompetensi_dasar_id": "2d941ee5-8ccd-482d-8a5b-188d6ebf3f2d", "id_kompetensi": "4.1", "kompetensi_id": 2, "mata_pelajaran_id": 820020200, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": "0", "kompetensi_dasar": "Menggunakan macam-macam hand tools", "kompetensi_dasar_alias": "", "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:30:31", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-06-15 15:30:31"}, {"kompetensi_dasar_id": "2d95455a-a6f9-4ed9-851c-14ccedf9d723", "id_kompetensi": "3.10", "kompetensi_id": 1, "mata_pelajaran_id": 827210400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> pemanenan, pengepakan dan transportasi benih siap tebar", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:33", "updated_at": "2019-11-27 00:29:33", "deleted_at": null, "last_sync": "2019-11-27 00:29:33"}, {"kompetensi_dasar_id": "2d958602-011f-47ce-9e50-a42b4bb11ff2", "id_kompetensi": "4.41", "kompetensi_id": 2, "mata_pelajaran_id": 820030500, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan asesmen kinerja unit boiler/ketel uap.", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:17", "updated_at": "2022-11-10 19:57:29", "deleted_at": null, "last_sync": "2019-11-27 00:27:17"}, {"kompetensi_dasar_id": "2d96e917-efea-430a-83c8-98a30f305dcb", "id_kompetensi": "4.14", "kompetensi_id": 2, "mata_pelajaran_id": 826120100, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Melakukan perencanaan reklamasi hutan", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:36", "updated_at": "2019-11-27 00:29:36", "deleted_at": null, "last_sync": "2019-11-27 00:29:36"}, {"kompetensi_dasar_id": "2d97de59-0c5a-49b4-abb7-b93f5e4cacf7", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON> makna iman k<PERSON>ada <PERSON> dan <PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:01:49", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 16:01:49"}, {"kompetensi_dasar_id": "2d99ff0b-a846-4148-b3b7-df24adaab027", "id_kompetensi": "3.4", "kompetensi_id": 1, "mata_pelajaran_id": 830040000, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "       Menganalisis tangan dan kaki", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:30:32", "updated_at": "2019-11-27 00:30:32", "deleted_at": null, "last_sync": "2019-11-27 00:30:32"}, {"kompetensi_dasar_id": "2d9b11d4-470b-4af7-bb60-70cceac24840", "id_kompetensi": "3.2", "kompetensi_id": 1, "mata_pelajaran_id": 816010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Memahami macam-macam cacat kain", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:53", "updated_at": "2019-11-27 00:28:53", "deleted_at": null, "last_sync": "2019-11-27 00:28:53"}, {"kompetensi_dasar_id": "2d9b3a25-6500-49f2-bc38-2433b00a4831", "id_kompetensi": "3.9", "kompetensi_id": 1, "mata_pelajaran_id": 805010400, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengevaluasi metode pengukuran <PERSON> (Ground Truth)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:48", "updated_at": "2019-11-27 00:27:48", "deleted_at": null, "last_sync": "2019-11-27 00:27:48"}, {"kompetensi_dasar_id": "2d9b3f8b-c43e-4294-9b05-a7796e7b8e3b", "id_kompetensi": "4.2", "kompetensi_id": 2, "mata_pelajaran_id": 100012050, "kelas_10": 1, "kelas_11": 0, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>-<PERSON><PERSON>:  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dan <PERSON>  da<PERSON> kehid<PERSON>.", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 15:28:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 15:28:14"}, {"kompetensi_dasar_id": "2d9c3d59-719c-43ae-a072-78ca9e1e635c", "id_kompetensi": "4.5", "kompetensi_id": 2, "mata_pelajaran_id": 300110000, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mengonversi teks cerita sejarah, berita, i<PERSON>n, editorial/opini, dan cerita fiksi dalam novel ke dalam bentuk yang lain sesuai dengan struktur dan kaidah teks baik secara lisan maupun tulisan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 16:00:14", "updated_at": "2022-11-10 19:57:12", "deleted_at": null, "last_sync": "2019-06-15 16:00:14"}, {"kompetensi_dasar_id": "2d9cca4b-7ddb-4e3e-ae4c-15bbeb5814cc", "id_kompetensi": "3.15", "kompetensi_id": 1, "mata_pelajaran_id": 843061110, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menafsirkan teknik pengembangan pola tabuhan instrumen iringan", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:52:32", "updated_at": "2022-11-10 19:57:44", "deleted_at": null, "last_sync": "2019-06-15 14:58:18"}, {"kompetensi_dasar_id": "2d9da926-5a02-4518-8f89-4c52b9ea36e8", "id_kompetensi": "3.14", "kompetensi_id": 1, "mata_pelajaran_id": 804210200, "kelas_10": 1, "kelas_11": null, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "   Memahami Sembur Buatan (artificial Lift)", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 0, "kurikulum": 2017, "created_at": "2019-11-27 00:27:28", "updated_at": "2022-11-10 19:57:24", "deleted_at": null, "last_sync": "2019-11-27 00:27:28"}, {"kompetensi_dasar_id": "2d9f0fbd-20c7-475b-bc7b-cf557fa6c908", "id_kompetensi": "4.1.2", "kompetensi_id": 2, "mata_pelajaran_id": 100011070, "kelas_10": 0, "kelas_11": 0, "kelas_12": 1, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Mendemonstras<PERSON><PERSON><PERSON><PERSON> (3): 190-191 dan <PERSON><PERSON><PERSON><PERSON> (3): 159 dengan lancar", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2013, "created_at": "2019-06-15 15:57:20", "updated_at": "2022-11-10 19:57:11", "deleted_at": null, "last_sync": "2019-06-15 15:57:20"}, {"kompetensi_dasar_id": "2da03ccf-b00f-48fe-b5d1-690c68839802", "id_kompetensi": "4.12", "kompetensi_id": 2, "mata_pelajaran_id": 804060300, "kelas_10": null, "kelas_11": 1, "kelas_12": 1, "kelas_13": 1, "id_kompetensi_nas": null, "kompetensi_dasar": "Mempresenta-sikan stabilisasi dan perbaikan tanah", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:28:22", "updated_at": "2019-11-27 00:28:22", "deleted_at": null, "last_sync": "2019-11-27 00:28:22"}, {"kompetensi_dasar_id": "2da059d0-56ff-4e71-aa24-74a71efb7b21", "id_kompetensi": "3.3", "kompetensi_id": 1, "mata_pelajaran_id": 803090300, "kelas_10": 0, "kelas_11": 1, "kelas_12": 0, "kelas_13": 0, "id_kompetensi_nas": null, "kompetensi_dasar": "Menganalisis bagian - bagian instrumentasi elektroteknik medik", "kompetensi_dasar_alias": null, "user_id": null, "aktif": 0, "kurikulum": 2017, "created_at": "2019-06-15 14:50:46", "updated_at": "2022-11-10 19:57:21", "deleted_at": null, "last_sync": "2019-06-15 15:12:35"}, {"kompetensi_dasar_id": "2da07282-9b08-4d2f-9e1d-4366602d9a6e", "id_kompetensi": "4.6", "kompetensi_id": 2, "mata_pelajaran_id": 807020610, "kelas_10": null, "kelas_11": 1, "kelas_12": null, "kelas_13": null, "id_kompetensi_nas": null, "kompetensi_dasar": "Menyimpulkan hasil pembentukan pipa Aircraft Hydraulic instalation", "kompetensi_dasar_alias": null, "user_id": "b80dfb08-af25-45fe-a5fc-badbef7a2a75", "aktif": 1, "kurikulum": 2017, "created_at": "2019-11-27 00:29:49", "updated_at": "2019-11-27 00:29:49", "deleted_at": null, "last_sync": "2019-11-27 00:29:49"}]